// Package utils ..
package utils

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"reflect"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/customer/identity/auth"
	cutils "gitlab.myteksi.net/dbmy/core-banking/common/utils"

	customermasterExternal "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/customermaster"
	customermasterExternalDTO "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/customermaster/dto"

	"github.com/google/uuid"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
	productMaster "gitlab.myteksi.net/dbmy/core-banking/product-master/api"
	customerMasterDBMY "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

var (
	// TimeZoneLocation ...
	TimeZoneLocation = &time.Location{}
)

// CustomerData ...
type CustomerData struct {
	PreferredName string
	ID            string
}

// GetIdempotencyKeyFromHeader ...
func GetIdempotencyKeyFromHeader(ctx context.Context) string {
	return commonCtx.GetIdempotencyKey(ctx)
}

// AddIdempotencyKeyToHeader ...
func AddIdempotencyKeyToHeader(ctx context.Context, idempotencyKey string) context.Context {
	return commonCtx.WithIdempotencyKey(ctx, idempotencyKey)
}

// GetServiceIDFromHeader ...
func GetServiceIDFromHeader(ctx context.Context) string {
	return commonCtx.GetServiceID(ctx)
}

// AddServiceIDToHeader ...
func AddServiceIDToHeader(ctx context.Context, serviceID string) context.Context {
	return commonCtx.WithServiceID(ctx, serviceID)
}

// GetUserIDFromHeader ...
func GetUserIDFromHeader(ctx context.Context) string {
	return commonCtx.GetUserID(ctx)
}

// AddUserIDToHeader ...
func AddUserIDToHeader(ctx context.Context, userID string) context.Context {
	return commonCtx.WithUserID(ctx, userID)
}

// CheckIfAuthorized ...
func CheckIfAuthorized(ctx context.Context, accountID string, store storage.DatabaseStore, c customermasterExternal.ICustomerMasterClient) error {
	// Look Up for CIF Number in header if not present get it from customer master
	customerIdentity, lookUpErr := GetCustomerCurrentIdentity(ctx, constants.EmptyCustomerIdentity, c)
	if lookUpErr != nil {
		return lookUpErr
	}

	// Check Permission for CIF Number
	status, checkErr := CheckPermissionForAccount(ctx, accountID, customerIdentity.CustomerID(), store)
	if checkErr != nil {
		return checkErr
	}

	// Return if Not Allowed
	if status != api.AccountPermission_ALLOWED {
		return apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10),
			apiErr.ErrAccountPermissionForbidden.Message)
	}

	return nil
}

// CheckIfCustomerAuthorized ...
func CheckIfCustomerAuthorized(ctx context.Context, accountID string, store storage.DatabaseStore, c customermasterExternal.ICustomerMasterClient) error {
	dbCustomerAccount, getDBAccErr := store.GetCustomerAccount(ctx, accountID)
	if getDBAccErr != nil {
		slog.FromContext(ctx).Warn(constants.UpdateCASAAccountStatusLogTag, fmt.Sprintf("unable to fetch account-details from database: %s", getDBAccErr.Error()), GetTraceID(ctx))
		if errors.Is(getDBAccErr, data.ErrNoData) {
			return apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrAccountNotFound.Code), 10), apiErr.ErrAccountNotFound.Message)
		}
		return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message)
	}
	_, err := GetCustomerCurrentIdentity(ctx, dbCustomerAccount.CifNumber, c)

	if err != nil {
		return apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10),
			apiErr.ErrAccountPermissionForbidden.Message)
	}

	return nil
}

// GetCustomerByCIFFromCustomerMaster ...
func GetCustomerByCIFFromCustomerMaster(ctx context.Context, c customerMaster.CustomerMaster, cifNumber string) (*CustomerData, error) {
	req := &customerMaster.GetCustomerByCIFNumberRequest{
		CifNumber: cifNumber,
	}
	getCustomerByCIFResponse, err := c.GetCustomerByCIFNumber(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetCustomerByCIFLogTag, fmt.Sprintf("Unable to fetch cif number via customer-master: %s", err.Error()))
		if reflect.DeepEqual(err.Error(), apiErr.ErrCifMappingNotFoundMsg.Error()) {
			return nil, apiErr.BuildErrorResponse(http.StatusNotFound, cutils.SafeIntToString(apiErr.ErrCifMappingNotFound.Code),
				apiErr.ErrCifMappingNotFound.Message)
		}
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrCustomerMasterConnectionFailure.Code), 10),
			apiErr.ErrCustomerMasterConnectionFailure.Message)
	}

	// Unmarshal the JSON into CustomerData struct
	var customerData CustomerData
	err = json.Unmarshal(getCustomerByCIFResponse.Customer.Data, &customerData)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetCustomerByCIFLogTag, fmt.Sprintf("Unable to unmarshal customer data: %s", err.Error()))
		return nil, err
	}

	return &customerData, nil
}

// LookUpCifFromCustomerMaster ...
func LookUpCifFromCustomerMaster(ctx context.Context, c customerMaster.CustomerMaster) (string, error) {
	serviceID := GetServiceIDFromHeader(ctx)
	userID := GetUserIDFromHeader(ctx)

	if serviceID == "" || userID == "" {
		slog.FromContext(ctx).Warn(constants.LookUpCifNumberLogTag, "Unable to fetch serviceID/userID from the headers")
		return "", apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrJWTNotFound.Code), 10),
			apiErr.ErrJWTNotFound.Message)
	}

	req := &customerMaster.LookupCIFNumberRequest{
		ID: userID,
		Target: &customerMaster.TargetGroup{
			ServiceID: serviceID,
		},
	}
	lookUpCifResponse, err := c.LookupCIFNumber(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LookUpCifNumberLogTag, fmt.Sprintf("Unable to fetch cif number via customer-master: %s", err.Error()))
		if reflect.DeepEqual(err.Error(), apiErr.ErrCifMappingNotFoundMsg.Error()) {
			return "", apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrCifMappingNotFound.Code), 10),
				apiErr.ErrCifMappingNotFound.Message)
		}
		return "", apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrCustomerMasterConnectionFailure.Code), 10),
			apiErr.ErrCustomerMasterConnectionFailure.Message)
	}

	slog.FromContext(ctx).Info(constants.LookUpCifNumberLogTag, fmt.Sprintf("CifNumber from customer-master: %s", lookUpCifResponse.CifNumber))
	return lookUpCifResponse.CifNumber, nil
}

// GetCustomerCurrentIdentity returns the customer identity (CIF/BIF) based on the Profile ID in the context.
// If no Profile ID is found, then it uses Grab User ID (Safe ID) to resolve it to a CIF/BIF on a best effort basis.
func GetCustomerCurrentIdentity(ctx context.Context, customerIdentity string, clientDBMY customermasterExternal.ICustomerMasterClient) (*auth.CustomerIdentity, error) {
	profileID := getProfileID(ctx)
	if profileID != "" {
		if customerIdentity != constants.EmptyCustomerIdentity && customerIdentity != profileID {
			return nil, apiErr.BuildErrorResponse(http.StatusNotFound, cutils.SafeIntToString(apiErr.ErrCustomerIdentityMappingNotFound.Code),
				apiErr.ErrCustomerIdentityMappingNotFound.Message)
		}
		identityObj := lo.Ternary(auth.IsBusinessCifNumber(profileID), &auth.CustomerIdentity{BIF: profileID}, &auth.CustomerIdentity{CIF: profileID})
		return identityObj, nil
	}

	// fallback to Grab User ID (Safe ID) based authorization
	slog.FromContext(ctx).Warn(constants.LookUpCustomerIdentityLogTag, "Unable to fetch Profile ID from the context. Falling back to GetBusinessInfo.")
	serviceID := GetServiceIDFromHeader(ctx)
	userID := GetUserIDFromHeader(ctx)

	if serviceID == "" || userID == "" {
		slog.FromContext(ctx).Warn(constants.LookUpCustomerIdentityLogTag, "Unable to fetch serviceID/userID from the headers")
		return nil, apiErr.BuildErrorResponse(http.StatusNotFound, cutils.SafeIntToString(apiErr.ErrJWTNotFound.Code),
			apiErr.ErrJWTNotFound.Message)
	}

	apiRes, err := clientDBMY.GetBusinessInfo(ctx, &customermasterExternalDTO.GetBusinessInfoRequest{
		SafeID: userID,
		Status: customermasterExternalDTO.BusinessRelationshipStatus_ACTIVE,
	})

	if err != nil {
		slog.FromContext(ctx).Warn(constants.LookUpCustomerIdentityLogTag, fmt.Sprintf("Unable to fetch customer identity via customer-master for: %s", customerIdentity))
		if errors.Is(err, apiErr.ErrCustomerIDMappingNotFoundMsg) {
			return nil, apiErr.BuildErrorResponse(http.StatusNotFound, cutils.SafeIntToString(apiErr.ErrCustomerIdentityMappingNotFound.Code),
				apiErr.ErrCifMappingNotFound.Message)
		}
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, cutils.SafeIntToString(apiErr.ErrCustomerMasterConnectionFailure.Code),
			apiErr.ErrCustomerMasterConnectionFailure.Message)
	}

	// CIF/BIF is not available to the caller, we will derive the identity on best effort based on biz profile presence
	if customerIdentity == "" {
		bif := ""
		// TODO: This uses heuristics like recently created business to identify the BIF. The guess can go wrong
		// and hence this logic needs to be removed once Profile ID is fully rolled out.
		if bizProfile, ok := auth.FindLatestBizProfile(apiRes.BusinessRelationships); ok {
			bif = bizProfile.BIF
		}
		return &auth.CustomerIdentity{
			SafeID: userID,
			CIF:    apiRes.CIF,
			BIF:    bif,
		}, nil
	}

	for _, business := range apiRes.BusinessRelationships {
		if customerIdentity == business.BIF {
			return &auth.CustomerIdentity{
				SafeID: userID,
				BIF:    business.BIF,
				CIF:    apiRes.CIF,
			}, nil
		}
	}
	if customerIdentity == apiRes.CIF {
		return &auth.CustomerIdentity{
			SafeID: userID,
			BIF:    "",
			CIF:    customerIdentity,
		}, nil
	}
	return nil, apiErr.BuildErrorResponse(http.StatusNotFound, cutils.SafeIntToString(apiErr.ErrCustomerIdentityMappingNotFound.Code),
		apiErr.ErrCustomerIdentityMappingNotFound.Message)
}

// CheckPermissionForAccount ...
func CheckPermissionForAccount(ctx context.Context, accountID string, cifNumber string, store storage.DatabaseStore) (api.AccountPermission, error) {
	account, err := store.GetCustomerAccount(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CheckPermissionsLogTag, fmt.Sprintf("Unable to fetch account-details from database: %s", err.Error()), GetTraceID(ctx))
		if reflect.DeepEqual(err, data.ErrNoData) {
			return api.AccountPermission_FORBIDDEN, nil
		}
		return "", apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message)
	}
	if account.CifNumber == cifNumber {
		return api.AccountPermission_ALLOWED, nil
	}
	return api.AccountPermission_FORBIDDEN, nil
}

// GetCASAAccountBalances ...
// Since pocket is account, savingPocketIds are not in use, only will pass in empty value
func GetCASAAccountBalances(accBalances []depositsCore.AccountBalance, savingPocketIDs map[string]bool) ([]api.AccountBalance, *api.Money, *api.Money) {
	var balances []api.AccountBalance
	var mainAccountBalance, savingsPocketBalance int64
	var currencyCode string
	if accBalances == nil {
		return nil, nil, nil
	}

	for _, accountBalance := range accBalances {
		balance := api.AccountBalance{
			AccountAddress: accountBalance.AccountAddress,
			Phase:          accountBalance.Phase,
			Amount: &api.Money{
				CurrencyCode: accountBalance.Amount.CurrencyCode,
				Val:          accountBalance.Amount.Val,
			},
		}
		balances = append(balances, balance)
		if accountBalance.Phase == "POSTING_PHASE_PENDING_OUTGOING" || accountBalance.Phase == "POSTING_PHASE_COMMITTED" {
			if accountBalance.AccountAddress == constants.AccountAddressDefault {
				mainAccountBalance += accountBalance.Amount.Val
			} else if savingPocketIDs[accountBalance.AccountAddress] {
				// since savingPocketIds always empty, this logic block won't be used
				savingsPocketBalance += accountBalance.Amount.Val
			}
		}
		currencyCode = accountBalance.Amount.CurrencyCode
	}
	return balances, &api.Money{
			CurrencyCode: currencyCode,
			Val:          mainAccountBalance,
		}, &api.Money{
			CurrencyCode: currencyCode,
			Val:          savingsPocketBalance,
		}
}

// GetBusinessName ...
func GetBusinessName(ctx context.Context, c customermasterExternal.ICustomerMasterClient, bifNumber string) (string, error) {
	business, err := c.GetBusinessesByIdentifier(ctx, &customermasterExternalDTO.GetBusinessesByIdentifierRequest{
		Identifier:     bifNumber,
		IdentifierType: customermasterExternalDTO.GetBusinessesByIdentifierRequest_Type_BIF,
		BasicInfoOnly:  false,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetBusinessNameLogTag, fmt.Sprintf("unable to fetch business data for %s : %s", bifNumber, err.Error()))
		return "", err
	}
	if len(business.Businesses) == 0 {
		slog.FromContext(ctx).Warn(constants.GetBusinessNameLogTag, fmt.Sprintf("no businesses found for %s", bifNumber))
		return "", apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrBusinessNotFound.Code), 10), apiErr.ErrBusinessNotFound.Message)
	}
	return business.Businesses[0].Name, nil
}

// GetCASAOnHoldAmount calculate the on hold amount for CASA accounts
func GetCASAOnHoldAmount(accBalances []depositsCore.AccountBalance) ([]api.AccountBalance, *api.Money) {
	var (
		accountOnHoldAmount int64
		currencyCode        string
		accountBalances     = make([]api.AccountBalance, len(accBalances))
	)

	if accBalances == nil {
		return nil, nil
	}
	currencyCode = accBalances[0].Amount.CurrencyCode

	for _, accountBalance := range accBalances {
		balance := api.AccountBalance{
			AccountAddress: accountBalance.AccountAddress,
			Phase:          accountBalance.Phase,
			Amount: &api.Money{
				CurrencyCode: accountBalance.Amount.CurrencyCode,
				Val:          accountBalance.Amount.Val,
			},
		}
		accountBalances = append(accountBalances, balance)
		if accountBalance.Phase == "POSTING_PHASE_PENDING_OUTGOING" {
			if accountBalance.AccountAddress == constants.AccountAddressDefault {
				accountOnHoldAmount += accountBalance.Amount.Val
			}
		}
	}
	// TM return onHoldAmount as negative value, however for easier to understand it is converted into positive value for frontend purpose
	accountOnHoldAmount = -accountOnHoldAmount

	return accountBalances, &api.Money{
		CurrencyCode: currencyCode,
		Val:          accountOnHoldAmount,
	}
}

// GetAvailableBalance ...
func GetAvailableBalance(accBalances []depositsCore.AccountBalance) (*api.Money, error) {
	var mainAccountBalance int64
	var currencyCode string

	if len(accBalances) == 0 {
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrEmptyBalancesForAccount.Code), 10), apiErr.ErrEmptyBalancesForAccount.Message)
	}

	for _, accountBalance := range accBalances {
		if accountBalance.Phase == constants.BalancePhasePendingOutgoing || accountBalance.Phase == constants.BalancePhaseCommitted {
			if accountBalance.AccountAddress == constants.AccountAddressDefault {
				mainAccountBalance += accountBalance.Amount.Val
			}
		}
		currencyCode = accountBalance.Amount.CurrencyCode
	}

	return &api.Money{
		CurrencyCode: currencyCode,
		Val:          mainAccountBalance,
	}, nil
}

// BuildBalanceSummary ...
func BuildBalanceSummary(accBalances []depositsCore.AccountBalance, defaultCurrencyCode string) *api.Balance {
	var (
		availableBalance int64
		ledgerBalance    int64
		earmarkedBalance int64
		currencyCode     string
	)

	for _, accountBalance := range accBalances {
		if accountBalance.AccountAddress == constants.AccountAddressDefault {
			availableBalance += accountBalance.Amount.Val
			if accountBalance.Phase == constants.BalancePhasePendingOutgoing {
				earmarkedBalance += accountBalance.Amount.Val
			}
			if accountBalance.Phase == constants.BalancePhaseCommitted {
				ledgerBalance += accountBalance.Amount.Val
			}
		}
		currencyCode = accountBalance.Amount.CurrencyCode
	}

	if len(currencyCode) == 0 {
		currencyCode = defaultCurrencyCode
	}

	return &api.Balance{
		AvailableBalance: &api.Money{
			CurrencyCode: currencyCode,
			Val:          availableBalance,
		}, LedgerBalance: &api.Money{
			CurrencyCode: currencyCode,
			Val:          ledgerBalance,
		}, EarmarkedBalance: &api.Money{
			CurrencyCode: currencyCode,
			Val:          -earmarkedBalance,
		},
	}
}

// GetCustomerNADFlag ...
func GetCustomerNADFlag(ctx context.Context, customerClient customerMasterDBMY.CustomerMaster) (*customerMasterDBMY.Flag, error) {
	customerSafeID := GetUserIDFromHeader(ctx)
	flagReq := &customerMasterDBMY.GetCustomerFlagRequest{
		ID: customerSafeID,
	}
	flagResponse, err := customerClient.GetCustomerFlag(ctx, flagReq)
	if err != nil {
		slog.FromContext(ctx).Error(constants.GetCustomerNADFlagLogTag, fmt.Sprintf("unable to get customer flag with SAFE ID %s: %s", customerSafeID, err.Error()), GetTraceID(ctx))
		return nil, err
	}

	var nadFlag customerMasterDBMY.Flag
	for _, flag := range flagResponse.CustomerFlag.Flag {
		if flag.FlagCode == customerMasterDBMY.FlagCode_DBMY_NAD_STATUS {
			nadFlag = flag
			break
		}
	}
	return &nadFlag, nil
}

// GetTraceID ...
func GetTraceID(ctx context.Context) slog.Tag {
	span := commonCtx.GetSpan(ctx)
	return slog.TraceID(cutils.SafeIntToString(span.Context().TraceID()))
}

// GetXTraceID ...
func GetXTraceID(ctx context.Context) slog.Tag {
	span := commonCtx.GetSpan(ctx)
	id := cutils.SafeIntToString(span.Context().TraceID())
	return slog.CustomTag("x_trace_id", id)
}

// ToJSON converts struct to JSON string, ie "{"key":"value"}"
func ToJSON(i interface{}) string {
	s, _ := json.Marshal(i)
	return string(s)
}

// GetClientIdentityFromHeader ...
func GetClientIdentityFromHeader(ctx context.Context) string {
	return commonCtx.GetClientIdentity(ctx)
}

// UpdateRequestQueryLog ...
func UpdateRequestQueryLog(ctx context.Context, response interface{}, dbRequestLog *storage.RequestLog, store storage.DatabaseStore) error {
	r, err := json.Marshal(response)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.CreateGoalLogTag, fmt.Sprintf("Error in marshalling the create savings pocket response: %s", err.Error()), GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrObjectMarshal.Code), 10), apiErr.ErrObjectMarshal.Message)
	}

	requestLog, reqLogErr := store.GetRequestLog(ctx, dbRequestLog.RequestID)
	if reqLogErr != nil {
		return reqLogErr
	}
	responseCode := storage.RequestLogResponseStatusOK
	requestLog.ResponseCode = &responseCode
	requestLog.ResponseBody = sql.NullString{String: string(r), Valid: true}
	requestLog.Status = storage.RequestLogStatusCompleted
	requestLog.LockedAt = sql.NullTime{Valid: false}

	return store.UpdateRequestLog(ctx, requestLog)
}

// DateNowLocal return current Date in current timezone
func DateNowLocal() time.Time {
	return GetLocalDate(time.Now())
}

// LocalDate ...
var LocalDate = DateNowLocal

// GetLocalDate ...
func GetLocalDate(now time.Time) time.Time {
	currDateTimeLocal := now.In(TimeZoneLocation)
	dateLocal, _ := time.Parse(constants.DateTimeFormat, currDateTimeLocal.Format(constants.DateTimeFormat))
	return dateLocal
}

// CalculateTrackingDetail ...
func CalculateTrackingDetail(currBalance, targetAmount int64, targetDate, goalCreationDate time.Time) *dto.TrackingInfo {
	trackingInfo := &dto.TrackingInfo{
		GoalStatus:        api.GoalStatus_Status_COMPLETED,
		OnTrackPercent:    100,
		OffTrackPercent:   0,
		OutstandingAmount: 0,
	}
	onTrackPercent := cutils.MustConvertToUint32((currBalance * 100) / targetAmount)
	// currBalance >= targetAmount
	if currBalance >= targetAmount {
		return trackingInfo
	}
	// ONGOING Scenarios :
	currDate := LocalDate()
	if currDate.After(targetDate) {
		trackingInfo.OnTrackPercent = onTrackPercent
		trackingInfo.OffTrackPercent = 100 - onTrackPercent
		trackingInfo.OutstandingAmount = targetAmount - currBalance
		trackingInfo.GoalStatus = api.GoalStatus_Status_OFFTRACK
		return trackingInfo
	}
	// next cycle date
	nextCycleDate := goalCreationDate.AddDate(0, 1, 0)
	periodsElapsed := int64(1)
	for currDate.After(nextCycleDate) {
		nextCycleDate = nextCycleDate.AddDate(0, 1, 0)
		periodsElapsed++
	}
	numOfPeriods := targetDate.Sub(goalCreationDate).Hours() / float64(24*30)
	expectedBalAtEndOfPeriod := float64(0)
	if nextCycleDate.Before(targetDate) {
		// current cycle is not last cycle
		expectedBalAtEndOfPeriod = (float64(targetAmount) / numOfPeriods) * float64(periodsElapsed)
	} else {
		// current cycle is last cycle
		expectedBalAtEndOfPeriod = float64(targetAmount)
	}

	if float64(currBalance) >= math.Floor(expectedBalAtEndOfPeriod) {
		// Ontrack, : x %, offtrack : 0%
		trackingInfo.OnTrackPercent = onTrackPercent
		trackingInfo.OffTrackPercent = 0
		trackingInfo.OutstandingAmount = 0
		trackingInfo.GoalStatus = api.GoalStatus_Status_ONTRACK
		return trackingInfo
	}
	/* Offtrack scenario:
	offtrack % = expectedBalAtEndOfPeriod/targetAmount * 100 - x
	OutstandingAmount = expectedBalAtEndOfPeriod - currBalance */
	trackingInfo.OnTrackPercent = onTrackPercent

	// Note: Multiplication operations should be commutative/associative by nature.
	// However, due to data type constraints, this is not the case here.
	// TODO: Address the progress accuracy issue (see ticket #RGDEP-505).
	trackingInfo.OffTrackPercent = cutils.MustConvertToUint32((expectedBalAtEndOfPeriod - float64(currBalance)) * 100 / float64(targetAmount))
	trackingInfo.OutstandingAmount = int64(expectedBalAtEndOfPeriod) - currBalance
	trackingInfo.GoalStatus = api.GoalStatus_Status_OFFTRACK

	if currBalance == 0 {
		trackingInfo.GoalStatus = api.GoalStatus_Status_EMPTY
	}
	return trackingInfo
}

// LookUpPocketTemplateIDFromProductMaster ...
func LookUpPocketTemplateIDFromProductMaster(ctx context.Context, templateID string, p productMaster.ProductMaster) error {
	req := &productMaster.GetPocketTemplateRequest{
		Id: templateID,
	}

	_, err := p.GetPocketTemplate(ctx, req)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LookUpPocketTemplateLogTag, fmt.Sprintf("Unable to fetch pocket templates via product-master: %s", err.Error()))
		if err == data.ErrNoData {
			return apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrPocketTemplateIDNotFound.Code), 10),
				apiErr.ErrPocketTemplateIDNotFound.Message)
		}
		return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrProductMasterConnectionFailure.Code), 10),
			apiErr.ErrProductMasterConnectionFailure.Message)
	}
	return err
}

// AuthorizeAndValidate ... checks if it's the account of the user and checks if it's pocket and not closed
func AuthorizeAndValidate(ctx context.Context, accountID string, store storage.DatabaseStore, c customermasterExternal.ICustomerMasterClient) error {
	// Look Up for CIF Number from header if not present get from customer-master
	customerIdentity, lookUpErr := GetCustomerCurrentIdentity(ctx, constants.EmptyCustomerIdentity, c)
	if lookUpErr != nil {
		return lookUpErr
	}

	// Check Permission for CIF Number
	account, err := store.GetCustomerAccount(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.AuthorizeAndValidateLogTag, fmt.Sprintf("Unable to fetch account-details from database: %s", err.Error()), GetTraceID(ctx))
		if reflect.DeepEqual(err, data.ErrNoData) {
			return apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10),
				apiErr.ErrAccountPermissionForbidden.Message)
		}
		return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message)
	}
	if account.CifNumber != customerIdentity.CustomerID() {
		return apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10),
			apiErr.ErrAccountPermissionForbidden.Message)
	}

	// Account is legit now, check if it's a pocket and is active
	if !account.ParentAccountID.Valid {
		slog.FromContext(ctx).Warn(constants.AuthorizeAndValidateLogTag, "Invalid pocket", GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidPocket.Code), 10), apiErr.ErrInvalidPocket.Message)
	}
	if account.CurrentStatus == api.AccountStatus_CLOSED {
		slog.FromContext(ctx).Warn(constants.AuthorizeAndValidateLogTag, "Pocket is closed", GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrAccountInactive.Code), 10), apiErr.ErrAccountInactive.Message)
	}
	return nil
}

// AuthorizeAndValidateCustomer checks if it's the account of the user, be if retail or biz scope, and checks if it's pocket and not closed
func AuthorizeAndValidateCustomer(ctx context.Context, accountID string, store storage.DatabaseStore, clientDBMY customermasterExternal.ICustomerMasterClient) error {
	dbCustomerAccount, getDBAccErr := store.GetCustomerAccount(ctx, accountID)
	if getDBAccErr != nil {
		slog.FromContext(ctx).Warn(constants.AuthorizeAndValidateLogTag, fmt.Sprintf("unable to fetch account-details from database: %s", getDBAccErr.Error()), GetTraceID(ctx))
		if errors.Is(getDBAccErr, data.ErrNoData) {
			return apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountNotFound.Code), 10), apiErr.ErrAccountNotFound.Message)
		}
		return apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message)
	}

	_, err := GetCustomerCurrentIdentity(ctx, dbCustomerAccount.CifNumber, clientDBMY)
	if err != nil {
		return apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10),
			apiErr.ErrAccountPermissionForbidden.Message)
	}

	// Account is legit now, check if it's a pocket and is active
	if !dbCustomerAccount.ParentAccountID.Valid {
		slog.FromContext(ctx).Warn(constants.AuthorizeAndValidateLogTag, "Invalid pocket", GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrInvalidPocket.Code), 10), apiErr.ErrInvalidPocket.Message)
	}
	if dbCustomerAccount.CurrentStatus == api.AccountStatus_CLOSED {
		slog.FromContext(ctx).Warn(constants.AuthorizeAndValidateLogTag, "Pocket is closed", GetTraceID(ctx))
		return apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrAccountInactive.Code), 10), apiErr.ErrAccountInactive.Message)
	}
	return nil
}

// GetImageDetailsFromHermes ...
func GetImageDetailsFromHermes(ctx context.Context, tag string, imageIDs []string, h hermes.Hermes) (*hermes.GetDocumentsResponse, error) {
	resp, hermesErr := h.GetDocuments(ctx, &hermes.GetDocumentsRequest{
		DocumentIDs: imageIDs,
	})
	if hermesErr != nil {
		slog.FromContext(ctx).Warn(tag, fmt.Sprintf("unable to fetch image details from hermes: %s", hermesErr.Error()), GetTraceID(ctx))
		return nil, hermesErr
	}
	return resp, nil
}

// GetUTCOffset ...
func GetUTCOffset(timezoneLocation string) int {
	var offset int
	switch timezoneLocation {
	case "WIB":
		offset = 7 * 60 * 60
	case "SGT":
		offset = 8 * 60 * 60
	case "MYT":
		offset = 8 * 60 * 60
	default:
		offset = 0
	}
	return offset
}

// NewUUID ...
func NewUUID() string {
	return uuid.New().String()
}

// GetProfileID returns the profile ID from the context if present in it else returns it from CustomerMaster
func getProfileID(ctx context.Context) string {
	slog.FromContext(ctx).Info(constants.GetActiveProfileIDLogTag, "Getting profileID from the headers")
	if profileObj, ok := getActiveProfile(ctx); ok && profileObj.ProfileID != "" {
		return profileObj.ProfileID
	}
	// profile ID is empty in the header
	slog.FromContext(ctx).Warn(constants.GetActiveProfileIDLogTag, "Unable to fetch profileID from the headers")
	return ""
}

// GetActiveProfile retrieves the active profile object from the context.
func getActiveProfile(ctx context.Context) (activeProfile.ActiveProfile, bool) {
	activeProfileObj, ok := ctx.Value(activeProfile.CtxActiveProfileKey).(activeProfile.ActiveProfile)
	if !ok {
		return activeProfile.ActiveProfile{}, false
	}
	return activeProfileObj, true
}
