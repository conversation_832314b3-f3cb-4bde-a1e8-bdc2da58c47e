package utils

import (
	"database/sql"
	"reflect"
	"testing"
	"time"

	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"

	"github.com/stretchr/testify/assert"
	goalCore "gitlab.myteksi.net/dakota/core-banking/goal-core/api"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
)

func TestMapToTMStatus(t *testing.T) {
	type args struct {
		status api.AccountStatus
	}
	tests := []struct {
		name string
		args args
		want depositsCore.AccountStatus
	}{
		{
			"TM status for active",
			args{
				api.AccountStatus_ACTIVE,
			},
			depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
		},
		{
			"TM status for dormant",
			args{
				api.AccountStatus_DORMANT,
			},
			depositsCore.AccountStatus_ACCOUNT_STATUS_DORMANT,
		},
		{
			"TM status for closed",
			args{
				api.AccountStatus_CLOSED,
			},
			depositsCore.AccountStatus_ACCOUNT_STATUS_CLOSED,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, MapToTMStatus(tt.args.status), "MapToTMStatus(%v)", tt.args.status)
		})
	}
}

func TestGetValidTransitionsForAccountStatus(t *testing.T) {
	type args struct {
		status api.AccountStatus
	}
	tests := []struct {
		name string
		args args
		want []api.AccountStatus
	}{
		{
			"valid transition  for account pending activation status",
			args{
				api.AccountStatus_PENDING_ACTIVATION,
			},
			[]api.AccountStatus{api.AccountStatus_ACTIVE, api.AccountStatus_CLOSED},
		},
		{
			"valid transition  for account active status",
			args{
				api.AccountStatus_ACTIVE,
			},
			[]api.AccountStatus{api.AccountStatus_DORMANT, api.AccountStatus_CLOSED},
		},
		{
			"valid transition for account dormant status",
			args{
				api.AccountStatus_DORMANT,
			},
			[]api.AccountStatus{api.AccountStatus_ACTIVE, api.AccountStatus_CLOSED},
		},
		{
			"valid transition for account closed status",
			args{
				api.AccountStatus_CLOSED,
			},
			[]api.AccountStatus{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetValidTransitionsForAccountStatus(tt.args.status), "GetValidTransitionsForAccountStatus(%v)", tt.args.status)
		})
	}
}

func TestMapToGoalCoreStatus(t *testing.T) {
	type args struct {
		status goalCore.GoalStatus
	}
	tests := []struct {
		name string
		args args
		want api.GoalStatus_Status
	}{
		{
			"Goal core status for completed",
			args{
				goalCore.GoalStatus_COMPLETED,
			},
			api.GoalStatus_Status_COMPLETED,
		},
		{
			"Goal core status for Empty",
			args{
				goalCore.GoalStatus_EMPTY,
			},
			api.GoalStatus_Status_EMPTY,
		},
		{
			"Goal core status for closed",
			args{
				goalCore.GoalStatus_CLOSED,
			},
			api.GoalStatus_Status_CLOSED,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, MapToGoalCoreStatus(tt.args.status), "MapToGoalCoreStatus(%v)", tt.args.status)
		})
	}
}

func TestMapSavingsPocketObjFromDeposits(t *testing.T) {
	type args struct {
		savingsPocket *depositsCore.SavingsPocket
	}
	id := "454"
	accountID := "01344"
	tests := []struct {
		name string
		args args
		want api.SavingsPocket
	}{
		{
			"Testing MapSavingsPocketObjFromDeposits",
			args{
				savingsPocket: &depositsCore.SavingsPocket{
					Id:        id,
					AccountID: accountID,
				},
			},
			api.SavingsPocket{
				Id:        id,
				AccountID: accountID,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, MapSavingsPocketObjFromDeposits(tt.args.savingsPocket), "MapSavingsPocketObjFromDeposits(%v)", tt.args.savingsPocket)
		})
	}
}

func TestMapCASAAccountObjFromDeposits(t *testing.T) {
	type args struct {
		account *depositsCore.CASAAccount
	}
	opening := time.Now()
	closing := time.Now().Add(time.Hour * 48)
	tests := []struct {
		name string
		args args
		want api.CASAAccount
	}{
		{
			"Test for MapCASAAccountObjFromDeposits",
			args{
				account: &depositsCore.CASAAccount{
					Id:                  "id",
					ProductID:           "productID",
					ProductVersionID:    "1.0",
					ProductVariantID:    "v",
					PermittedCurrencies: []string{"IDR"},
					OpeningTimestamp:    opening,
					ClosingTimestamp:    closing,
				},
			},
			api.CASAAccount{
				Id:                  "id",
				ProductID:           "productID",
				ProductVersionID:    "1.0",
				ProductVariantID:    "v",
				PermittedCurrencies: []string{"IDR"},
				OpeningTimestamp:    opening,
				ClosingTimestamp:    closing,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, MapCASAAccountObjFromDeposits(tt.args.account), "MapCASAAccountObjFromDeposits(%v)", tt.args.account)
		})
	}
}

func TestMapPendingActionToAPI(t *testing.T) {
	scenarios := []struct {
		desc          string
		pendingAction storage.PendingAction
		expected      api.AccountPendingAction
	}{
		{
			desc: "Test for MapPendingActionToAPI",
			pendingAction: storage.PendingAction{
				PublicID: "4B95DF2F-B021-40F9-AF53-7DA5727BF2E1",
				Name:     storage.PendingActionFirstDeposit,
				Status:   storage.PendingActionStatusPending,
			},
			expected: api.AccountPendingAction{
				ID:     "4B95DF2F-B021-40F9-AF53-7DA5727BF2E1",
				Name:   storage.PendingActionFirstDeposit,
				Status: string(storage.PendingActionStatusPending),
			},
		},
	}

	for _, scenario := range scenarios {
		s := scenario

		t.Run(s.desc, func(t *testing.T) {
			res := MapPendingActionToAPI(&s.pendingAction)
			assert.Equal(t, s.expected, res)
		})
	}
}

func TestMapAccount(t *testing.T) {
	type scenario struct {
		desc       string
		dcAccount  *depositsCore.CASAAccount
		dbAccount  *storage.CustomerAccount
		assertFunc func(t *testing.T, s interface{}, actual api.Account)
	}
	scenarios := []scenario{
		{
			desc:      "should map db account ID to ID",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{AccountID: "3407E00C-D0D5-493C-89E0-03502256A3B6"},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.AccountID, actual.Id)
			},
		},
		{
			desc: "should map deposits core account permitted currencies to permitted currencies",
			dcAccount: &depositsCore.CASAAccount{
				PermittedCurrencies: []string{"MYR"},
			},
			dbAccount: &storage.CustomerAccount{},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dcAccount.PermittedCurrencies, actual.PermittedCurrencies)
			},
		},
		{
			desc: "should map deposits core account product parameters to product parameters",
			dcAccount: &depositsCore.CASAAccount{
				InstanceParams: map[string]string{"key": "value"},
			},
			dbAccount: &storage.CustomerAccount{},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				assert.Equal(t, map[string]string{"applicableHoldcodes": "[]"}, actual.ProductSpecificParameters)
			},
		},
		{
			desc:      "should map db account cif number to cif number",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{CifNumber: "cif-number"},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.CifNumber, actual.CifNumber)
			},
		},
		{
			desc:      "should map db account current status to status",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{CurrentStatus: api.AccountStatus_ACTIVE},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.CurrentStatus, actual.Status)
			},
		},
		{
			desc:      "should map db account parent account id to parent account id",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{ParentAccountID: sql.NullString{String: "parent-account-id", Valid: true}},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.ParentAccountID.String, actual.ParentAccountID)
			},
		},
		{
			desc:      "should map db account product id to product id",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{ProductID: "product-id"},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.ProductID, actual.ProductID)
			},
		},
		{
			desc:      "should map db account product variant id to product variant id",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{ProductVariantID: "product-variant-id"},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.ProductVariantID, actual.ProductVariantID)
			},
		},
		{
			desc:      "should map db account opening timestamp to opening timestamp",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{AccountOpeningTimestamp: time.Date(2021, 1, 1, 0, 0, 0, 0, TimeZoneLocation)},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.AccountOpeningTimestamp, actual.OpeningTimestamp)
			},
		},
		{
			desc:      "should map db account name to name",
			dcAccount: &depositsCore.CASAAccount{},
			dbAccount: &storage.CustomerAccount{Name: sql.NullString{
				String: "account name",
				Valid:  true,
			}},
			assertFunc: func(t *testing.T, s interface{}, actual api.Account) {
				expected, _ := s.(scenario)
				assert.Equal(t, expected.dbAccount.Name.String, actual.Name)
			},
		},
	}

	for _, item := range scenarios {
		s := item
		t.Run(s.desc, func(t *testing.T) {
			res := MapAccount(s.dcAccount, s.dbAccount)
			s.assertFunc(t, s, res)
		})
	}
}

func TestStructToMap(t *testing.T) {
	type testStruct struct {
		ExportedField   string `json:"exported_field"`
		OmitEmptyField  string `json:"omit_field,omitempty"`
		IgnoredField    string `json:"-"`
		unexportedField string
		NoTagField      string
		CustomNameField int     `json:"custom"`
		PointerField    *string `json:"pointer"`
	}

	tests := []struct {
		name     string
		input    interface{}
		expected map[string]any
	}{
		{
			name: "basic struct with various fields",
			input: testStruct{
				ExportedField:   "value1",
				OmitEmptyField:  "value2",
				IgnoredField:    "ignored",
				unexportedField: "unexported",
				NoTagField:      "no tag",
				CustomNameField: 42,
				PointerField:    nil,
			},
			expected: map[string]any{
				"exported_field": "value1",
				"omit_field":     "value2",
				"custom":         42,
				"pointer":        (*string)(nil),
			},
		},
		{
			name: "pointer to struct",
			input: &testStruct{
				ExportedField:   "pointer test",
				CustomNameField: 100,
			},
			expected: map[string]any{
				"exported_field": "pointer test",
				"omit_field":     "",
				"custom":         100,
				"pointer":        (*string)(nil),
			},
		},
		{
			name:     "non-struct input",
			input:    "not a struct",
			expected: map[string]any{},
		},
		{
			name:     "nil input",
			input:    nil,
			expected: map[string]any{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StructToMap(tt.input)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("StructToMap() = %v, want %v", result, tt.expected)
			}
		})
	}
}
