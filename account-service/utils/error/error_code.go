package error

import (
	"errors"
	"strconv"
)

var (
	// ErrCifMappingNotFoundMsg To signify that cif number mapping not found
	ErrCifMappingNotFoundMsg = errors.New("CIF_MAPPING_NOT_FOUND:cif number not found")
	// ErrCustomerIDMappingNotFoundMsg To signify that customer id number mapping not found
	ErrCustomerIDMappingNotFoundMsg = errors.New("CUSTOMERID_MAPPING_NOT_FOUND:customer id number not found")
	// ErrIdempotentRequestStartedButIncomplete indicates a idempotent request being started but only partially complete
	ErrIdempotentRequestStartedButIncomplete = errors.New("IDEMPOTENT_REQUEST_STARTED_BUT_INCOMPLETE:request already started but could not be complete due to timeout or other reasons")
)

const (
	// ClientCloseConnHTTPCode ...
	ClientCloseConnHTTPCode = 499
)

// CustomError ...
type CustomError struct {
	Code    int
	Message string
}

// ErrorCode ...
type ErrorCode string

// CodeString returns custom error code as string
func (e CustomError) CodeString() string {
	return strconv.FormatInt(int64(e.Code), 10)
}

var (

	// ErrUnknown ...
	ErrUnknown = CustomError{
		Code:    1200,
		Message: "Unknown Error",
	}

	// ErrMissingAccountID ...
	ErrMissingAccountID = CustomError{
		Code:    1201,
		Message: "Missing account id",
	}

	// ErrMissingSafeID ...
	ErrMissingSafeID = CustomError{
		Code:    1202,
		Message: "Missing Safe ID",
	}

	// ErrMissingCifNumber ...
	ErrMissingCifNumber = CustomError{
		Code:    1202,
		Message: "Missing cif number",
	}

	// ErrMissingIdempotencyKey ...
	ErrMissingIdempotencyKey = CustomError{
		Code:    1203,
		Message: "Missing idempotency key",
	}

	// ErrMissingProductVariantCode ...
	ErrMissingProductVariantCode = CustomError{
		Code:    1204,
		Message: "Missing product variant code",
	}

	// ErrMissingClosingTimestamp ...
	ErrMissingClosingTimestamp = CustomError{
		Code:    1205,
		Message: "Missing closing timestamp",
	}

	// ErrMissingCreatedBy ...
	ErrMissingCreatedBy = CustomError{
		Code:    1206,
		Message: "Missing createdBy field",
	}

	// ErrMissingUpdatedBy ...
	ErrMissingUpdatedBy = CustomError{
		Code:    1207,
		Message: "Missing updatedBy field",
	}

	// ErrInvalidStatus ...
	ErrInvalidStatus = CustomError{
		Code:    1208,
		Message: "Invalid status",
	}

	// ErrInvalidInterestPostingFrequency ...
	ErrInvalidInterestPostingFrequency = CustomError{
		Code:    1209,
		Message: "Invalid interest posting frequency",
	}

	// ErrInvalidSoftDepositCap ...
	ErrInvalidSoftDepositCap = CustomError{
		Code:    1210,
		Message: "Invalid soft deposit cap",
	}

	// ErrInvalidHardDepositCap ...
	ErrInvalidHardDepositCap = CustomError{
		Code:    1211,
		Message: "Invalid hard deposit cap",
	}

	// ErrInvalidApplicableHoldCode ...
	ErrInvalidApplicableHoldCode = CustomError{
		Code:    1212,
		Message: "Invalid applicable hold code",
	}

	// ErrNoParameterToUpdate ...
	ErrNoParameterToUpdate = CustomError{
		Code:    1213,
		Message: "No parameter to update",
	}

	// ErrObjectMarshal ...
	ErrObjectMarshal = CustomError{
		Code:    1214,
		Message: "Failed to marshal object",
	}

	// ErrDatabaseFailure ...
	ErrDatabaseFailure = CustomError{
		Code:    1215,
		Message: "Database failure",
	}

	// ErrSaveDataInDB ...
	ErrSaveDataInDB = CustomError{
		Code:    1216,
		Message: "Failed to save data in database",
	}

	// ErrUpdateDataInDB ...
	ErrUpdateDataInDB = CustomError{
		Code:    1217,
		Message: "Failed to update data in database",
	}

	// ErrAccountNotFound ...
	ErrAccountNotFound = CustomError{
		Code:    1218,
		Message: "Account not found",
	}

	// ErrRecordNotFound ...
	ErrRecordNotFound = CustomError{
		Code:    1219,
		Message: "Record not found",
	}

	// ErrCifMappingNotFound ...
	ErrCifMappingNotFound = CustomError{
		Code:    1220,
		Message: "Cif mapping not found for provided token",
	}

	// ErrCustomerIdentityMappingNotFound ...
	ErrCustomerIdentityMappingNotFound = CustomError{
		Code:    1220,
		Message: "Customer Identity mapping not found for provided token",
	}

	// ErrCustomerMasterConnectionFailure ...
	ErrCustomerMasterConnectionFailure = CustomError{
		Code:    1221,
		Message: "Failed to connect customer-master",
	}

	// ErrDepositsCoreFailure ...
	ErrDepositsCoreFailure = CustomError{
		Code:    1222,
		Message: "Unable to fetch data from deposits-core",
	}

	// ErrAccountPermissionForbidden ...
	ErrAccountPermissionForbidden = CustomError{
		Code:    1223,
		Message: "Account permission forbidden",
	}

	// ErrJWTNotFound ...
	ErrJWTNotFound = CustomError{
		Code:    1224,
		Message: "JWT not found",
	}

	// ErrStatusChange ...
	ErrStatusChange = CustomError{
		Code: 1225,
	}

	// ErrGenericInvalidRequest ...
	ErrGenericInvalidRequest = CustomError{
		Code:    1226,
		Message: "Invalid request",
	}

	// ErrGenericInternalError ...
	ErrGenericInternalError = CustomError{
		Code:    1227,
		Message: "Internal error",
	}

	// ErrObjectUnMarshal ...
	ErrObjectUnMarshal = CustomError{
		Code:    1228,
		Message: "Failed to Unmarshal object",
	}

	// ErrLoadDataFromDB ...
	ErrLoadDataFromDB = CustomError{
		Code:    1229,
		Message: "Failed to load data from database",
	}

	// ErrMissingPocketTemplateDetail ...
	ErrMissingPocketTemplateDetail = CustomError{
		Code:    1230,
		Message: "Missing PocketTemplateDetail field",
	}

	// ErrMissingPocketTemplateDetailsFields ...
	ErrMissingPocketTemplateDetailsFields = CustomError{
		Code:    1231,
		Message: "Missing PocketTemplateID or QuestionAnswerPairs field",
	}
	// ErrChildAccountLimitExceeded ...
	ErrChildAccountLimitExceeded = CustomError{
		Code:    1232,
		Message: "Savings child account limit exceeded",
	}

	// ErrInvalidPocket ...
	ErrInvalidPocket = CustomError{
		Code:    1234,
		Message: "Given pocket ID is not a pocket",
	}

	// ErrMissingPocketID ...
	ErrMissingPocketID = CustomError{
		Code:    1235,
		Message: "Missing PocketID field",
	}

	// ErrIncorrectPocketIDFormat ...
	ErrIncorrectPocketIDFormat = CustomError{
		Code:    1236,
		Message: "Pocket ID format is invalid",
	}
	// ErrSamePocketImage ...
	ErrSamePocketImage = CustomError{
		Code:    1237,
		Message: "Existing pocket image cannot be same as the new image",
	}

	// ErrSamePocketName ...
	ErrSamePocketName = CustomError{
		Code:    1238,
		Message: "Existing pocket name cannot be same as the new name",
	}

	// ErrPocketUpdate ...
	ErrPocketUpdate = CustomError{
		Code:    1239,
		Message: "Cannot update as the Pocket is CLOSED/DORMANT",
	}

	// ErrMissingCurrency ...
	ErrMissingCurrency = CustomError{
		Code:    1240,
		Message: "Missing currency field",
	}

	// ErrDateInPast ...
	ErrDateInPast = CustomError{
		Code:    1241,
		Message: "Date cannot be empty or in past",
	}

	// ErrTargetAmountLessThanZero ...
	ErrTargetAmountLessThanZero = CustomError{
		Code:    1242,
		Message: "Target Amount less than zero",
	}

	// ErrTargetAmountZero ...
	ErrTargetAmountZero = CustomError{
		Code:    1243,
		Message: "Target amount missing or has zero value",
	}

	// ErrInvalidCurrency ...
	ErrInvalidCurrency = CustomError{
		Code:    1244,
		Message: "Invalid currency",
	}

	// ErrAccountInactive ...
	ErrAccountInactive = CustomError{
		Code:    1245,
		Message: "Account is not active",
	}

	// ErrMissingImageURL ...
	ErrMissingImageURL = CustomError{
		Code:    1246,
		Message: "Missing ImageUrl field",
	}

	// ErrFetchingPockets ...
	ErrFetchingPockets = CustomError{
		Code:    1247,
		Message: "Error in fetching pockets from DB",
	}

	// ErrMissingGoalID ...
	ErrMissingGoalID = CustomError{
		Code:    1248,
		Message: "Missing GoalID field",
	}

	// ErrPocketsNotFound ...
	ErrPocketsNotFound = CustomError{
		Code:    1253,
		Message: "Pockets not found",
	}

	// ErrPocketTemplateIDNotFound ...
	ErrPocketTemplateIDNotFound = CustomError{
		Code:    1254,
		Message: "PocketTemplateID not found in product master",
	}

	// ErrProductMasterConnectionFailure ...
	ErrProductMasterConnectionFailure = CustomError{
		Code:    1255,
		Message: "Failed to connect product-master",
	}

	// ErrChildAccountNameCharLimitExceeded ...
	ErrChildAccountNameCharLimitExceeded = CustomError{
		Code:    1256,
		Message: "Child account name character limit exceeded",
	}

	// ErrIncorrectChildAccountNameFormat ...
	ErrIncorrectChildAccountNameFormat = CustomError{
		Code:    1257,
		Message: "Child account name format is incorrect",
	}

	// ErrMissingChildAccountName ...
	ErrMissingChildAccountName = CustomError{
		Code:    1258,
		Message: "Missing Child account name field",
	}

	// ErrMissingAccountType ...
	ErrMissingAccountType = CustomError{
		Code:    1259,
		Message: "Missing account type",
	}

	// ErrEmptyAccountIDs ...
	ErrEmptyAccountIDs = CustomError{
		Code:    1260,
		Message: "AccountID array is empty",
	}

	// ErrInvalidAccountType ...
	ErrInvalidAccountType = CustomError{
		Code:    1261,
		Message: "Invalid account type",
	}

	// ErrMissingImageID ...
	ErrMissingImageID = CustomError{
		Code:    1262,
		Message: "Missing imageID field",
	}
	// ErrAccountAlreadyExists ...
	ErrAccountAlreadyExists = CustomError{
		Code:    1263,
		Message: "Account already exists for cif number",
	}

	// ErrChildAccountNameAlreadyExists ...
	ErrChildAccountNameAlreadyExists = CustomError{
		Code:    1264,
		Message: "Child account with given name already exists",
	}

	// ErrEmptyBalancesForAccount ...
	ErrEmptyBalancesForAccount = CustomError{
		Code:    1265,
		Message: "Balances for account does not exist",
	}

	// ErrInvalidCif ...
	ErrInvalidCif = CustomError{
		Code:    1266,
		Message: "Cif Number is not valid",
	}

	// ErrGenericForbiddenRequest ...
	ErrGenericForbiddenRequest = CustomError{
		Code:    1267,
		Message: "Forbidden",
	}

	// ErrInvalidAccountWithOutstandingLoans ...
	ErrInvalidAccountWithOutstandingLoans = CustomError{
		Code:    1269,
		Message: "Given LOC account has outstanding loans. Account can't be closed",
	}

	// DBMY error codes

	// ErrFirstDepositNotCompleted ...
	ErrFirstDepositNotCompleted = CustomError{
		Code:    5000,
		Message: "First deposit action not completed",
	}

	// ErrTargetAmountExceedMax ...
	ErrTargetAmountExceedMax = CustomError{
		Code:    5001,
		Message: "Target amount exceeded max value",
	}

	// ErrClientCloseConn ...
	ErrClientCloseConn = CustomError{
		Code:    5002,
		Message: "Client closed connection unexpectedly",
	}

	// ErrSchedulerRemovalFailure ...
	ErrSchedulerRemovalFailure = CustomError{
		Code:    5003,
		Message: "Failed to remove pocket scheduler",
	}

	// ErrAccountClosureDisabled ...
	ErrAccountClosureDisabled = CustomError{
		Code:    5004,
		Message: "Account closure is disabled",
	}

	// ErrAccountClosureHoldcode ...
	ErrAccountClosureHoldcode = CustomError{
		Code:    5005,
		Message: "Failed to close account due to holdcode",
	}

	// ErrChildAccountNameMatchingMicroSaver ...
	ErrChildAccountNameMatchingMicroSaver = CustomError{
		Code:    1268,
		Message: "Child account with given name already exists",
	}

	// ParsingError ...
	ParsingError = CustomError{
		Code:    1269,
		Message: "Parsing Error",
	}

	// ErrMicroSaverAccountExists ...
	ErrMicroSaverAccountExists = CustomError{
		Code:    1270,
		Message: "active microsaver account already exists",
	}

	// InvalidMicroSaverRequest ...
	InvalidMicroSaverRequest = CustomError{
		Code:    1239,
		Message: "Invalid microsaver account create request",
	}

	// ErrMissingProductCode ...
	ErrMissingProductCode = CustomError{
		Code:    1263,
		Message: "Missing product code",
	}

	// ErrMissingProductVariantVersion ...
	ErrMissingProductVariantVersion = CustomError{
		Code:    1263,
		Message: "Missing product variant version",
	}

	// ErrInvalidProductVariantCode ...
	ErrInvalidProductVariantCode = CustomError{
		Code:    1266,
		Message: "Invalid product variant code",
	}

	// ErrProductVariantCodeNotFound ...
	ErrProductVariantCodeNotFound = CustomError{
		Code:    1265,
		Message: "Product variant code does not exists",
	}

	// ErrMissingMandatoryProductParameter ...
	ErrMissingMandatoryProductParameter = CustomError{
		Code:    1264,
		Message: "Missing Mandatory Product Parameter",
	}

	// ErrInvalidReasonCodeForLOCAccountDeactivation ...
	ErrInvalidReasonCodeForLOCAccountDeactivation = CustomError{
		Code:    1269,
		Message: "Invalid reason",
	}

	// ErrInvalidSubStatus ...
	ErrInvalidSubStatus = CustomError{
		Code:    1269,
		Message: "Invalid sub status",
	}

	// ErrInvalidReasonsAndCommentForLOCAccountClosure ...
	ErrInvalidReasonsAndCommentForLOCAccountClosure = CustomError{
		Code:    1267,
		Message: "Invalid reasons-comment combination",
	}

	// ErrInvalidCommentForLOCAccountClosure ...
	ErrInvalidCommentForLOCAccountClosure = CustomError{
		Code:    1268,
		Message: "Comment length can not be more than 1000 characters",
	}

	// ErrAccountAlreadyActive ...
	ErrAccountAlreadyActive = CustomError{
		Code:    1249,
		Message: "Account is active",
	}

	// ErrMissingRequestID ...
	ErrMissingRequestID = CustomError{
		Code:    1283,
		Message: "Missing request id",
	}

	// ErrMissingRequestOperation ...
	ErrMissingRequestOperation = CustomError{
		Code:    1284,
		Message: "Missing request operation",
	}

	// ErrInvalidReasonLengthForCASAStatus ...
	ErrInvalidReasonLengthForCASAStatus = CustomError{
		Code:    1289,
		Message: "Reason length must not be empty and less than 100 characters",
	}
)

// Error codes for workflow execution
var (
	// ErrWorkflowInit ...
	ErrWorkflowInit = CustomError{
		Code:    2000,
		Message: "Workflow Initialize error",
	}

	// ErrWorkflowGet ...
	ErrWorkflowGet = CustomError{
		Code:    2001,
		Message: "Workflow Get error",
	}

	// ErrWorkflowExecute ...
	ErrWorkflowExecute = CustomError{
		Code:    2002,
		Message: "Workflow execute error",
	}

	// ErrActionNotPermitted ...
	ErrActionNotPermitted = CustomError{
		Code:    1287,
		Message: "Action not permitted",
	}
)

// Error codes for lending functionalities
var (
	// ErrMissingWebhookURL ...
	ErrMissingWebhookURL = CustomError{
		Code:    2003,
		Message: "Missing webhookURL field",
	}

	// ErrInvalidOfferedLOC ...
	ErrInvalidOfferedLOC = CustomError{
		Code:    2004,
		Message: "Invalid offeredLOC amount",
	}

	// ErrCIFNumberNotFound ...
	ErrCIFNumberNotFound = CustomError{
		Code:    2005,
		Message: "CIF number doesn't exists",
	}

	// ErrMaxActiveLOCAccountsThresholdExceeded ...
	ErrMaxActiveLOCAccountsThresholdExceeded = CustomError{
		Code:    2006,
		Message: "Max Active LOC accounts threshold exceeded for the customer",
	}

	// ErrLOCAccountCreationAlreadyBeingProcessed ...
	ErrLOCAccountCreationAlreadyBeingProcessed = CustomError{
		Code:    2007,
		Message: "LOC Account creation is already being processed by other workflow",
	}

	// ErrInvalidOfferedMaxTenor ...
	ErrInvalidOfferedMaxTenor = CustomError{
		Code:    2008,
		Message: "Invalid offeredMaxTenor field",
	}

	// ErrInvalidOfferedInterestRate ...
	ErrInvalidOfferedInterestRate = CustomError{
		Code:    2009,
		Message: "Invalid offeredInterestRate field",
	}

	// ErrMissingApplicationID ...
	ErrMissingApplicationID = CustomError{
		Code:    2010,
		Message: "Missing applicationID field",
	}

	// ErrLOCAccountClosureAlreadyBeingProcessed ...
	ErrLOCAccountClosureAlreadyBeingProcessed = CustomError{
		Code:    2011,
		Message: "LOC Account closure is already being processed by other workflow",
	}

	// InvalidVariantIDRequest ...
	InvalidVariantIDRequest = CustomError{
		Code:    1271,
		Message: "Get customer account by variantID request failed",
	}

	InvalidLOCAccountStatus = CustomError{
		Code:    1272,
		Message: "Invalid LOC account status",
	}

	// ErrListEffectiveProductVariantParametersResponse ...
	ErrListEffectiveProductVariantParametersResponse = CustomError{
		Code:    1287,
		Message: "Invalid Response for ProductVariantParameters",
	}

	// ErrAccountLimitExceeded ...
	ErrAccountLimitExceeded = CustomError{
		Code:    1281,
		Message: "You can only create a maximum of #maxLimit account",
	}

	// ErrInvalidPagination ...
	ErrInvalidPagination = CustomError{
		Code:    1288,
		Message: "Invalid pagination param",
	}

	// ErrBusinessNotFound ...
	ErrBusinessNotFound = CustomError{
		Code:    1289,
		Message: "Business not found",
	}

	// ErrMissingParentAccountID ...
	ErrMissingParentAccountID = CustomError{
		Code:    1290,
		Message: "Missing Parent Account ID",
	}

	// ErrInvalidMaturityInstructionType ...
	ErrInvalidMaturityInstructionType = CustomError{
		Code:    1291,
		Message: "Invalid maturity instruction type",
	}

	// ErrMissingCalendarDate ...
	ErrMissingCalendarDate = CustomError{
		Code:    1292,
		Message: "Missing calendar date",
	}

	// ErrInvalidCalendarDate ...
	ErrInvalidCalendarDate = CustomError{
		Code:    1293,
		Message: "Invalid calendar date",
	}

	// ErrMissingFundSourceAccount ...
	ErrMissingFundSourceAccount = CustomError{
		Code:    1294,
		Message: "Missing fund source account",
	}

	// ErrPrincipalAmountOutOfRange ...
	ErrPrincipalAmountOutOfRange = CustomError{
		Code:    1295,
		Message: "Principal balance is out of range",
	}

	// InternalServerError ...
	InternalServerError = CustomError{
		Code:    500,
		Message: "Internal server error",
	}
)
