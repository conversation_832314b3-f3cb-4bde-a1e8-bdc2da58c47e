package utils

import (
	"context"
	"reflect"
	"strings"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	goalCore "gitlab.myteksi.net/dakota/core-banking/goal-core/api"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
)

// MapToTMStatus ...
func MapToTMStatus(status api.AccountStatus) depositsCore.AccountStatus {
	statusMap := make(map[api.AccountStatus]depositsCore.AccountStatus)
	statusMap[api.AccountStatus_ACTIVE] = depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN
	statusMap[api.AccountStatus_DORMANT] = depositsCore.AccountStatus_ACCOUNT_STATUS_DORMANT
	statusMap[api.AccountStatus_CLOSED] = depositsCore.AccountStatus_ACCOUNT_STATUS_CLOSED
	statusMap[api.AccountStatus_PENDING_ACTIVATION] = depositsCore.AccountStatus_ACCOUNT_STATUS_PENDING_ACTIVATION
	return statusMap[status]
}

// TODO: add in a reverse function MapFromTMStatus

// MapCASAAccountObjFromDeposits ...
func MapCASAAccountObjFromDeposits(account *depositsCore.CASAAccount) api.CASAAccount {
	return api.CASAAccount{
		Id:                        account.Id,
		ProductID:                 account.ProductID,
		ProductVariantID:          account.ProductVariantID,
		ProductVersionID:          account.ProductVersionID,
		PermittedCurrencies:       account.PermittedCurrencies,
		OpeningTimestamp:          account.OpeningTimestamp,
		ClosingTimestamp:          account.ClosingTimestamp,
		ProductSpecificParameters: account.InstanceParams,
		DerivedParams:             account.DerivedParams,
	}
}

// MapCASAAccount ...
func MapCASAAccount(account *depositsCore.CASAAccount, req *api.CreateCASAAccountRequest) api.CASAAccount {
	return api.CASAAccount{
		Id:                        account.Id,
		ProductID:                 account.ProductID,
		ProductVariantID:          req.ProductVariantCode,
		ProductVersionID:          account.ProductVersionID,
		PermittedCurrencies:       account.PermittedCurrencies,
		OpeningTimestamp:          account.OpeningTimestamp,
		ClosingTimestamp:          account.ClosingTimestamp,
		ProductSpecificParameters: mapInstanceParamsToProductSpecificParameters(account.InstanceParams),
		DerivedParams:             account.DerivedParams,
		CifNumber:                 req.CifNumber,
		ParentAccountID:           req.ParentAccountID,
		Name:                      req.Name,
		Status:                    api.AccountStatus_ACTIVE,
	}
}

// GetValidTransitionsForAccountStatus ...
// Deprecated: - use the one defined in logic layer
func GetValidTransitionsForAccountStatus(status api.AccountStatus) []api.AccountStatus {
	transitionMap := make(map[api.AccountStatus][]api.AccountStatus)
	transitionMap[api.AccountStatus_PENDING_ACTIVATION] = []api.AccountStatus{api.AccountStatus_ACTIVE, api.AccountStatus_CLOSED}
	transitionMap[api.AccountStatus_ACTIVE] = []api.AccountStatus{api.AccountStatus_DORMANT, api.AccountStatus_CLOSED}
	transitionMap[api.AccountStatus_DORMANT] = []api.AccountStatus{api.AccountStatus_ACTIVE, api.AccountStatus_CLOSED}
	transitionMap[api.AccountStatus_CLOSED] = []api.AccountStatus{}

	return transitionMap[status]
}

// MapSavingsPocketObjFromDeposits ...
func MapSavingsPocketObjFromDeposits(savingsPocket *depositsCore.SavingsPocket) api.SavingsPocket {
	return api.SavingsPocket{
		Id:        savingsPocket.Id,
		AccountID: savingsPocket.AccountID,
	}
}

// MapToGoalCoreStatus ...
func MapToGoalCoreStatus(status goalCore.GoalStatus) api.GoalStatus_Status {
	statusMap := make(map[goalCore.GoalStatus]api.GoalStatus_Status)
	statusMap[goalCore.GoalStatus_COMPLETED] = api.GoalStatus_Status_COMPLETED
	statusMap[goalCore.GoalStatus_EMPTY] = api.GoalStatus_Status_EMPTY
	statusMap[goalCore.GoalStatus_CLOSED] = api.GoalStatus_Status_CLOSED
	return statusMap[status]
}

func mapInstanceParamsToProductSpecificParameters(instanceParams map[string]string) map[string]string {
	mp := make(map[string]string)
	applicableHoldCodeVal := "[]"
	if val := instanceParams[constants.ApplicableHoldcodes]; strings.TrimSpace(val) != "" {
		applicableHoldCodeVal = val
	}
	mp[constants.ApplicableHoldcodes] = applicableHoldCodeVal
	return mp
}

// MapAccount ...
func MapAccount(
	account *depositsCore.CASAAccount,
	dbAccount *storage.CustomerAccount,
) api.Account {
	return api.Account{
		Id:                        dbAccount.AccountID,
		PermittedCurrencies:       account.PermittedCurrencies,
		ProductSpecificParameters: mapInstanceParamsToProductSpecificParameters(account.InstanceParams),
		CifNumber:                 dbAccount.CifNumber,
		Status:                    dbAccount.CurrentStatus,
		ParentAccountID:           dbAccount.ParentAccountID.String,
		ProductID:                 dbAccount.ProductID,
		ProductVariantID:          dbAccount.ProductVariantID,
		OpeningTimestamp:          dbAccount.AccountOpeningTimestamp,
		Name:                      dbAccount.Name.String,
		AccountType:               string(dbAccount.Type),
	}
}

// MapPendingActionToAPI map pending action entity to API DTO
func MapPendingActionToAPI(action *storage.PendingAction) api.AccountPendingAction {
	return api.AccountPendingAction{
		ID:     action.PublicID,
		Name:   action.Name,
		Status: string(action.Status),
	}
}

// MapPendingActionsToAPI map pending actions entity to API DTOs
func MapPendingActionsToAPI(actions []*storage.PendingAction) []api.AccountPendingAction {
	var results []api.AccountPendingAction
	for _, action := range actions {
		results = append(results, MapPendingActionToAPI(action))
	}
	return results
}

// MapToCASAAccountV2Request ...
func MapToCASAAccountV2Request(ctx context.Context, req *api.CreateCASAAccountV3Request) *dto.CASAAccountV3Request {
	var cifNumber string
	cifNumber, _ = ctx.Value(constants.CtxUserCifNumber).(string)
	if req.CifNumber != "" {
		cifNumber = req.CifNumber
	}
	return &dto.CASAAccountV3Request{
		IdempotencyKey:     req.IdempotencyKey,
		ProductVariantCode: req.ProductVariantCode,
		CifNumber:          cifNumber,
		CreatedBy:          req.CreatedBy,
		InstanceParams: dto.CASAAccountParams{
			ApplicableHoldcodes: req.InstanceParams.ApplicableHoldcodes,
		},
		ProductParameters: req.ProductParameters,
		ParentAccountID:   req.ParentAccountID,
	}
}

// MapToCASAAccountV2Response ...
func MapToCASAAccountV2Response(resp *api.CreateCASAAccountV3Response) *dto.CASAAccountV3Response {
	return &dto.CASAAccountV3Response{
		RequestIdempotencyKey: resp.RequestIdempotencyKey,
		RequestStatus:         resp.RequestStatus,
	}
}

// MapFromDepositsCoreStatus ...
func MapFromDepositsCoreStatus(status string) api.AccountStatus {
	switch status {
	case string(depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN):
		return api.AccountStatus_ACTIVE
	case string(depositsCore.AccountStatus_ACCOUNT_STATUS_DORMANT):
		return api.AccountStatus_DORMANT
	case string(depositsCore.AccountStatus_ACCOUNT_STATUS_CLOSED):
		return api.AccountStatus_CLOSED
	default:
		return ""
	}
}

// StructToMap converts a struct to a map[string]any using json tags as keys
func StructToMap(input any) map[string]any {
	result := make(map[string]any)

	v := reflect.ValueOf(input)
	t := reflect.TypeOf(input)

	// Handle pointer to struct
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
		t = t.Elem()
	}

	if v.Kind() != reflect.Struct {
		return result
	}

	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)

		// Skip unexported fields
		if !value.CanInterface() {
			continue
		}

		jsonTag := field.Tag.Get("json")
		if jsonTag == "" || jsonTag == "-" {
			continue
		}

		// Handle tag options, e.g., "name,omitempty"
		tagName := jsonTag
		if commaIdx := indexComma(jsonTag); commaIdx != -1 {
			tagName = jsonTag[:commaIdx]
		}

		result[tagName] = value.Interface()
	}

	return result
}

// indexComma returns the index of the first comma or -1 if none
func indexComma(tag string) int {
	for i, c := range tag {
		if c == ',' {
			return i
		}
	}
	return -1
}
