package constants

type (
	// CustomerPocketType ...
	CustomerPocketType string
)

const (
	// PocketCreationEvent field denotes value send on pocket creation
	PocketCreationEvent = "PocketCreation"
	// BoostPocketCreationEvent ...
	BoostPocketCreationEvent = "BoostPocketCreation"
	// PocketClosureEvent field denotes value send on pocket closure
	PocketClosureEvent = "PocketClosure"

	// PocketTypeSavings ...
	PocketTypeSavings     CustomerPocketType = "SAVINGS"
	PocketTypeBoostPocket CustomerPocketType = "BOOST_POCKET"

	// PocketCreationPushNotificationTemplate ...
	PocketCreationPushNotificationTemplate = "savingsPocketCreationPushNotificationTemplate"
	// PocketClosurePushNotificationTemplate ..
	PocketClosurePushNotificationTemplate = "savingsPocketClosurePushNotificationTemplate"

	// Ongoing ... Pocket Sorting constant
	Ongoing = "OnGoingPocketsWithGoal"
	// Complete ... Pocket Sorting constant
	Complete = "CompletedPocketsWithGoal"
	// WithoutGoals ... Pocket Sorting constant
	WithoutGoals = "PocketsWithoutGoal"
)

var (
	// SavingsPocketEventToNotificationTemplateMap stores map of event-type and template for savings pocket
	SavingsPocketEventToNotificationTemplateMap = map[string]string{
		PocketCreationEvent: PocketCreationPushNotificationTemplate,
		PocketClosureEvent:  PocketClosurePushNotificationTemplate,
	}
)

// bp notification event
const (
	// BPAutoRenewalOffNotification ...
	BPAutoRenewalOffNotification = "bp_auto_renewal_off"

	// BPClosureNotification ...
	BPClosureNotification = "bp_closure_notification"
)
