package testapi

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/url"
	"time"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/test/responses"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	"gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/customermaster/dto"
	externalCustomerClientMock "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/customermaster/mocks"

	mocks "gitlab.myteksi.net/dakota/core-banking/goal-core/api/mock"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	depositsCoreMock "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api/mock"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"
)

var _ = Describe("ListSavingsPocketDetail", func() {
	var xfccHeader hcl.RequestModifier
	now := time.Now().UTC()
	constTargetDate := utils.DateNowLocal().AddDate(1, 0, 0)
	var goalDetail *api.GoalDetail
	var currency string

	BeforeEach(func() {
		xfccKey, xfccValue := servus.GenerateClientIdentities(servicename.AccountService, servicename.IstioIngressGW)
		xfccHeader = hcl.Header(xfccKey, xfccValue)
		mockCustomerMaster = &customerMasterMock.CustomerMaster{}
		mockExternalCustomerMaster = &externalCustomerClientMock.ICustomerMasterClient{}
		mockDBStore = &storage.MockDatabaseStore{}
		mockGoalCore = &mocks.GoalCore{}
		mockDepositsCore = &depositsCoreMock.DepositsCore{}
		mockHermes = &hermesMock.Hermes{}

		service.CustomerMasterClient = mockCustomerMaster
		service.ExternalCustomerClient = mockExternalCustomerMaster
		service.Store = mockDBStore
		service.GoalCoreClient = mockGoalCore
		service.DepositsCoreClient = mockDepositsCore
		service.HermesClient = mockHermes
		currency = service.AppConfig.Locale.Currency

		goalDetail = responses.GoalDetail(constTargetDate, api.GoalStatus_Status_OFFTRACK, 50, 50, 20)
		goalDetail.TargetAmount = &api.Money{
			CurrencyCode: currency,
			Val:          40,
		}
	})

	Context("No Errors", func() {
		When("body is correct", func() {
			It("returns 200 happy case", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)
				mockGoalCore.On("GetGoals", mock.Anything, mock.Anything).Return(responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate), nil)
				mockDBStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetPocketResponse(now), nil)
				mockDBStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetSubAccountsNonEmptyResponse(now), nil)
				mockDepositsCore.On("GetAccountBalance", mock.Anything, mock.Anything).Return(responses.DepositsCoreBalanceWithPocketsResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(responses.ListImageDetailsResponseFromHermes(), nil)

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(200))

				expectedResponse := responses.ListSavingsPocketDetailResponse(constTargetDate, "test-pocket-id-001", "test-pocket-id-002", goalDetail)
				respObj := &api.ListSavingsPocketDetailResponse{}
				err = json.Unmarshal(resp.Body.Bytes, respObj)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(respObj).Should(Equal(expectedResponse))
			})
		})
	})
	Context("Forbidden Access", func() {
		When("Main account does not belong to the User", func() {
			It("returns 400 error", func() {
				mockProfileID = func() string {
					return ""
				}
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif-1",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif-1",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID":"test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(403))

				expectedResponse := AccountPermissionForbiddenResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Validation Error", func() {
		When("Missing accountID", func() {
			It("returns 400 error", func() {
				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID":""
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))

				expectedResponse := MissingAccountIDResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("Database Error", func() {
		When("Unable to load customer account", func() {
			It("returns error", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(nil, errors.New("unable to load data from Pocket table"))
				mockDepositsCore.On("GetAccountBalance", mock.Anything, mock.Anything).Return(responses.DepositsCoreBalanceWithPocketsResponse(), nil)

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := DatabaseFailureResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("Unable to load pockets", func() {
			It("returns error", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)
				mockGoalCore.On("GetGoals", mock.Anything, mock.Anything).Return(responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate), nil)
				mockDBStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("some db failure"))
				mockDBStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetSubAccountsNonEmptyResponse(now), nil)

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := ErrDatabaseLoadResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
	})
	Context("service error", func() {
		When("no active pockets found", func() {
			It("returns error", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)
				mockDBStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
				mockDBStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetSubAccountsNonEmptyResponse(now), nil)
				mockDepositsCore.On("GetAccountBalance", mock.Anything, mock.Anything).Return(responses.DepositsCoreBalanceWithPocketsResponse(), nil)

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(404))
				expectedResponse := ErrRecordNotFoundResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("Unable to fetch balance via deposits-core", func() {
			It("returns error", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)
				mockGoalCore.On("GetGoals", mock.Anything, mock.Anything).Return(responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate), nil)
				mockDBStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetPocketResponse(now), nil)
				mockDBStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetSubAccountsNonEmptyResponse(now), nil)
				mockDepositsCore.On("GetAccountBalance", mock.Anything, mock.Anything).Return(
					nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, "xxxx", "Some service error"))

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := `{
				   "code": "xxxx",
				   "message": "Some service error"
				  }`
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("goal-core service error via goal-core", func() {
			It("returns error", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)
				mockDBStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetPocketResponse(now), nil)
				mockDBStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetSubAccountsNonEmptyResponse(now), nil)
				mockDepositsCore.On("GetAccountBalance", mock.Anything, mock.Anything).Return(responses.DepositsCoreBalanceWithPocketsResponse(), nil)
				mockGoalCore.On("GetGoals", mock.Anything, mock.Anything).Return(nil, &url.Error{
					Op:  "GET",
					URL: "http://goal-core/api/v1/goals",
					Err: errors.New("unknown error")})

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				expectedResponse := GenericInternalErrorResponse
				Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
			})
		})
		When("unable to fetch image details from hermes", func() {
			It("returns error", func() {
				mockCustomerMaster.On("LookupCIFNumber", mock.Anything, mock.Anything).Return(&customerMaster.LookupCIFNumberResponse{
					CifNumber: "test-cif",
				}, nil)
				mockExternalCustomerMaster.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(&dto.GetBusinessInfoResponse{
					CIF: "test-cif",
				}, nil)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(responses.GetCustomerAccountResponse(now), nil)
				mockGoalCore.On("GetGoals", mock.Anything, mock.Anything).Return(responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate), nil)
				mockDBStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetPocketResponse(now), nil)
				mockDBStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(responses.GetSubAccountsNonEmptyResponse(now), nil)
				mockDepositsCore.On("GetAccountBalance", mock.Anything, mock.Anything).Return(responses.DepositsCoreBalanceWithPocketsResponse(), nil)
				mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(nil, errors.New("error from hermes"))

				resp, err := client.Post(ListSavingsPocketDetail, xfccHeader,
					hcl.JSON(`{
									"accountID": "test-account-id"
								}`), hcl.Header(constants.GrabUserIDHeader, "test-user-id"),
					hcl.Header(constants.GrabServiceIDHeader, "test-service-id"))

				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
			})
		})
	})
})
