// Package testapi contains api level test cases
package testapi

// URLs ...
const (
	CreateCASAAccountURL                 = "/v2/casa-accounts"
	GetCASAAccountURL                    = "/v1/casa-accounts/:accountID"
	ListCASAAccountsForCustomerURL       = "/v1/casa-accounts"
	ListCASAAccountsForCustomerDetailURL = "/v2/accounts/list"
	UpdateCASAAccountParamsURL           = "/v1/casa-accounts/:accountID/parameters"
	UpdateCASAAccountStatusURL           = "/v1/casa-accounts/:accountID/status"
	GetAccountBalanceURL                 = "/v1/casa-accounts/:accountID/balances"
	CheckPermissionsURL                  = "/v1/casa-accounts/:accountID/permissions?cifNumber=:cifNumber"
	LookUpAccountParentURL               = "/v1/accounts/:accountID/parent"
	GetAccountURL                        = "/v1/accounts/:accountID"
	CreateSavingsPocketURL               = "/v1/savings-pockets"
	UpdateSavingsPocketStatusURL         = "/v1/savings-pockets/status"
	CloseSavingsPocketURL                = "/v1/savings-pockets/status-close"
	UpdateSavingsPocketNameURL           = "/v1/savings-pockets/name"
	UpdateSavingsPocketImageURL          = "/v1/savings-pockets/image"
	GetCASAAccountSummaryURL             = "/v1/casa-accounts/:accountID/summary"
	GetSavingsPocketURL                  = "/v1/savings-pockets/:pocketID"
	CreateGoalURL                        = "/v1/savings-pockets/goal"
	GetGoalURL                           = "/v1/savings-pockets/get-goal"
	GetGoalEstimate                      = "/v1/savings-pockets/goal/estimate"
	UpdateGoalURL                        = "/v1/savings-pockets/goal"
	CloseGoalURL                         = "/v1/savings-pockets/goal/status-close"
	GetSavingsPocketDetail               = "/v1/savings-pockets/pocket-detail"
	V2GetSavingsPocketDetail             = "/v2/savings-pockets/pocket-detail"
	ListSavingsPocketDetail              = "/v1/savings-pockets/summary"
	GetAccountDetailsURL                 = "/v1/accounts/account-details"
	GetAccountDetailsByAccountIDURL      = "/v2/accounts/get"
)

// Error responses ...
const (
	MissingAccountIDResponse = `{
									"code": "1201",
									"message": "Missing account id"
								}`
	MissingCifNumberResponse = `{
									"code": "1202",
									"message": "Missing cif number"
							    }`
	MissingIdempotencyKey = `{
 									"code": "1203",
									"message": "Missing idempotency key"
							  }`
	MissingProductVariantCodeResponse = `{
											"code": "1204",
											"message": "Missing product variant code"
                                         }`
	MissingClosingTimestampResponse = `{
										   	"code": "1205",
										   	"message": "Missing closing timestamp"
								       }`
	MissingCreatedByResponse = `{
									"code": "1206",
									"message": "Missing createdBy field"
								 }`
	MissingUpdatedByResponse = `{
									"code": "1207",
									"message": "Missing updatedBy field"
								 }`
	InvalidStatusResponse = `{
								"code": "1208",
								"message": "Invalid status"
							 }`
	InvalidInterestPostingFrequencyResponse = `{
												   "code": "1209",
									               "message": "Invalid interest posting frequency"
								               }`
	InvalidSoftDepositCapResponse = `{
									    "code": "1210",
									    "message": "Invalid soft deposit cap"
								     }`
	InvalidHardDepositCapResponse = `{
									    "code": "1211",
									    "message": "Invalid hard deposit cap"
								     }`
	InvalidApplicableHoldCodeResponse = `{
										    "code": "1212",
										    "message": "Invalid applicable hold code"
									     }`
	NoParameterToUpdateResponse = `{
									   "code": "1213",
									   "message": "No parameter to update"
								   }`
	DatabaseFailureResponse = `{
									"code": "1215",
									"message": "Database failure"
							   }`
	SaveDataInDBFailureResponse = `{
										"code": "1216",
										"message": "Failed to save data in database"
								   }`
	UpdateDataInDBFailureResponse = `{
										 "code": "1217",
										 "message": "Failed to update data in database"
									 }`
	AccountNotFoundResponse = `{
									"code": "1218",
									"message": "Account not found"
							   }`
	AccountPermissionForbiddenResponse = `{
											"code": "1223",
											"message": "Account permission forbidden"
										  }`
	ErrMissingJWTResponse = `{
								"code": "1224",
								"message": "JWT not found"
							  }`
	GenericInvalidResponse = `{
        						    "code": "1226",
        						    "message": "Invalid request"
      						    }`
	GenericInternalErrorResponse = `{
         						   "code": "1227",
         						   "message": "Internal error"
        						  }`
	MissingPocketTemplateDetailResponse = `{
         						   "code": "1230",
         						   "message": "Missing PocketTemplateDetail field"
        						  }`
	MissingPocketTemplateDetailFieldsResponse = `{
         						   "code": "1231",
         						   "message": "Missing PocketTemplateID or QuestionAnswerPairs field"
        						  }`
	ChildAccountLimitExceeded = `{
         						   "code": "1232",
         						   "message": "Savings child account limit exceeded"
        						  }`
	MissingPocketIDResponse = `{
         						   "code": "1235",
         						   "message": "Missing PocketID field"
        						  }`
	InvalidPocketIDFormatResponse = `{
         						   "code": "1236",
         						   "message": "Pocket ID format is invalid"
        						  }`
	InvalidPocketIDResponse = `{
         						   "code": "1234",
         						   "message": "Given pocket ID is not a pocket"
        						  }`
	InvalidCIFMappingResponse = `{
         						   "code": "1220",
         						   "message": "Cif mapping not found for provided token"
        						  }`
	ErrSamePocketImageResponse = `{
         						   "code": "1237",
         						   "message": "Existing pocket image cannot be same as the new image"
        						  }`
	ErrSamePocketNameResponse = `{
         						   "code": "1238",
         						   "message": "Existing pocket name cannot be same as the new name"
        						  }`
	ErrPocketUpdateResponse = `{
         						   "code": "1239",
         						   "message": "Cannot update as the Pocket is CLOSED/DORMANT"
        						}`
	ErrDateInPastResponse = `{
							   "code": "1241",
							   "message": "Date cannot be empty or in past"
							  }`
	ErrTargetAmountLessThanZeroResponse = `{
											   "code": "1242",
											   "message": "Target Amount less than zero"
											}`
	ErrTargetAmountZeroResponse = `{
									   "code": "1243",
									   "message": "Target amount missing or has zero value"
									}`

	ErrGoalCoreDatabaseLoadResponse = `{
										   "code": "5101",
										   "message": "Database load error"
										}`

	ErrGoalCoreDatabaseSaveResponse = `{
										   "code": "5102",
										   "message": "Database save error"
										}`

	ErrGoalCoreDatabaseUpdateResponse = `{
										   "code": "5103",
										   "message": "Database update error"
										  }`

	ErrGoalCoreRecordNotFoundResponse = `{
										   "code": "5104",
										   "message": "Record not found"
										  }`
	ErrGoalCoreGoalAlreadyExistResponse = `{
											   "code": "5125",
											   "message": "An active goal already exist"
											  }`
	ErrAccountInactiveResponse = `{
         						   "code": "1245",
         						   "message": "Account is not active"
        						  }`
	MissingImageURLResponse = `{
         						   "code": "1246",
         						   "message": "Missing ImageUrl field"
								}`
	ErrFetchingPocketsResponse = `{
         						   "code": "1247",
         						   "message": "Error in fetching pockets from DB"
        						  }`
	ErrMissingGoalID = `{
						   "code": "1248",
						   "message": "Missing GoalID field"
						  }`
	ErrUnknown = `{
				   "code": "5100",
				   "message": "Unknown Error"
				  }`
	ErrPocketNotFoundResponse = `{
									"code": "1253",
									"message": "Pockets not found"
							     }`
	ErrDatabaseLoadResponse = `{
								   "code": "1229",
								   "message": "Failed to load data from database"
								  }`
	ErrRecordNotFoundResponse = `{
								   "code": "1219",
								   "message": "Record not found"
								  }`
	ProductMasterConnectionFailureResponse = `{
											    "code": "1255",
											    "message": "Failed to connect product-master"
											  }`
	MismatchedPocketTemplateIDResponse = `{
										    "code": "1254",
										    "message": "PocketTemplateID not found in product master"
										  }`
	ErrCustomerMasterConnectionFailureResponse = `{
													"code":  "1221",
													"message": "Failed to connect customer-master"
												 }`
	ErrChildAccountNameCharLimitExceededResponse = `{
         						   "code": "1256",
         						   "message": "Child account name character limit exceeded"
        						  }`
	ErrIncorrectChildAccountNameStringFormatResponse = `{
         						   "code": "1257",
         						   "message": "Child account name format is incorrect"
        						  }`
	ErrMissingChildAccountNameResponse = `{
         						   "code": "1258",
         						   "message": "Missing Child account name field"
        						  }`
	MissingAccountTypeResponse = `{
									"code": "1259",
									"message": "Missing account type"
								}`
	EmptyAccountIDsResponse = `{
									"code": "1260",
									"message": "AccountID array is empty"
								}`
	ErrMissingImageIDResponse = `{
         						   "code": "1262",
         						   "message": "Missing imageID field"
        						  }`
	ErrAccountAlreadyExists = `{
         						   "code": "1263",
         						   "message": "Account already exists for cif number"
        						  }`
	ErrInvalidCif = `{
								"code":    "1266",
								"message": "Cif Number is not valid"
								}`

	ErrAccountPermissionForbidden = `{
            "code": "1223",
            "message": "Account permission forbidden"
          }`
)
