// Package responses provides test support.
package responses

import (
	"database/sql"
	"time"

	"github.com/samber/lo"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	depositsExp "gitlab.myteksi.net/dbmy/core-banking/deposits-exp/api"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.myteksi.net/bersama/core-banking/account-service/test/utils"
	goalCore "gitlab.myteksi.net/dakota/core-banking/goal-core/api"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
	productMaster "gitlab.myteksi.net/dbmy/core-banking/product-master/api"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
)

// GetAccountResponse ..
func GetAccountResponse(now time.Time) *storage.CustomerAccount {
	return &storage.CustomerAccount{
		ID:                      1234,
		AccountID:               "test-account-id",
		ParentAccountID:         sql.NullString{},
		CifNumber:               "test-cif",
		Name:                    sql.NullString{},
		Type:                    storage.AccountTypeCasa,
		ProductID:               "test-product-id",
		ProductVariantID:        "test-variant-1",
		ProductVariantVersion:   "v1.0.0",
		Currency:                constants.DefaultCurrency,
		CountryCode:             constants.DefaultCountryCode,
		Notes:                   sql.NullString{},
		CurrentStatus:           api.AccountStatus_ACTIVE,
		AccountOpeningTimestamp: now,
		CreatedAt:               now,
		CreatedBy:               "test",
		UpdatedAt:               now,
		UpdatedBy:               "test",
	}
}

// CreateSavingsPocketResponses ...
func CreateSavingsPocketResponses() *api.CreateSavingsPocketResponse {
	return &api.CreateSavingsPocketResponse{
		SavingsPocket: &api.SavingsPocket{
			Id:        "test-pocket-id",
			Name:      "Paris",
			AccountID: "test-account-id",
			Status:    api.AccountStatus_ACTIVE,
		},
	}
}

// GetSavingsPocketResponses ...
func GetSavingsPocketResponses() *api.GetSavingsPocketResponse {
	return &api.GetSavingsPocketResponse{
		SavingsPocket: &api.SavingsPocket{
			Id:        "test-pocket-id-001",
			Name:      "phone",
			AccountID: "test-account-id",
			Status:    api.AccountStatus_ACTIVE,
		},
	}
}

// CreateSavingsPocketWithSpaceInNameResponses ...
func CreateSavingsPocketWithSpaceInNameResponses() *api.CreateSavingsPocketResponse {
	return &api.CreateSavingsPocketResponse{
		SavingsPocket: &api.SavingsPocket{
			Id:        "test-pocket-id",
			Name:      "MacBook Air",
			AccountID: "test-account-id",
			Status:    api.AccountStatus_ACTIVE,
		},
	}
}

// ActiveSavingsPocketStatusResponses ...
func ActiveSavingsPocketStatusResponses() *api.UpdateSavingsPocketStatusResponse {
	return &api.UpdateSavingsPocketStatusResponse{
		PocketID: "test-pocket-id",
		Status:   api.AccountStatus_ACTIVE,
	}
}

// CloseSavingsPocketStatusResponses ...
func CloseSavingsPocketStatusResponses() *api.UpdateSavingsPocketStatusResponse {
	return &api.UpdateSavingsPocketStatusResponse{
		PocketID: "test-pocket-id",
		Status:   api.AccountStatus_CLOSED,
	}
}

// UpdateSavingsPocketNameResponses ...
func UpdateSavingsPocketNameResponses() *api.UpdateSavingsPocketNameResponse {
	return &api.UpdateSavingsPocketNameResponse{
		PocketID: "test-pocket-id",
		Name:     "new_name",
	}
}

// DormantSavingsPocketStatusResponses ...
func DormantSavingsPocketStatusResponses() *api.UpdateSavingsPocketStatusResponse {
	return &api.UpdateSavingsPocketStatusResponse{
		PocketID: "test-pocket-id",
		Status:   api.AccountStatus_DORMANT,
	}
}

// GoalCoreCreateGoalResponse ..
func GoalCoreCreateGoalResponse(now time.Time) *goalCore.CreateGoalResponse {
	return &goalCore.CreateGoalResponse{
		Goal: GoalCoreGoal(now, 1234, goalCore.GoalStatus_EMPTY),
	}
}

// DepositsCoreBalanceWithoutPocketsResponse ...
//
//nolint:dupl
func DepositsCoreBalanceWithoutPocketsResponse() *depositsCore.GetAccountBalanceResponse {
	currency := utils.GetLocale().Currency
	return &depositsCore.GetAccountBalanceResponse{
		AccountID:        "testID",
		AvailableBalance: nil,
		Balances: []depositsCore.AccountBalance{
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_OUTGOING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_INCOMING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          15,
				},
			},
		},
	}
}

// CreateGoalResponse ..
func CreateGoalResponse(now time.Time) *api.CreateGoalResponse {
	return &api.CreateGoalResponse{
		Goal: Goal(now, 1234, api.GoalStatus_Status_EMPTY),
	}
}

// GoalCoreGetGoalResponse ..
func GoalCoreGetGoalResponse(now time.Time) *goalCore.GetGoalResponse {
	return &goalCore.GetGoalResponse{
		Goal: GoalCoreGoal(now, 1234, goalCore.GoalStatus_EMPTY),
	}
}

// GetGoalResponse ..
func GetGoalResponse(now time.Time) *api.GetGoalResponse {
	return &api.GetGoalResponse{
		Goal: Goal(now, 1234, api.GoalStatus_Status_EMPTY),
	}
}

// GoalCoreUpdateGoalResponse ..
func GoalCoreUpdateGoalResponse(now time.Time, targetAmount int64) *goalCore.UpdateGoalResponse {
	return &goalCore.UpdateGoalResponse{
		Goal: GoalCoreGoal(now, targetAmount, goalCore.GoalStatus_EMPTY),
	}
}

// UpdateGoalResponse ..
func UpdateGoalResponse(now time.Time, targetAmount int64) *api.UpdateGoalResponse {
	return &api.UpdateGoalResponse{
		Goal: Goal(now, targetAmount, api.GoalStatus_Status_EMPTY),
	}
}

// GoalCoreCloseGoalResponse ..
func GoalCoreCloseGoalResponse(now time.Time) *goalCore.UpdateGoalStatusResponse {
	return &goalCore.UpdateGoalStatusResponse{
		Goal: GoalCoreGoal(now, 1234, goalCore.GoalStatus_CLOSED),
	}
}

// CloseGoalResponse ..
func CloseGoalResponse(now time.Time) *api.CloseGoalResponse {
	return &api.CloseGoalResponse{
		Goal: Goal(now, 1234, api.GoalStatus_Status_CLOSED),
	}
}

// Goal ...
func Goal(now time.Time, targetAmount int64, status api.GoalStatus_Status) *api.Goal {
	return &api.Goal{
		ID:           "goal-id",
		PocketID:     "test-pocket",
		TargetAmount: targetAmount,
		Currency:     api.Currency(utils.GetLocale().Currency),
		TargetDate:   now,
		Status:       status,
	}
}

// GoalCoreGoal ...
func GoalCoreGoal(now time.Time, targetAmount int64, status goalCore.GoalStatus) *goalCore.Goal {
	return &goalCore.Goal{
		ID:           "goal-id",
		AccountID:    []string{"test-pocket"},
		TargetAmount: targetAmount,
		Currency:     goalCore.Currency(utils.GetLocale().Currency),
		TargetDate:   now,
		Status:       status,
	}
}

// DepositsCoreBalanceWithPocketsResponse ...
// nolint:dupl
func DepositsCoreBalanceWithPocketsResponse() *depositsCore.GetAccountBalanceResponse {
	currency := utils.GetLocale().Currency
	return &depositsCore.GetAccountBalanceResponse{
		AccountID:        "test-account-id",
		AvailableBalance: nil,
		Balances: []depositsCore.AccountBalance{
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_OUTGOING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_INCOMING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          50,
				},
			},
			{
				AccountAddress: "test-pocket-id-001",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          20,
				},
			},
			{
				AccountAddress: "test-pocket-id-002",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
		},
	}
}

// V2DepositsCoreBalanceWithPocketsResponse ...
// nolint:dupl
func V2DepositsCoreBalanceWithPocketsResponse() *depositsCore.GetAccountBalanceResponse {
	currency := utils.GetLocale().Currency
	return &depositsCore.GetAccountBalanceResponse{
		AccountID:        "test-account-id",
		AvailableBalance: nil,
		Balances: []depositsCore.AccountBalance{
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_OUTGOING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_INCOMING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          20,
				},
			},
		},
	}
}

// DepositsExpSchedulerResponse ...
// nolint: dupl
func DepositsExpSchedulerResponse(status string) *depositsExp.Scheduler {
	return &depositsExp.Scheduler{
		SchedulerID:   "scheduler-id-1",
		SchedulerName: "scheduler-name-1",
		Status:        status,
		Amount: &depositsExp.Money{
			CurrencyCode: "MYR",
			Val:          3000,
		},
		StartDate:      "2023-01-01",
		EndDate:        "2023-06-01",
		FrequencyType:  "MONTHLY",
		FrequencyLabel: "Monthly",
		RepeatCount:    6,
	}
}

// SchedulerDetail ...
// nolint: dupl
func SchedulerDetail(status string) *api.SchedulerDetail {
	return &api.SchedulerDetail{
		SchedulerID:   "scheduler-id-1",
		SchedulerName: "scheduler-name-1",
		Status:        status,
		Amount: &api.Money{
			CurrencyCode: "MYR",
			Val:          3000,
		},
		StartDate:      "2023-01-01",
		EndDate:        "2023-06-01",
		FrequencyType:  "MONTHLY",
		FrequencyLabel: "Monthly",
		RepeatCount:    6,
	}
}

// DepositsExpGetSchedulerDetailResponse ...
func DepositsExpGetSchedulerDetailResponse(status string) *depositsExp.GetSchedulerDetailResponse {
	return &depositsExp.GetSchedulerDetailResponse{
		Scheduler: DepositsExpSchedulerResponse(status),
	}
}

// DepositsExpListSchedulersResponse ..
func DepositsExpListSchedulersResponse() *depositsExp.ListSchedulersResponse {
	scheduler := &depositsExp.ListSchedulersResponse{
		Schedulers: []depositsExp.Scheduler{{
			SchedulerID:   "scheduler-id-1",
			SchedulerName: "scheduler-name-1",
			Status:        "REGISTERED",
			Amount: &depositsExp.Money{
				CurrencyCode: "MYR",
				Val:          3000,
			},
			SourceAccountID:      "test-account-id",
			DestinationAccountID: "test-pocket-id-001",
			StartDate:            "2023-01-01",
			EndDate:              "2023-06-01",
			FrequencyType:        "MONTHLY",
			RepeatCount:          6,
		},
			{
				SchedulerID:   "scheduler-id-2",
				SchedulerName: "scheduler-name-2",
				Status:        "REGISTERED",
				Amount: &depositsExp.Money{
					CurrencyCode: "MYR",
					Val:          5000,
				},
				SourceAccountID:      "test-account-id",
				DestinationAccountID: "test-pocket-id-002",
				StartDate:            "2023-01-01",
				EndDate:              "2023-06-01",
				FrequencyType:        "MONTHLY",
				RepeatCount:          6,
			}},
	}
	return scheduler
}

// DepositsCoreBalanceWithPocketsAndInterestResponse ...
// nolint:dupl
func DepositsCoreBalanceWithPocketsAndInterestResponse() *depositsCore.GetAccountBalanceResponse {
	currency := utils.GetLocale().Currency
	return &depositsCore.GetAccountBalanceResponse{
		AccountID:        "test-account-id",
		AvailableBalance: nil,
		Balances: []depositsCore.AccountBalance{
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_PENDING_OUTGOING",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
			{
				AccountAddress: "ACCRUED_DEPOSIT_INTEREST",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          2,
				},
			},
			{
				AccountAddress: "DEFAULT",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          50,
				},
			},
			{
				AccountAddress: "test-pocket-id-001",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          20,
				},
			},
			{
				AccountAddress: "test-pocket-id-002",
				Phase:          "POSTING_PHASE_COMMITTED",
				Amount: &depositsCore.Money{
					CurrencyCode: currency,
					Val:          0,
				},
			},
		},
	}
}

// GetCASAAccountSummaryWithoutPocketResponse ...
func GetCASAAccountSummaryWithoutPocketResponse() *api.GetCASAAccountSummaryResponse {
	currency := utils.GetLocale().Currency
	return &api.GetCASAAccountSummaryResponse{
		TotalBalance: &api.Money{
			CurrencyCode: currency,
			Val:          15,
		},
		DailyPocketSummary: &api.DailyPocketSummary{
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          15,
			},
		},
		SavingsPocketSummary: &api.SavingsPocketSummary{
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          0,
			},
		},
	}
}

// GetCASAAccountSummaryWithPocketResponse ...
//
//nolint:dupl
func GetCASAAccountSummaryWithPocketResponse() *api.GetCASAAccountSummaryResponse {
	currency := utils.GetLocale().Currency
	return &api.GetCASAAccountSummaryResponse{
		TotalBalance: &api.Money{
			CurrencyCode: currency,
			Val:          70,
		},
		DailyPocketSummary: &api.DailyPocketSummary{
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          50,
			},
		},
		SavingsPocketSummary: &api.SavingsPocketSummary{
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          20,
			},
			Count:  2,
			Images: []string{"default-image-url1", "default-image-url2"},
			PocketImageDetails: []api.PocketImageDetail{
				{
					PocketID:          "test-pocket-id-001",
					PocketImageURL:    "default-image-url1",
					PocketImageID:     "default-image-id1",
					PocketImageStatus: api.ImageStatus_SUCCESS,
				},
				{
					PocketID:          "test-pocket-id-002",
					PocketImageURL:    "default-image-url2",
					PocketImageID:     "default-image-id2",
					PocketImageStatus: api.ImageStatus_SUCCESS,
				},
			},
		},
	}
}

// GetCASAAccountSummaryWithPocketHavingSameImageResponse ...
//
//nolint:dupl
func GetCASAAccountSummaryWithPocketHavingSameImageResponse() *api.GetCASAAccountSummaryResponse {
	currency := utils.GetLocale().Currency
	return &api.GetCASAAccountSummaryResponse{
		TotalBalance: &api.Money{
			CurrencyCode: currency,
			Val:          70,
		},
		DailyPocketSummary: &api.DailyPocketSummary{
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          50,
			},
		},
		SavingsPocketSummary: &api.SavingsPocketSummary{
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          20,
			},
			Count:  2,
			Images: []string{"default-image-url1", "default-image-url1"},
			PocketImageDetails: []api.PocketImageDetail{
				{
					PocketID:          "test-pocket-id-001",
					PocketImageURL:    "default-image-url1",
					PocketImageID:     "default-image-id1",
					PocketImageStatus: api.ImageStatus_SUCCESS,
				},
				{
					PocketID:          "test-pocket-id-002",
					PocketImageURL:    "default-image-url1",
					PocketImageID:     "default-image-id1",
					PocketImageStatus: api.ImageStatus_SUCCESS,
				},
			},
		},
	}
}

// GoalCoreGetGoalWithCreationDateResponse ..
func GoalCoreGetGoalWithCreationDateResponse(now time.Time) *goalCore.GetGoalResponse {
	goal := &goalCore.GetGoalResponse{
		Goal: GoalCoreGoal(now, 40, goalCore.GoalStatus_EMPTY),
	}
	goal.Goal.TargetDate = now
	goal.Goal.CreationDate = now.AddDate(0, -1, 0)
	return goal
}

// SavingsPocketDetailResponse ...
func SavingsPocketDetailResponse(name string, goalDetail *api.GoalDetail, schedulerDetail *api.SchedulerDetail) *api.GetSavingsPocketDetailResponse {
	pocketDetail := SavingsPocketDetail(name, goalDetail, schedulerDetail)
	return &api.GetSavingsPocketDetailResponse{
		SavingsPocketDetail: &pocketDetail,
	}
}

// ListSavingsPocketDetailResponse ...
func ListSavingsPocketDetailResponse(now time.Time, name1, name2 string, goalDetail *api.GoalDetail) *api.ListSavingsPocketDetailResponse {
	currency := utils.GetLocale().Currency
	return &api.ListSavingsPocketDetailResponse{
		TotalBalance: &api.Money{
			CurrencyCode: currency,
			Val:          20,
		},
		SavingsPocketDetail: []api.SavingsPocketDetail{{
			ID:        name1,
			AccountID: "test-account-id",
			Name:      "Venice",
			Image: &api.Image{
				ID:     "default-image-id1",
				URL:    "default-image-url1",
				Status: api.ImageStatus_SUCCESS,
			},
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          20,
			},
			Status: api.AccountStatus_ACTIVE,
			GoalDetail: &api.GoalDetail{
				ID: "goal-id-1",
				TargetAmount: &api.Money{
					CurrencyCode: currency,
					Val:          40,
				},
				TargetDate:         now,
				Status:             api.GoalStatus_Status_OFFTRACK,
				OfftrackPercentage: 50,
				OntrackPercentage:  50,
				OutstandingBalance: &api.Money{
					CurrencyCode: currency,
					Val:          20,
				}}}, {
			ID:        name2,
			AccountID: "test-account-id",
			Name:      "Iphone",
			Image: &api.Image{
				ID:     "default-image-id2",
				URL:    "default-image-url2",
				Status: api.ImageStatus_SUCCESS,
			},
			Balance: &api.Money{
				CurrencyCode: currency,
				Val:          0,
			},
			Status: api.AccountStatus_ACTIVE,
			GoalDetail: &api.GoalDetail{
				ID: "goal-id-2",
				TargetAmount: &api.Money{
					CurrencyCode: currency,
					Val:          40,
				},
				TargetDate:         now,
				Status:             api.GoalStatus_Status_EMPTY,
				OfftrackPercentage: 100,
				OntrackPercentage:  0,
				OutstandingBalance: &api.Money{
					CurrencyCode: currency,
					Val:          40,
				},
			}}},
	}
}

// ListSavingsPocketDetailWithSchedulerResponse ...
// nolint: dupl, funlen
func ListSavingsPocketDetailWithSchedulerResponse(now time.Time, savingPocketNames []string, boostPocketNames []string, goalDetail *api.GoalDetail) *api.ListSavingsPocketDetailResponse {
	currency := utils.GetLocale().Currency
	savingsPocketDetails := []api.SavingsPocketDetail{{
		ID:        savingPocketNames[0],
		AccountID: "test-account-id",
		Name:      "Venice",
		Image: &api.Image{
			ID:     "default-image-id1",
			URL:    "default-image-url1",
			Status: api.ImageStatus_SUCCESS,
		},
		Balance: &api.Money{
			CurrencyCode: currency,
			Val:          20,
		},
		Status: api.AccountStatus_ACTIVE,
		GoalDetail: &api.GoalDetail{
			ID: "goal-id-1",
			TargetAmount: &api.Money{
				CurrencyCode: currency,
				Val:          40,
			},
			TargetDate:         now,
			Status:             api.GoalStatus_Status_OFFTRACK,
			OfftrackPercentage: 50,
			OntrackPercentage:  50,
			OutstandingBalance: &api.Money{
				CurrencyCode: currency,
				Val:          20,
			}},
		SchedulerDetail: &api.SchedulerDetail{
			SchedulerID:   "scheduler-id-1",
			SchedulerName: "scheduler-name-1",
			Status:        "REGISTERED",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          3000,
			},
			StartDate:     "2023-01-01",
			EndDate:       "2023-06-01",
			FrequencyType: "MONTHLY",
			RepeatCount:   6,
		},
		ProductVariantCode: "test-variant-1",
	}, {
		ID:        savingPocketNames[1],
		AccountID: "test-account-id",
		Name:      "Iphone",
		Image: &api.Image{
			ID:     "default-image-id2",
			URL:    "default-image-url2",
			Status: api.ImageStatus_SUCCESS,
		},
		Balance: &api.Money{
			CurrencyCode: currency,
			Val:          0,
		},
		Status: api.AccountStatus_ACTIVE,
		GoalDetail: &api.GoalDetail{
			ID: "goal-id-2",
			TargetAmount: &api.Money{
				CurrencyCode: currency,
				Val:          40,
			},
			TargetDate:         now,
			Status:             api.GoalStatus_Status_EMPTY,
			OfftrackPercentage: 100,
			OntrackPercentage:  0,
			OutstandingBalance: &api.Money{
				CurrencyCode: currency,
				Val:          40,
			},
		},
		SchedulerDetail: &api.SchedulerDetail{
			SchedulerID:   "scheduler-id-2",
			SchedulerName: "scheduler-name-2",
			Status:        "REGISTERED",
			Amount: &api.Money{
				CurrencyCode: "MYR",
				Val:          5000,
			},
			StartDate:     "2023-01-01",
			EndDate:       "2023-06-01",
			FrequencyType: "MONTHLY",
			RepeatCount:   6,
		},
		ProductVariantCode: "test-variant-1",
	}}

	// Hardcode two boost pockets instead of using a loop
	if len(boostPocketNames) > 0 {
		boostPockets := []api.SavingsPocketDetail{
			{
				ID:        boostPocketNames[0],
				AccountID: "test-account-id",
				Name:      "Venice",
				Image: &api.Image{
					ID:     "default-image-id3",
					URL:    "default-image-url3",
					Status: api.ImageStatus_SUCCESS,
				},
				Balance: &api.Money{
					CurrencyCode: currency,
					Val:          20,
				},
				Status:             api.AccountStatus_ACTIVE,
				ProductVariantCode: "test-variant-boost-pocket-2",
				// Boost pockets don't have goal details
				// Boost pockets don't have scheduler details
			},
			{
				ID:        boostPocketNames[1],
				AccountID: "test-account-id",
				Name:      "Iphone",
				Image: &api.Image{
					ID:     "default-image-id4",
					URL:    "default-image-url4",
					Status: api.ImageStatus_SUCCESS,
				},
				Balance: &api.Money{
					CurrencyCode: currency,
					Val:          0,
				},
				Status:             api.AccountStatus_ACTIVE,
				ProductVariantCode: "test-variant-boost-pocket-2",
				// Boost pockets don't have goal details
				// Boost pockets don't have scheduler details
			},
		}

		savingsPocketDetails = append(savingsPocketDetails, boostPockets...)
	}

	return &api.ListSavingsPocketDetailResponse{
		TotalBalance: &api.Money{
			CurrencyCode: currency,
			Val: lo.Reduce(savingsPocketDetails, func(agg int64, item api.SavingsPocketDetail, index int) int64 {
				agg += item.Balance.Val
				return agg
			}, 0),
		},
		SavingsPocketDetail: savingsPocketDetails,
	}
}

// GoalDetail ...
func GoalDetail(now time.Time, status api.GoalStatus_Status, offTrack, onTrack uint32, outStanding int64) *api.GoalDetail {
	currency := utils.GetLocale().Currency
	return &api.GoalDetail{
		ID: "goal-id",
		TargetAmount: &api.Money{
			CurrencyCode: currency,
			Val:          1234,
		},
		TargetDate:         now,
		Status:             status,
		OfftrackPercentage: offTrack,
		OntrackPercentage:  onTrack,
		OutstandingBalance: &api.Money{
			CurrencyCode: currency,
			Val:          outStanding,
		},
	}
}

// SavingsPocketDetail ...
func SavingsPocketDetail(name string, goalDetail *api.GoalDetail, schedulerDetail *api.SchedulerDetail) api.SavingsPocketDetail {
	currency := utils.GetLocale().Currency
	return api.SavingsPocketDetail{
		ID:        name,
		AccountID: "test-account-id",
		Name:      "Venice",
		Image: &api.Image{
			ID:     "default-image-id1",
			URL:    "default-image-url1",
			Status: api.ImageStatus_SUCCESS,
		},
		ImageSuggestions: []api.Image{
			{
				ID:  "default-image-id1",
				URL: "default-image-url1",
			},
			{
				ID:  "default-image-id2",
				URL: "default-image-url2",
			},
		},
		Balance: &api.Money{
			CurrencyCode: currency,
			Val:          20,
		},
		Status:          api.AccountStatus_ACTIVE,
		GoalDetail:      goalDetail,
		SchedulerDetail: schedulerDetail,
	}
}

// GoalCoreGetGoalsWithCreationDateResponse ..
func GoalCoreGetGoalsWithCreationDateResponse(now time.Time) *goalCore.GetGoalsResponse {
	goal := &goalCore.GetGoalsResponse{
		Goals: []goalCore.Goal{{
			ID:           "goal-id-1",
			AccountID:    []string{"test-pocket-id-001"},
			TargetAmount: 40,
			Currency:     goalCore.Currency(utils.GetLocale().Currency),
			TargetDate:   now,
			Status:       goalCore.GoalStatus_EMPTY,
			CreationDate: now.AddDate(0, -1, 0),
		},
			{
				ID:           "goal-id-2",
				AccountID:    []string{"test-pocket-id-002"},
				TargetAmount: 40,
				Currency:     goalCore.Currency(utils.GetLocale().Currency),
				TargetDate:   now,
				Status:       goalCore.GoalStatus_ONGOING,
				CreationDate: now.AddDate(0, -1, 0),
			}},
	}
	return goal
}

// GetGoalEstimateResponse ...
func GetGoalEstimateResponse(targetAmount, monthlyContribution, customerContribution, interestEarned int64) *api.GetGoalEstimateResponse {
	currency := utils.GetLocale().Currency
	return &api.GetGoalEstimateResponse{
		PocketID: "test-account-id",
		TargetAmount: &api.Money{
			CurrencyCode: currency,
			Val:          targetAmount,
		},
		EstimatedMonthlyContribution: &api.Money{
			CurrencyCode: currency,
			Val:          monthlyContribution,
		},
		CustomerTotalContribution: &api.Money{
			CurrencyCode: currency,
			Val:          customerContribution,
		},
		EstimatedInterestEarned: &api.Money{
			CurrencyCode: currency,
			Val:          interestEarned,
		},
	}
}

// GetImageDetailResponseFromHermes ...
func GetImageDetailResponseFromHermes() *hermes.GetDocumentsResponse {
	return &hermes.GetDocumentsResponse{
		Documents: []hermes.Document{
			{
				Id:           "default-image-id1",
				SafeID:       "test-safe-id",
				PresignedURL: "default-image-url1",
				FileType:     "test-image-type",
				Metadata: map[string]string{
					constants.PocketIDKey: "test-pocket-id-001",
				},
				CreatedBy: "test-safe-id",
			},
			{
				Id:           "default-image-id1",
				SafeID:       "test-safe-id",
				PresignedURL: "default-image-url1",
				FileType:     "test-image-type",
				Metadata: map[string]string{
					constants.PocketIDKey: "test-pocket-id-002",
				},
				CreatedBy: "test-safe-id",
			},
		},
	}
}

// ListImageDetailsResponseFromHermes ...
func ListImageDetailsResponseFromHermes(opts ...responseFunc) *hermes.GetDocumentsResponse {
	option := opt{}
	for _, opt := range opts {
		option = opt(option)
	}
	documents := &hermes.GetDocumentsResponse{
		Documents: []hermes.Document{
			{
				Id:           "default-image-id1",
				SafeID:       "test-safe-id-1",
				PresignedURL: "default-image-url1",
				FileType:     "test-image-type",
				Metadata:     map[string]string{},
				CreatedBy:    "test-safe-id-1",
			},
			{
				Id:           "default-image-id2",
				SafeID:       "test-safe-id-2",
				PresignedURL: "default-image-url2",
				FileType:     "test-image-type",
				Metadata:     map[string]string{},
				CreatedBy:    "test-safe-id-2",
			},
		},
		Pagination: &hermes.Pagination{},
	}
	if option.includeBoostPocket {
		boostPocketDocuments := []hermes.Document{
			{
				Id:           "default-image-id3",
				SafeID:       "test-safe-id-1",
				PresignedURL: "default-image-url3",
				FileType:     "test-image-type",
				Metadata:     map[string]string{},
				CreatedBy:    "test-safe-id-1",
			},
			{
				Id:           "default-image-id4",
				SafeID:       "test-safe-id-1",
				PresignedURL: "default-image-url4",
				FileType:     "test-image-type",
			},
		}
		documents.Documents = append(documents.Documents, boostPocketDocuments...)
	}
	return documents
}

// GetPocketTemplateResponseFromProductMaster ...
func GetPocketTemplateResponseFromProductMaster() *productMaster.GetPocketTemplateResponse {
	return &productMaster.GetPocketTemplateResponse{
		PocketTemplate: &productMaster.PocketTemplate{
			ID:   "test-pocket-template-id-1",
			Type: "test-template-type",
			Name: "test-name",
			Images: []productMaster.Image{
				{
					ID:  "default-image-id1",
					URL: "default-image-url1",
				},
				{
					ID:  "default-image-id2",
					URL: "default-image-url2",
				},
			},
			DefaultImage: &productMaster.Image{
				ID:  "default-image-id1",
				URL: "default-image-url1",
			},
		},
	}
}

// GetInterestParametersBySavingPocketProductVariantResponse ...
func GetInterestParametersBySavingPocketProductVariantResponse() *productMaster.GetInterestParametersByProductVariantResponse {
	return &productMaster.GetInterestParametersByProductVariantResponse{
		DepositsAccount: nil,
		SavingsPocket: &productMaster.SavingsPocketInterestParameters{
			InterestRateType: productMaster.InterestRateType_flat,
			FlatInterest: &productMaster.SavingsPocketFlatInterest{
				Rate:       8,
				Multiplier: 100,
			},
		},
	}
}

// GetAccountDetailsResponse ...
func GetAccountDetailsResponse() *api.GetAccountDetailsResponse {
	return &api.GetAccountDetailsResponse{
		AccountImageDetails: map[string]api.Image{"ID1": {ID: "m1", URL: "m1-url", Status: api.ImageStatus_SUCCESS}, "ID2": {ID: "m2", URL: "m2-url", Status: api.ImageStatus_SUCCESS}},
		AccountNameDetails:  map[string]string{"ID1": "A", "ID2": "B"},
	}
}

// GetAccountDetailsDuplicateImageIDResponse ...
func GetAccountDetailsDuplicateImageIDResponse() *api.GetAccountDetailsResponse {
	return &api.GetAccountDetailsResponse{
		AccountImageDetails: map[string]api.Image{"ID1": {ID: "m1", URL: "m1-url", Status: api.ImageStatus_SUCCESS}, "ID2": {ID: "m2", URL: "m2-url", Status: api.ImageStatus_SUCCESS}, "ID3": {ID: "m2", URL: "m2-url", Status: api.ImageStatus_SUCCESS}},
		AccountNameDetails:  map[string]string{"ID1": "A", "ID2": "B", "ID3": "C"},
	}
}

// GetAccountImageDetailsEmptyResponse ...
func GetAccountImageDetailsEmptyResponse() *api.GetAccountDetailsResponse {
	return &api.GetAccountDetailsResponse{
		AccountImageDetails: map[string]api.Image{"ID1": {ID: "m1", URL: "", Status: api.ImageStatus_PROCESSING}, "ID2": {ID: "m2", URL: "", Status: api.ImageStatus_PROCESSING}},
		AccountNameDetails:  map[string]string{"ID1": "A", "ID2": "B"},
	}
}

// UpdateSavingsPocketImageResponses ...
func UpdateSavingsPocketImageResponses() *api.UpdateSavingsPocketImageResponse {
	return &api.UpdateSavingsPocketImageResponse{
		PocketID: "test-pocket-id",
		ImageID:  "new-test-pocket-image",
	}
}

// ListSavingsPocketDetailEmptyResponse ...
func ListSavingsPocketDetailEmptyResponse() *api.ListSavingsPocketDetailResponse {
	currency := utils.GetLocale().Currency
	return &api.ListSavingsPocketDetailResponse{
		TotalBalance: &api.Money{
			Val:          0,
			CurrencyCode: currency,
		},
		SavingsPocketDetail: []api.SavingsPocketDetail{},
	}
}

// GetLendingAccountBankDetailsOKResponse ...
func GetLendingAccountBankDetailsOKResponse() *api.GetLendingAccountBankDetailsResponse {
	return &api.GetLendingAccountBankDetailsResponse{
		LendingAccountBankDetails: []api.LendingAccountBankDetail{{
			AccountID:          "test-account-id",
			BankCode:           "",
			BankName:           "GXBank",
			BranchCode:         "",
			BeneficiaryName:    "test-Name",
			Status:             "CLOSED",
			ProductVariantCode: constants.DefaultFlexiLoanLineOfCredit,
		}},
	}
}

// GetLookCifNumberResponse ...
func GetLookCifNumberResponse(cifNumber string) *customerMaster.LookupCIFNumberResponse {
	return &customerMaster.LookupCIFNumberResponse{
		CifNumber: cifNumber,
	}
}

// GetCASAAccountParametersResponse ...
func GetCASAAccountParametersResponse() *depositsExp.GetCASAAccountParametersResponse {
	return &depositsExp.GetCASAAccountParametersResponse{
		AccountID: "test-parent-id",
		Parameters: &depositsExp.CASAAccountParameters{
			BoostPocketAvailableLimit: &depositsExp.Money{
				CurrencyCode: "SGD",
				Val:          100000,
			},
		},
	}
}

// GetDepositAccountProductParameters ...
func GetDepositAccountProductParameters() *productMaster.ListEffectiveProductVariantParametersResponse {
	return &productMaster.ListEffectiveProductVariantParametersResponse{
		ProductVariantParameters: []productMaster.ProductVariantParameter{
			{
				ParameterKey:   "max_accounts_per_customer",
				ParameterValue: "1",
			},
			{
				ParameterKey:   "minimum_balance_placement",
				ParameterValue: "100",
			},
			{
				ParameterKey:   "maximum_balance_placement",
				ParameterValue: "1000",
			},
		},
	}
}
