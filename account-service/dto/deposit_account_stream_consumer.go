package dto

import (
	"time"

	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// DepositsAccountStreamEventDTO defines structure for consuming account creation event
type DepositsAccountStreamEventDTO struct {
	IdempotencyKey     string
	AccountID          string
	CifNumber          string
	SafeID             string
	ProductCode        string
	ProductVariantCode string
	ProductVersionID   string
	InstanceParams     map[string]string
	Status             string
	ErrorCode          string
	ErrorMessage       string
	OpeningTimestamp   *time.Time
	ClosingTimestamp   *time.Time
}

// DepositsAccountStatusUpdateDTO  defines structure for consuming account status update event from deposits core
type DepositsAccountStatusUpdateDTO struct {
	IdempotencyKey string
	AccountID      string
	AccountStatus  string
	Metadata       string
	ErrorMessage   string
}

// DepositsAccountStreamMetaData metadata
type DepositsAccountStreamMetaData struct {
	UpdatedBy           string
	MaturityDate        time.Time
	BonusInterestAmount *api.Money
	TotalBalance        *api.Money
}

// BPClosureNotificationStreamEventDTO defines struct for producing bp close notification event to deposits-exp
type BPClosureNotificationStreamEventDTO struct {
	EventType           string
	IdempotencyKey      string
	AccountID           string
	CifNumber           string
	PocketName          string
	Balance             *api.Money
	BonusInterestAmount *api.Money
	MaturityDate        time.Time
}
