package closecasaaccount

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"github.com/samber/lo"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/external/appian"
	appianDTO "gitlab.myteksi.net/bersama/core-banking/account-service/external/appian/dto"
	casaaudit "gitlab.myteksi.net/bersama/core-banking/account-service/logic/audit/casa"
	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/casa"
	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/workflow"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	commonctx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"
	accountService "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	xerrors "gitlab.myteksi.net/dbmy/core-banking/common/errors"
	"gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) publishAccountDetailEvent(
	ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(workflowLogTag, "invalid context passed in publishAccountDetailEvent state", utils.GetTraceID(ctx))
		return nil, workflow.ErrInvalidContext
	}
	var joinedErr error

	slog.FromContext(ctx).Info(workflowLogTag, "In publishAccountDetail state", utils.GetTraceID(ctx))
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(currCtx.Request.IdempotencyKey))
	nextCtx := currCtx.clone()
	dcAccount, err := w.DepositsCoreClient.GetCASAAccount(
		ctx, &api.GetCASAAccountRequest{AccountID: nextCtx.CustomerAccount.AccountID})
	if err != nil {
		joinedErr = xerrors.New("unable to get casa account from deposits-core", WorkflowError, err)
		slog.FromContext(ctx).Error(workflowLogTag, joinedErr.Error(), utils.GetTraceID(ctx))
		return nil, joinedErr
	}
	auditContext := commonctx.WithIdempotencyKey(ctx, currCtx.Request.IdempotencyKey)
	err = casa.PublishAccountDetailsWhileUpdateStatus(auditContext, &casa.UpdateStatusDto{
		Account:   nextCtx.UpdatedCustomerAccount,
		UpdatedBy: nextCtx.Request.UpdatedBy,
		Status:    nextCtx.UpdatedCustomerAccount.CurrentStatus,
	}, nextCtx.CustomerAccount.CifNumber,
		dcAccount.Account.InstanceParams, w.DepositsAccountDetailPublisher)

	if err != nil {
		joinedErr = xerrors.New("error when publishing account detail to kafka", WorkflowError, err)
		slog.FromContext(ctx).Warn(workflowLogTag, joinedErr.Error(), utils.GetTraceID(ctx))
		return nil, joinedErr
	}

	nextCtx.SetState(stPublishSuccessAccountStatusUpdateEvent)
	slog.FromContext(ctx).Info(workflowLogTag, "transition to stPublishSuccessAccountStatusUpdateEvent", utils.GetTraceID(ctx))

	return nextCtx, nil
}

func (w *WorkflowImpl) publishAccountStatusUpdateEvent(
	ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(workflowLogTag, "invalid context passed in publishAccountStatusUpdateEvent state", utils.GetTraceID(ctx))
		return nil, workflow.ErrInvalidContext
	}

	slog.FromContext(ctx).Info(workflowLogTag, "In publishAccountStatusUpdateEvent state", utils.GetTraceID(ctx))
	// ctx = slog.AddTagsToContext(ctx, tags.TraceID(currCtx.Request.IdempotencyKey))
	nextCtx := currCtx.clone()

	metadata := lo.Ternary(nextCtx.Metadata == nil, &ExecutionMetadata{}, nextCtx.Metadata)
	productVariantConfig := w.AppConfig.AccountServiceConfig.ProductVariantCodeConfig

	// callback after the actual work succeeded
	if currCtx.State == stSuccessPublished {
		if nextCtx.CustomerAccount.ProductVariantID == productVariantConfig.BoostPocket && metadata.Origin != servicename.CustomerPortal.ToString() {
			slog.FromContext(ctx).Info(workflowLogTag, "transition to stSuccessNotification", utils.GetTraceID(ctx))
			nextCtx.SetState(stSuccessNotification)
		} else {
			slog.FromContext(ctx).Info(workflowLogTag, "transition to stAccountClosurePublishCompleted", utils.GetTraceID(ctx))
			nextCtx.SetState(stAccountClosurePublishCompleted)
		}
		return nextCtx, nil
	}
	//

	isSuccessEvent := nextCtx.GetState() == stPublishSuccessAccountStatusUpdateEvent

	// publish audit log for pocket account
	if !metadata.IsAuditLogSent && nextCtx.CustomerAccount.ProductVariantID == productVariantConfig.SavingsPocket {
		auditErr := w.publishAuditLog(ctx, nextCtx, isSuccessEvent)
		if auditErr != nil {
			slog.FromContext(ctx).Error(
				workflowLogTag, fmt.Sprintf("failed to audit account status update: %s", auditErr.Error()), utils.GetTraceID(ctx),
			)
			return nil, auditErr
		}
		metadata.IsAuditLogSent = true
	}

	// notify appian status only when the request is origin from customer portal
	if metadata.Origin == servicename.CustomerPortal.ToString() {
		if appianErr := w.publishAccountStatusUpdateToAppian(ctx, nextCtx, isSuccessEvent); appianErr != nil {
			return nil, appianErr
		}
	}

	if isSuccessEvent {
		nextCtx.SetState(stSuccessPublished)
		slog.FromContext(ctx).Info(workflowLogTag, "transition to stAccountStatusUpdateEventPublished", utils.GetTraceID(ctx))
	} else {
		nextCtx.SetState(stFailurePublished)
		slog.FromContext(ctx).Info(workflowLogTag, "transition to stAccountStatusUpdateEventFailed", utils.GetTraceID(ctx))
	}

	return nextCtx, nil
}

func (w *WorkflowImpl) publishAuditLog(ctx context.Context, executionData *ExecutionData, isSuccessEvent bool) error {
	metadata := lo.Ternary(executionData.Metadata == nil, &ExecutionMetadata{}, executionData.Metadata)
	var err error
	if !isSuccessEvent {
		err = lo.Ternary(executionData.FailureReason == "", errors.New("unknown error"),
			errors.New(executionData.FailureReason))
	}
	auditErr := w.AuditEventHandler.Handle(ctx, constants.CustJournalClosePocket, &casaaudit.Event{
		AccountID: executionData.CustomerAccount.AccountID,
		Origin:    metadata.Origin,
		Err:       err,
		SafeID:    executionData.SafeID,
		Payload: map[string]interface{}{
			casaaudit.ClosureReasonKey.ToString(): executionData.Request.Reasons,
		},
	})
	if auditErr != nil {
		slog.FromContext(ctx).Error(
			workflowLogTag, fmt.Sprintf("failed to audit account status update: %s", auditErr.Error()), utils.GetTraceID(ctx),
		)
		return auditErr
	}
	return nil
}

func (w *WorkflowImpl) publishAccountStatusUpdateToAppian(ctx context.Context, executionData *ExecutionData, isSuccessEvent bool) error {
	accessToken, err := w.getAppianAccessToken(ctx)
	if err != nil {
		slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("error getting appian access token: %s", err.Error()), utils.GetTraceID(ctx))
		return err
	}

	authHeader := fmt.Sprintf("Bearer %s", accessToken)
	ctx = commonctx.WithHTTPHeader(ctx, constants.Authorization, authHeader)
	casaAccountStatusUpdateReq := &appianDTO.CASAAccountStatusUpdateRequest{
		ReferenceID:        executionData.Request.IdempotencyKey,
		AccountID:          executionData.CustomerAccount.AccountID,
		ProductVariantCode: executionData.CustomerAccount.ProductVariantID,
		Status: lo.Ternary(
			lo.IsNotEmpty(executionData.FailureReason) || !isSuccessEvent,
			string(accountService.RequestStatus_Status_FAILED),
			string(accountService.RequestStatus_Status_SUCCESS),
		),
		FailureReason: executionData.FailureReason,
		UpdatedBy:     executionData.Request.UpdatedBy,
	}
	response, err := w.AppianClient.PostCASAAccountStatusUpdate(ctx, casaAccountStatusUpdateReq)
	if err != nil {
		slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("error calling appian PostCASAAccountStatusUpdate details api: %s", err.Error()), utils.GetTraceID(ctx))
		return workflow.ErrUpdateAccountStatus
	}
	slog.FromContext(ctx).Info(workflowLogTag, fmt.Sprintf("PostCASAAccountStatusUpdate response from appian: %s", utils.ToJSON(response)), utils.GetTraceID(ctx))

	return nil
}

func (w *WorkflowImpl) getAppianAccessToken(ctx context.Context) (string, error) {
	tokenReq := &appianDTO.GetAccessTokenRequest{
		ClientID:     w.AppConfig.Appian.RegisteredClientID,
		ClientSecret: w.AppConfig.Appian.RegisteredClientSecret,
		GrantType:    w.AppConfig.Appian.GrantType,
	}
	tokenResp, tokenErr := w.AppianClient.GetAccessTokenV2(ctx, tokenReq)
	if tokenErr != nil {
		errObj, ok := tokenErr.(appian.ResponseError)
		if ok && errObj.ErrorReason == appian.ErrInvalidClient {
			slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("invalid client error from appian: %s", errObj.ErrorReason))
			return "", tokenErr
		}
		slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("Error getting access token from appian: %s", tokenErr.Error()))
		return "", tokenErr
	}

	slog.FromContext(ctx).Info(workflowLogTag, "Successfully fetched access token from appian")
	return tokenResp.AccessToken, nil
}

func (w *WorkflowImpl) publishNotification(
	ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(workflowLogTag, "invalid context passed in publishNotification state", utils.GetTraceID(ctx))
		return nil, workflow.ErrInvalidContext
	}
	slog.FromContext(ctx).Info(workflowLogTag, "In publishNotification state", utils.GetTraceID(ctx))
	nextCtx := currCtx.clone()
	// success callback
	if currCtx.State == stSuccessNotified {
		slog.FromContext(ctx).Info(workflowLogTag, "transition to stAccountClosurePublishCompleted", utils.GetTraceID(ctx))
		nextCtx.SetState(stAccountClosurePublishCompleted)
		return nextCtx, nil
	}
	metadata := currCtx.AccountStatusUpdateEvent.Metadata
	metaDataDto := dto.DepositsAccountStreamMetaData{}
	unmarshalErr := json.Unmarshal([]byte(metadata), &metaDataDto)
	if unmarshalErr != nil {
		slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("error in deposits event metadata: %s", unmarshalErr.Error()))
		return nil, unmarshalErr
	}
	// get pocket name
	pockets, err := w.Store.GetPockets(ctx, []string{nextCtx.CustomerAccount.AccountID})
	if err != nil {
		slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("failed to get pockets: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}
	if len(pockets) == 0 {
		slog.FromContext(ctx).Warn(workflowLogTag, "pocket not found", utils.GetTraceID(ctx))
		return nil, errors.New("pocket not found")
	}
	accountDTO := &dto.BPClosureNotificationStreamEventDTO{}
	accountDTO.EventType = constants.BPClosureNotification
	accountDTO.IdempotencyKey = currCtx.Request.IdempotencyKey
	accountDTO.AccountID = currCtx.CustomerAccount.AccountID
	accountDTO.CifNumber = currCtx.CustomerAccount.CifNumber
	accountDTO.PocketName = pockets[0].Name
	accountDTO.BonusInterestAmount = metaDataDto.BonusInterestAmount
	accountDTO.Balance = metaDataDto.TotalBalance
	accountDTO.MaturityDate = metaDataDto.MaturityDate

	err = w.DepositsNotificationPublisher.Publish(ctx, accountDTO)
	if err != nil {
		slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("failed to send notification: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}
	slog.FromContext(ctx).Info(workflowLogTag, "transition to stSuccessNotified", utils.GetTraceID(ctx))
	nextCtx.SetState(stSuccessNotified)
	return nextCtx, nil
}
