package closecasaaccount

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/external/appian"
	appianDTO "gitlab.myteksi.net/bersama/core-banking/account-service/external/appian/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/external/appian/mocks"
	"gitlab.myteksi.net/bersama/core-banking/account-service/kafka/publisher"
	casaaudit "gitlab.myteksi.net/bersama/core-banking/account-service/logic/audit/casa"
	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/workflow"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	customermaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositapi "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
	depositsmock "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api/mock"
)

func Test_PublishAccountDetailEvent(t *testing.T) {
	scenarios := []struct {
		testDesc   string
		execData   we.ExecutionData
		want       we.ExecutionData
		publishErr error
		wantErr    error
	}{
		{
			testDesc: "Invalid context",
			wantErr:  workflow.ErrInvalidContext,
		},
		{
			testDesc: "Happy path",
			execData: &ExecutionData{
				State: stPublishAccountDetailEvent,
				Request: &CloseAccountRequest{
					IdempotencyKey: "abc",
					Account: &storage.CustomerAccount{
						AccountID: "test-account",
					},
					Status: api.AccountStatus_CLOSED,
				},
				CustomerAccount: &storage.CustomerAccount{
					CifNumber: "test-cif",
				},
				UpdatedCustomerAccount: &storage.CustomerAccount{
					CurrentStatus: api.AccountStatus_CLOSED,
				},
			},
			want: &ExecutionData{
				State: stPublishSuccessAccountStatusUpdateEvent,
				Request: &CloseAccountRequest{
					IdempotencyKey: "abc",
					Account: &storage.CustomerAccount{
						AccountID: "test-account",
					},
					Status: api.AccountStatus_CLOSED,
				},
				CustomerAccount: &storage.CustomerAccount{
					CifNumber: "test-cif",
				},
				UpdatedCustomerAccount: &storage.CustomerAccount{
					CurrentStatus: api.AccountStatus_CLOSED,
				},
			},
		},
	}

	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.testDesc, func(t *testing.T) {
			mockKafkaClient := kafkawriter.MockClient{}
			mockDepositClient := &depositsmock.DepositsCore{}
			mockDepositClient.On("GetCASAAccount", mock.Anything, mock.Anything).Return(
				&depositapi.GetCASAAccountResponse{Account: &depositapi.CASAAccount{
					InstanceParams: make(map[string]string),
				}}, nil,
			)
			mockAccDetailPublisher := &publisher.DepositsAccountDetailPublisher{
				KafkaWriterClient: &mockKafkaClient,
			}
			mockKafkaClient.On("Save", mock.Anything).Return(tt.publishErr).Once()
			w := WorkflowImpl{
				DepositsAccountDetailPublisher: mockAccDetailPublisher,
				StatsD:                         statsd.NewNoop(),
				DepositsCoreClient:             mockDepositClient,
			}

			got, err := w.publishAccountDetailEvent(context.Background(), "", tt.execData, "")
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
			} else {
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func Test_PublishAccountStatusUpdateEvent(t *testing.T) {
	type args struct {
		execData we.ExecutionData
		params   interface{}
	}

	tests := []struct {
		name                            string
		args                            args
		auditPublisherErr               error
		postCASAAccountStatusUpdateErr  error
		postCASAAccountStatusUpdateResp *appianDTO.CASAAccountStatusUpdateResponse
		want                            *ExecutionData
		wantErr                         error
		skipAuditLog                    bool
		skipAppian                      bool
	}{
		{
			name: "should publish audit log",
			args: args{
				execData: &ExecutionData{
					State: stPublishSuccessAccountStatusUpdateEvent,
					CustomerAccount: &storage.CustomerAccount{
						ProductVariantID: "SAVINGS_POCKET",
					},
					Request: &CloseAccountRequest{},
				},
			},
			postCASAAccountStatusUpdateResp: &appianDTO.CASAAccountStatusUpdateResponse{},
			want: &ExecutionData{
				State: stSuccessPublished,
			},
		},
		{
			name: "should not publish audit log if its not savings pocket",
			args: args{
				execData: &ExecutionData{
					State: stPublishSuccessAccountStatusUpdateEvent,
					CustomerAccount: &storage.CustomerAccount{
						ProductVariantID: "NOT_SAVINGS_POCKET",
					},
					Request: &CloseAccountRequest{},
				},
			},
			postCASAAccountStatusUpdateResp: &appianDTO.CASAAccountStatusUpdateResponse{},
			want: &ExecutionData{
				State:   stSuccessPublished,
				Request: &CloseAccountRequest{},
			},
			skipAuditLog: true,
		},
		{
			name: "should not send appian notification if its not origin from customer portal",
			args: args{
				execData: &ExecutionData{
					State: stPublishSuccessAccountStatusUpdateEvent,
					CustomerAccount: &storage.CustomerAccount{
						ProductVariantID: "SAVINGS_POCKET",
					},
					Metadata: &ExecutionMetadata{
						Origin: "NOT_CUSTOMER_PORTAL",
					},
					Request: &CloseAccountRequest{
						IdempotencyKey: "abc",
						Account: &storage.CustomerAccount{
							AccountID:        "test-account",
							ProductVariantID: "SAVINGS_POCKET",
						},
						UpdatedBy: "test-user",
					},
				},
			},
			postCASAAccountStatusUpdateResp: &appianDTO.CASAAccountStatusUpdateResponse{},
			want: &ExecutionData{
				State: stSuccessPublished,
			},
			skipAppian: true,
		},
		{
			name: "should remain same state on audit log error",
			args: args{
				execData: &ExecutionData{
					State: stPublishSuccessAccountStatusUpdateEvent,
					CustomerAccount: &storage.CustomerAccount{
						ProductVariantID: "SAVINGS_POCKET",
					},
					Request: &CloseAccountRequest{},
				},
			},
			postCASAAccountStatusUpdateResp: &appianDTO.CASAAccountStatusUpdateResponse{},
			auditPublisherErr:               assert.AnError,
			wantErr:                         assert.AnError,
		},
		{
			name: "should remain same state on appian notification error",
			args: args{
				execData: &ExecutionData{
					State: stPublishSuccessAccountStatusUpdateEvent,
					CustomerAccount: &storage.CustomerAccount{
						ProductVariantID: "NOT_SAVINGS_POCKET",
					},
					Metadata: &ExecutionMetadata{
						Origin: "customer-portal",
					},
					Request: &CloseAccountRequest{
						IdempotencyKey: "abc",
						Account: &storage.CustomerAccount{
							AccountID:        "test-account",
							ProductVariantID: "SAVINGS_POCKET",
						},
						UpdatedBy: "test-user",
					},
				},
			},
			postCASAAccountStatusUpdateResp: &appianDTO.CASAAccountStatusUpdateResponse{},
			postCASAAccountStatusUpdateErr:  assert.AnError,
			wantErr:                         assert.AnError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockAppianClient := &mocks.Appian{}
			mockAppianClient.On("GetAccessTokenV2", mock.Anything, mock.Anything).Return(&appianDTO.GetAccessTokenResponse{
				AccessToken: "",
				TokenType:   "",
				ExpiresIn:   0,
			}, nil)
			mockAppianClient.On("PostCASAAccountStatusUpdate", mock.Anything, mock.Anything).Return(tt.postCASAAccountStatusUpdateResp, tt.postCASAAccountStatusUpdateErr)
			mockAuditHandler := &casaaudit.MockHandler{}
			mockAuditHandler.On("Handle", mock.Anything, mock.Anything, mock.Anything).Return(tt.auditPublisherErr)

			w := &WorkflowImpl{
				StatsD: statsd.NewNoop(),
				AppConfig: &config.AppConfig{
					Appian: config.Appian{
						Config: &appian.Config{
							RegisteredClientID:     "test-client-id",
							RegisteredClientSecret: "test-secret-id",
							GrantType:              "test-grant",
						},
					},
					AccountServiceConfig: config.AccountServiceConfig{
						ProductVariantCodeConfig: config.ProductVariantCodeConfig{
							SavingsPocket: "SAVINGS_POCKET",
						},
					},
				},
				AppianClient:      mockAppianClient,
				AuditEventHandler: mockAuditHandler,
			}

			got, err := w.publishAccountStatusUpdateEvent(context.Background(), "", tt.args.execData, tt.args.params)

			if tt.skipAuditLog {
				mockAuditHandler.AssertNotCalled(t, "Handle")
			}

			if tt.skipAppian {
				mockAppianClient.AssertNotCalled(t, "PostCASAAccountStatusUpdate")
			}

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Nil(t, got)
			}
			if tt.want != nil {
				assert.Equal(t, tt.want.GetState(), got.GetState())
			}
		})
	}
}

func Test_PublishNotification(t *testing.T) {
	tests := []struct {
		name                string
		execData            we.ExecutionData
		metadataJSON        string
		getPocketsErr       error
		pockets             []*storage.Pocket
		getCustomerErr      error
		customerResponse    *customermaster.GetCustomerByCIFNumberResponse
		sendNotificationErr error
		want                *ExecutionData
		wantErr             error
	}{
		{
			name: "Success callback",
			execData: &ExecutionData{
				State: stSuccessNotified,
				Request: &CloseAccountRequest{
					IdempotencyKey: "idempotency-key",
				},
			},
			want: &ExecutionData{
				State: stAccountClosurePublishCompleted,
			},
		},
		{
			name: "Error unmarshaling metadata",
			execData: &ExecutionData{
				AccountStatusUpdateEvent: &dto.DepositsAccountStatusUpdateDTO{
					Metadata: "invalid json",
				},
				Request: &CloseAccountRequest{
					IdempotencyKey: "idempotency-key",
				},
			},
			wantErr: errors.New("invalid character 'i' looking for beginning of value"),
		},
		{
			name: "Error getting pockets",
			execData: &ExecutionData{
				AccountStatusUpdateEvent: &dto.DepositsAccountStatusUpdateDTO{
					Metadata: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
				},
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account",
				},
				Request: &CloseAccountRequest{
					IdempotencyKey: "idempotency-key",
				},
			},
			metadataJSON:  `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
			getPocketsErr: errors.New("error getting pockets"),
			wantErr:       errors.New("error getting pockets"),
		},
		{
			name: "Pocket not found",
			execData: &ExecutionData{
				AccountStatusUpdateEvent: &dto.DepositsAccountStatusUpdateDTO{
					Metadata: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
				},
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account",
				},
				Request: &CloseAccountRequest{
					IdempotencyKey: "idempotency-key",
				},
			},
			metadataJSON: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
			pockets:      []*storage.Pocket{},
			wantErr:      errors.New("pocket not found"),
		},
		{
			name: "Error sending notification",
			execData: &ExecutionData{
				AccountStatusUpdateEvent: &dto.DepositsAccountStatusUpdateDTO{
					Metadata: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
				},
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account",
					CifNumber: "test-cif",
				},
				SafeID: "test-safe-id",
				Request: &CloseAccountRequest{
					IdempotencyKey: "idempotency-key",
				},
			},
			metadataJSON: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
			pockets: []*storage.Pocket{
				{
					Name: "Test Pocket",
				},
			},
			customerResponse: &customermaster.GetCustomerByCIFNumberResponse{
				Customer: &customermaster.CustomerData{
					Data: json.RawMessage(`{"PreferredName":"Test User", "ID":"test-safe-id"}`),
				},
			},
			sendNotificationErr: errors.New("error sending notification"),
			wantErr:             errors.New("error sending notification"),
		},
		{
			name: "Success",
			execData: &ExecutionData{
				AccountStatusUpdateEvent: &dto.DepositsAccountStatusUpdateDTO{
					Metadata: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
				},
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account",
					CifNumber: "test-cif",
				},
				SafeID: "test-safe-id",
				Request: &CloseAccountRequest{
					IdempotencyKey: "idempotency-key",
				},
			},
			metadataJSON: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
			pockets: []*storage.Pocket{
				{
					Name: "Test Pocket",
				},
			},
			customerResponse: &customermaster.GetCustomerByCIFNumberResponse{
				Customer: &customermaster.CustomerData{
					Data: json.RawMessage(`{"PreferredName":"Test User", "ID":"test-safe-id"}`),
				},
			},
			want: &ExecutionData{
				AccountStatusUpdateEvent: &dto.DepositsAccountStatusUpdateDTO{
					Metadata: `{"TotalBalance":{"val":1000, "currencyCode": "MYR"}}`,
				},
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account",
					CifNumber: "test-cif",
				},
				SafeID: "test-safe-id",
				State:  stSuccessNotified,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockStore := &storage.MockDatabaseStore{}
			mockNotificationPublisher := &publisher.MockPublisher{}

			// Set up mock expectations
			if tt.metadataJSON != "" {
				var metaDataDto dto.DepositsAccountStreamMetaData
				err := json.Unmarshal([]byte(tt.metadataJSON), &metaDataDto)
				assert.NoError(t, err)
			}
			execData, ok := tt.execData.(*ExecutionData)
			if ok && execData.CustomerAccount != nil {
				mockStore.On("GetPockets", mock.Anything, []string{execData.CustomerAccount.AccountID}).
					Return(tt.pockets, tt.getPocketsErr)

				mockNotificationPublisher.On("Publish", mock.Anything, mock.Anything).
					Return(tt.sendNotificationErr)

				mockStore.On("GetPockets", mock.Anything, []string{execData.CustomerAccount.AccountID}).
					Return(tt.pockets, tt.getPocketsErr)
			}

			// Create the workflow implementation
			w := &WorkflowImpl{
				Store: mockStore,
				AppConfig: &config.AppConfig{
					Locale: config.Locale{
						Currency: "MYR",
					},
				},
				DepositsNotificationPublisher: mockNotificationPublisher,
				StatsD:                        statsd.NewNoop(),
			}

			// Call the method
			got, err := w.publishNotification(context.Background(), "", tt.execData, nil)

			// Assert the result
			if tt.wantErr != nil {
				assert.Error(t, err)
				if err != nil {
					assert.Equal(t, tt.wantErr.Error(), err.Error())
				}
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want.GetState(), got.GetState())
			}
		})
	}
}

func Test_PublishAuditLog(t *testing.T) {
	tests := []struct {
		name           string
		executionData  *ExecutionData
		isSuccessEvent bool
		auditErr       error
		wantErr        bool
		wantPayload    map[string]interface{}
	}{
		{
			name: "Success case with no failure reason",
			executionData: &ExecutionData{
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account-id",
				},
				SafeID:        "test-safe-id",
				FailureReason: "",
				Metadata: &ExecutionMetadata{
					Origin: "test-origin",
				},
				Request: &CloseAccountRequest{
					Reasons: []string{"close-reason-1", "close-reason-2"},
				},
			},
			isSuccessEvent: true,
			auditErr:       nil,
			wantErr:        false,
			wantPayload: map[string]interface{}{
				casaaudit.ClosureReasonKey.ToString(): []string{"close-reason-1", "close-reason-2"},
			},
		},
		{
			name: "Success case with failure reason",
			executionData: &ExecutionData{
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account-id",
				},
				SafeID:        "test-safe-id",
				FailureReason: "some-failure-reason",
				Metadata: &ExecutionMetadata{
					Origin: "test-origin",
				},
				Request: &CloseAccountRequest{
					Reasons: []string{"close-reason-1", "close-reason-2"},
				},
			},
			isSuccessEvent: true,
			auditErr:       nil,
			wantErr:        false,
			wantPayload: map[string]interface{}{
				casaaudit.ClosureReasonKey.ToString(): []string{"close-reason-1", "close-reason-2"},
			},
		},
		{
			name: "Success case with no metadata",
			executionData: &ExecutionData{
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account-id",
				},
				SafeID:        "test-safe-id",
				FailureReason: "",
				Metadata:      nil,
				Request: &CloseAccountRequest{
					Reasons: []string{"close-reason-1", "close-reason-2"},
				},
			},
			isSuccessEvent: true,
			auditErr:       nil,
			wantErr:        false,
			wantPayload: map[string]interface{}{
				casaaudit.ClosureReasonKey.ToString(): []string{"close-reason-1", "close-reason-2"},
			},
		},
		{
			name: "Failure case - audit handler returns error",
			executionData: &ExecutionData{
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account-id",
				},
				SafeID:        "test-safe-id",
				FailureReason: "",
				Metadata: &ExecutionMetadata{
					Origin: "test-origin",
				},
				Request: &CloseAccountRequest{
					Reasons: []string{"close-reason-1", "close-reason-2"},
				},
			},
			isSuccessEvent: true,
			auditErr:       errors.New("audit handler error"),
			wantErr:        true,
			wantPayload: map[string]interface{}{
				casaaudit.ClosureReasonKey.ToString(): []string{"close-reason-1", "close-reason-2"},
			},
		},
		{
			name: "Success case - failure event with failure reason",
			executionData: &ExecutionData{
				CustomerAccount: &storage.CustomerAccount{
					AccountID: "test-account-id",
				},
				SafeID:        "test-safe-id",
				FailureReason: "failure-reason",
				Metadata: &ExecutionMetadata{
					Origin: "test-origin",
				},
				Request: &CloseAccountRequest{
					Reasons: []string{"close-reason-1", "close-reason-2"},
				},
			},
			isSuccessEvent: false,
			auditErr:       nil,
			wantErr:        false,
			wantPayload: map[string]interface{}{
				casaaudit.ClosureReasonKey.ToString(): []string{"close-reason-1", "close-reason-2"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock audit handler
			mockAuditHandler := &casaaudit.MockHandler{}

			// Set up mock expectations
			mockAuditHandler.On("Handle", mock.Anything, constants.CustJournalClosePocket, mock.MatchedBy(func(event *casaaudit.Event) bool {
				// Verify the event properties
				assert.Equal(t, tt.executionData.CustomerAccount.AccountID, event.AccountID)

				metadata := lo.Ternary(tt.executionData.Metadata == nil, &ExecutionMetadata{}, tt.executionData.Metadata)
				assert.Equal(t, metadata.Origin, event.Origin)

				assert.Equal(t, tt.executionData.SafeID, event.SafeID)

				// Verify payload
				closureReasons, ok := event.Payload[casaaudit.ClosureReasonKey.ToString()]
				assert.True(t, ok)
				assert.Equal(t, tt.wantPayload[casaaudit.ClosureReasonKey.ToString()], closureReasons)

				// Verify error
				if !tt.isSuccessEvent {
					if tt.executionData.FailureReason == "" {
						assert.Equal(t, "unknown error", event.Err.Error())
					} else {
						assert.Equal(t, tt.executionData.FailureReason, event.Err.Error())
					}
				} else {
					assert.Nil(t, event.Err)
				}

				return true
			})).Return(tt.auditErr)

			// Create the workflow implementation
			w := &WorkflowImpl{
				AuditEventHandler: mockAuditHandler,
			}

			// Call the method
			err := w.publishAuditLog(context.Background(), tt.executionData, tt.isSuccessEvent)

			// Assert the result
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify mock was called
			mockAuditHandler.AssertCalled(t, "Handle", mock.Anything, constants.CustJournalClosePocket, mock.Anything)
		})
	}
}
