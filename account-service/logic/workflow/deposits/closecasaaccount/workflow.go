// Package closecasaaccount has workflow for closing an account's status
package closecasaaccount

import (
	"context"
	"fmt"
	"time"

	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"github.com/samber/lo"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/external/appian"
	"gitlab.myteksi.net/bersama/core-banking/account-service/kafka/publisher"
	casaaudit "gitlab.myteksi.net/bersama/core-banking/account-service/logic/audit/casa"
	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/workflow"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	we "gitlab.myteksi.net/dakota/workflowengine"
	commonwf "gitlab.myteksi.net/dbmy/core-banking/common/workflow"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
)

const (
	// WorkflowID ...
	WorkflowID     = "close_casa_account_status"
	workflowLogTag = constants.CloseCASAAccountStatusV2LogTag
)

const (
	stInit = we.StateInit

	stAccountStatusUpdateProcessing      = we.State(200)
	stAccountStatusUpdateStreamPersisted = we.State(205)

	stUpdateCustomerAccountDetails        = we.State(210)
	stPersistCustomerAccountStatusHistory = we.State(215)

	stAccountStatusUpdateCompleted = we.State(220)

	stPublishAccountDetailEvent              = we.State(225)
	stSuccessNotification                    = we.State(230)
	stPublishSuccessAccountStatusUpdateEvent = we.State(235)

	stAccountStatusUpdateFailed             = we.State(500)
	stPublishFailedAccountStatusUpdateEvent = we.State(505)
	stFailedNotification                    = we.State(510)

	stSuccessPublished               = we.State(905)
	stSuccessNotified                = we.State(910)
	stFailurePublished               = we.State(915)
	stFailureNotified                = we.State(920)
	stAccountClosurePublishCompleted = we.State(930)
)

const (
	evNoNeed                           = we.EventNoNeed
	evStart                            = we.Event(100)
	evPersistAccountStatusUpdateStream = we.Event(212)
)

var (
	wfInit    = we.InitExecution
	wfGet     = we.GetExecution
	wfExecute = we.Execute
)

// WorkflowImpl implements close account workflow
type WorkflowImpl struct {
	StatsD                         statsd.Client             `inject:"statsD"`
	AppConfig                      *config.AppConfig         `inject:"config"`
	Store                          storage.DatabaseStore     `inject:"storage.Store"`
	DepositsCoreClient             depositsCore.DepositsCore `inject:"client.depositsCore"`
	DepositsAccountDetailPublisher publisher.Publisher       `inject:"publisher.depositsAccountDetail"`
	DepositsNotificationPublisher  publisher.Publisher       `inject:"publisher.depositsNotificationEvent"`
	AuditEventHandler              casaaudit.Handler         `inject:"audit.handler"`
	Logger                         slog.YallLogger           `inject:"logger"`
	AppianClient                   appian.Appian             `inject:"client.appian"`
}

// Register ...
func (w *WorkflowImpl) Register() {
	updateCASAAccountStatusWorkflow := we.NewWorkflow(WorkflowID, func() we.ExecutionData {
		return &ExecutionData{}
	})

	var transactionalRetryOption *we.TransitionOptions
	if w.AppConfig.WorkflowRetryConfig.UpdateCASAAccountStatusV2RetryPolicy != nil &&
		w.AppConfig.WorkflowRetryConfig.UpdateCASAAccountStatusV2RetryPolicy.TransactionalRetryOption != nil {
		// used for time-sensitive transactional states
		txnRetryOption := w.AppConfig.WorkflowRetryConfig.UpdateCASAAccountStatusV2RetryPolicy.TransactionalRetryOption
		transactionalRetryOption = &we.TransitionOptions{
			RetryPolicy: &we.RetryPolicy{
				Interval:    txnRetryOption.IntervalInSeconds,
				MaxAttempts: txnRetryOption.MaxAttempt},
		}
	}
	wrapperFun := func(name string, f we.Function) we.Function {
		return withSafeIDAndServiceID(commonwf.WithLogger(
			w.Logger, withTraceID(
				withMonitoring(name, f),
			),
		))
	}

	transitions := []*commonwf.Transition{
		commonwf.NewTransition().From(stInit).To(stAccountStatusUpdateProcessing).On(
			evStart).Run(wrapperFun("updateCASAAccountStatus", w.updateCASAAccountStatus), transactionalRetryOption),
		commonwf.NewTransition().From(stAccountStatusUpdateProcessing).To(stAccountStatusUpdateStreamPersisted).On(
			evPersistAccountStatusUpdateStream).Run(wrapperFun("persistAccountStatusUpdateStream", w.persistAccountStatusUpdateStream), nil),
		commonwf.NewTransition().From(stAccountStatusUpdateStreamPersisted).To(stUpdateCustomerAccountDetails, stAccountStatusUpdateFailed).On(
			evNoNeed).Run(wrapperFun("completeAccountStatusUpdate", w.completeAccountStatusUpdate), nil),

		// persist details in db
		commonwf.NewTransition().From(stUpdateCustomerAccountDetails).To(stPersistCustomerAccountStatusHistory).On(
			evNoNeed).Run(wrapperFun("updateCustomerAccountDetails", w.updateCustomerAccountDetails), transactionalRetryOption),
		commonwf.NewTransition().From(stPersistCustomerAccountStatusHistory).To(stAccountStatusUpdateCompleted).On(
			evNoNeed).Run(wrapperFun("persistCustomerAccountStatusHistory", w.persistCustomerAccountStatusHistory), transactionalRetryOption),

		// success handling
		commonwf.NewTransition().From(stAccountStatusUpdateCompleted).To(stPublishAccountDetailEvent, stPublishSuccessAccountStatusUpdateEvent).On(
			evNoNeed).Run(wrapperFun("accountSuccessUpdateHandling", w.accountSuccessUpdateHandling), transactionalRetryOption),
		commonwf.NewTransition().From(stPublishAccountDetailEvent).To(stPublishSuccessAccountStatusUpdateEvent).On(
			evNoNeed).Run(wrapperFun("publishAccountDetailEvent", w.publishAccountDetailEvent), transactionalRetryOption),
		commonwf.NewTransition().From(stPublishSuccessAccountStatusUpdateEvent).To(stSuccessPublished).On(
			evNoNeed).Run(wrapperFun("publishAccountStatusUpdateEvent", w.publishAccountStatusUpdateEvent), transactionalRetryOption),
		commonwf.NewTransition().From(stSuccessPublished).To(stSuccessNotification, stAccountClosurePublishCompleted).On(
			evNoNeed).Run(wrapperFun("postPublishAccountStatusUpdateEvent", w.publishAccountStatusUpdateEvent), transactionalRetryOption),

		commonwf.NewTransition().From(stSuccessNotification).To(stSuccessNotified).On(
			evNoNeed).Run(wrapperFun("publishNotification", w.publishNotification), transactionalRetryOption),
		commonwf.NewTransition().From(stSuccessNotified).To(stFailedNotification, stAccountClosurePublishCompleted).On(
			evNoNeed).Run(wrapperFun("postPublishNotification", w.publishNotification), transactionalRetryOption),

		// failure handling
		commonwf.NewTransition().From(stAccountStatusUpdateFailed).To(stPublishFailedAccountStatusUpdateEvent, stFailedNotification).On(
			evNoNeed).Run(wrapperFun("accountFailureUpdateHandling", w.accountFailureUpdateHandling), transactionalRetryOption),
		commonwf.NewTransition().From(stPublishFailedAccountStatusUpdateEvent).To(stFailurePublished).On(
			evNoNeed).Run(wrapperFun("publishAccountStatusUpdateEvent", w.publishAccountStatusUpdateEvent), transactionalRetryOption),
	}

	wfConfigurator := commonwf.NewTransitionConfigurator(workflowLogTag, transitions)
	wfConfigurator.AddTransitionsTo(updateCASAAccountStatusWorkflow)

	we.RegisterWorkflow(updateCASAAccountStatusWorkflow)
}

//nolint:dupl
func (w *WorkflowImpl) accountSuccessUpdateHandling(
	ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(
			workflowLogTag, "invalid context passed in accountSuccessUpdateHandling state", utils.GetTraceID(ctx),
		)
		return nil, workflow.ErrInvalidContext
	}

	slog.FromContext(ctx).Info(workflowLogTag, "In accountSuccessUpdateHandling state", utils.GetTraceID(ctx))
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(currCtx.Request.IdempotencyKey))
	nextCtx := currCtx.clone()

	if w.isAllowableProductVariant(nextCtx.CustomerAccount.ProductVariantID) {
		nextCtx.SetState(stPublishAccountDetailEvent)
		slog.FromContext(ctx).Info(workflowLogTag, "transition to stPublishAccountDetailEvent", utils.GetTraceID(ctx))
		return nextCtx, nil
	}

	return nextCtx, nil
}

//nolint:dupl
func (w *WorkflowImpl) accountFailureUpdateHandling(
	ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(
			workflowLogTag, "invalid context passed in accountFailureUpdateHandling state", utils.GetTraceID(ctx),
		)
		return nil, workflow.ErrInvalidContext
	}

	ctx = slog.AddTagsToContext(ctx, tags.TraceID(currCtx.Request.IdempotencyKey))
	slog.FromContext(ctx).Info(workflowLogTag, "In accountFailureUpdateHandling state", utils.GetTraceID(ctx))
	nextCtx := currCtx.clone()

	if w.isAllowableProductVariant(nextCtx.CustomerAccount.ProductVariantID) {
		nextCtx.SetState(stPublishFailedAccountStatusUpdateEvent)
		slog.FromContext(ctx).Info(
			workflowLogTag, "transition to stPublishFailureAccountCreatedEvent", utils.GetTraceID(ctx),
		)
		return nextCtx, nil
	}

	return nextCtx, nil
}

func (w *WorkflowImpl) isAllowableProductVariant(productVariantID string) bool {
	productVariantConfig := w.AppConfig.AccountServiceConfig.ProductVariantCodeConfig
	return lo.Contains([]string{productVariantConfig.DepositAccount, productVariantConfig.SavingsPocket, productVariantConfig.BoostPocket}, productVariantID)
}

// withTraceID is a wrapper function to inject trace id in the workflow context
func withTraceID(f we.Function) we.Function {
	return func(
		ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
	) (we.ExecutionData, error) {
		traceID := utils.GetTraceID(ctx)
		if traceID.Value() != "0" {
			ctx = slog.AddTagsToContext(ctx, traceID)
		} else {
			currCtx, ok := execData.(*ExecutionData)
			if ok && currCtx.Metadata != nil && currCtx.Metadata.TraceID != "" {
				traceID = slog.TraceID(currCtx.Metadata.TraceID)
				ctx = slog.AddTagsToContext(ctx, traceID)
			}
		}
		return f(ctx, transitionID, execData, params)
	}
}

// withMonitoring is a wrapper function to monitor the time taken for each transition
func withMonitoring(name string, f we.Function) we.Function {
	return func(
		ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
	) (we.ExecutionData, error) {
		nextCtx, ok := execData.(*ExecutionData)
		startTime := utils.Now().UTC()
		res, err := f(ctx, transitionID, execData, params)
		if err == nil {
			timeSpent := time.Since(startTime)
			if ok {
				slog.FromContext(ctx).Info(
					workflowLogTag, fmt.Sprintf(
						"Time elapsed for the transition %s for the account=%s : %s", name,
						nextCtx.CustomerAccount.AccountID, timeSpent,
					),
					slog.CustomTag("duration", timeSpent.Nanoseconds()),
				)
			}
		}
		return res, err
	}
}

func withSafeIDAndServiceID(f we.Function) we.Function {
	return func(
		ctx context.Context, transitionID string, execData we.ExecutionData, params interface{},
	) (we.ExecutionData, error) {
		nextCtx, ok := execData.(*ExecutionData)
		if !ok {
			return f(ctx, transitionID, execData, params)
		}
		if nextCtx.SafeID != "" {
			if commonCtx.GetUserID(ctx) == "" {
				ctx = commonCtx.WithUserID(ctx, nextCtx.SafeID)
			}
			if commonCtx.GetHTTPHeader(ctx, commonCtx.HeaderXUserID) == "" {
				ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, nextCtx.SafeID)
			}
		}

		if nextCtx.ServiceID != "" {
			if commonCtx.GetServiceID(ctx) == "" {
				ctx = commonCtx.WithServiceID(ctx, nextCtx.ServiceID)
			}
			if commonCtx.GetHTTPHeader(ctx, commonCtx.HeaderXServiceID) == "" {
				ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, nextCtx.ServiceID)
			}
		}
		return f(ctx, transitionID, execData, params)
	}
}
