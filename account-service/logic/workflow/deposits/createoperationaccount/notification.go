package createoperationaccount

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/workflow"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"

	tags "gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) sendNotification(ctx context.Context, transitionID string, execData we.ExecutionData, txnCtx we.TransitionContext, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(workflowLogTag, "invalid context passed in sendNotification state", utils.GetTraceID(ctx))
		return nil, workflow.ErrInvalidContext
	}

	slog.FromContext(ctx).Info(workflowLogTag, "In sendNotification state")
	ctx = slog.AddTagsToContext(ctx, tags.TraceID(currCtx.Request.IdempotencyKey))
	nextCtx := currCtx.clone()
	currState := currCtx.State
	productVariantConfig := w.AppConfig.AccountServiceConfig.ProductVariantCodeConfig

	// Check for success state (Only Publish when successfully created)
	if currState == stSuccessNotification && currCtx.Request != nil && currCtx.Request.ProductVariantCode == productVariantConfig.BoostPocket {
		pocketCategory, categoryErr := utils.LookUpPocketCategoryNameFromProductMaster(ctx, getBoostPocketTemplateID(currCtx), w.ProductMasterClient)
		if categoryErr != nil {
			slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("BP created: error looking up category name %s", categoryErr.Error()), utils.GetTraceID(ctx))
		} else {
			pocketName := getBoostPocketName(currCtx, productVariantConfig)
			pocketLifecycleDTO := constructPocketLifeCycleDTO(currCtx, pocketName, pocketCategory, getBoostPocketTenure(currCtx, productVariantConfig))
			err := w.PocketLifeCycleEventPublisher.Publish(ctx, pocketLifecycleDTO)
			if err != nil {
				slog.FromContext(ctx).Warn(workflowLogTag, fmt.Sprintf("BP created: error publishing notification %s", err.Error()), utils.GetTraceID(ctx))
			} else {
				slog.FromContext(ctx).Info(workflowLogTag, "BP created: notification published", utils.GetTraceID(ctx))
			}
		}
	}

	switch currState {
	case stSuccessNotification:
		nextCtx.SetState(stSuccessNotified)
		slog.FromContext(ctx).Info(workflowLogTag, "transition to stSuccessNotified", utils.GetTraceID(ctx))
	case stFailedNotification:
		nextCtx.SetState(stFailureNotified)
		slog.FromContext(ctx).Info(workflowLogTag, "transition to stFailureNotified", utils.GetTraceID(ctx))
	}

	return nextCtx, nil
}

func constructPocketLifeCycleDTO(currCtx *ExecutionData, pocketName string, pocketCategory string, pocketTenure map[string]int32) *dto.PocketLifecycleInfo {
	// this function covers success and failed lifecycle, so we need to be failsafe on null input
	createAccountSeamMsg := &dto.DepositsAccountStreamEventDTO{
		InstanceParams: make(map[string]string),
	}
	if currCtx.CreateAccountStreamMsg != nil {
		createAccountSeamMsg = currCtx.CreateAccountStreamMsg
	}
	pocketMetadata := dto.PocketLifecycleCreationMetaDTO{
		PocketCategory:          pocketCategory,
		PocketTenure:            pocketTenure,
		AccountOpeningTimestamp: createAccountSeamMsg.OpeningTimestamp,
		PrincipalAmount:         createAccountSeamMsg.InstanceParams["principal_amount"],
		MaturityDate:            createAccountSeamMsg.InstanceParams["maturity_date"],
		BaseInterest:            createAccountSeamMsg.InstanceParams["account_level_base_interest_rate"],
		BonusInterest:           createAccountSeamMsg.InstanceParams["account_level_bonus_interest_rate"],
		ErrorMessage:            lo.Ternary(currCtx.GetState() == stSuccessNotification, "", createAccountSeamMsg.ErrorMessage),
		ErrorCode:               lo.Ternary(currCtx.GetState() == stSuccessNotification, "", createAccountSeamMsg.ErrorCode),
	}

	return &dto.PocketLifecycleInfo{
		PocketID:               currCtx.AccountID,
		PocketType:             currCtx.Request.ProductVariantCode,
		PocketName:             pocketName,
		PocketMetadata:         utils.StructToMap(pocketMetadata),
		LifecycleOperationType: constants.BoostPocketCreationEvent,
	}
}

func getBoostPocketTemplateID(ctx *ExecutionData) string {
	if ctx == nil || ctx.Request == nil ||
		ctx.Request.ProductParameters == nil ||
		ctx.Request.ProductParameters.BoostPocket == nil ||
		ctx.Request.ProductParameters.BoostPocket.PocketTemplateDetail == nil {
		return ""
	}
	return ctx.Request.ProductParameters.BoostPocket.PocketTemplateDetail.TemplateID
}

func getBoostPocketTenure(currCtx *ExecutionData, productVariantConfig config.ProductVariantCodeConfig) map[string]int32 {
	if currCtx != nil &&
		currCtx.Request != nil &&
		currCtx.Request.ProductVariantCode == productVariantConfig.BoostPocket &&
		currCtx.Request.ProductParameters != nil &&
		currCtx.Request.ProductParameters.BoostPocket != nil {
		if currCtx.Request.ProductParameters.BoostPocket.CalendarDate != nil {
			return map[string]int32{
				"day":   currCtx.Request.ProductParameters.BoostPocket.CalendarDate.Day,
				"month": currCtx.Request.ProductParameters.BoostPocket.CalendarDate.Month,
				"year":  currCtx.Request.ProductParameters.BoostPocket.CalendarDate.Year,
			}
		}
	}
	return nil
}

func getBoostPocketName(currCtx *ExecutionData, productVariantConfig config.ProductVariantCodeConfig) string {
	if currCtx.Request.ProductVariantCode != productVariantConfig.BoostPocket {
		return ""
	}
	if currCtx.Request.ProductParameters == nil {
		return ""
	}
	if currCtx.Request.ProductParameters.BoostPocket == nil {
		return ""
	}
	if currCtx.Request.ProductParameters.BoostPocket.PocketTemplateDetail == nil {
		return ""
	}
	if currCtx.Request.ProductParameters.BoostPocket.PocketTemplateDetail.CustomName != "" {
		return currCtx.Request.ProductParameters.BoostPocket.PocketTemplateDetail.CustomName
	}
	if len(currCtx.Request.ProductParameters.BoostPocket.PocketTemplateDetail.QuestionAnswerPairs) > 0 {
		return currCtx.Request.ProductParameters.BoostPocket.PocketTemplateDetail.QuestionAnswerPairs[0].AnswerText
	}
	return ""
}
