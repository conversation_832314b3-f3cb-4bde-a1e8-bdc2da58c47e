package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/internal/notification"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/test/utils"
	utils2 "gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	pocketLifecycleSchema "gitlab.myteksi.net/dakota/schemas/streams/apis/pocket_lifecycle"
)

func Test_validatePocketLifecycleEvent(t *testing.T) {
	scenarios := []struct {
		desc        string
		data        pocketLifecycleSchema.PocketLifecycle
		isError     bool
		expectedErr string
	}{
		{
			desc: "invalid-pocketID",
			data: pocketLifecycleSchema.PocketLifecycle{
				PocketType:             string(constants.PocketTypeSavings),
				LifecycleOperationType: constants.PocketClosureEvent},
			isError:     true,
			expectedErr: "`PocketID` is invalid",
		}, {
			desc: "invalid-pocketType",
			data: pocketLifecycleSchema.PocketLifecycle{
				PocketID:               "888",
				PocketType:             "SAVE",
				LifecycleOperationType: constants.PocketClosureEvent},
			isError:     true,
			expectedErr: "`PocketType` SAVE is invalid",
		}, {
			desc: "invalid-eventType",
			data: pocketLifecycleSchema.PocketLifecycle{
				PocketID:               "888",
				PocketType:             string(constants.PocketTypeSavings),
				LifecycleOperationType: "random"},
			isError:     true,
			expectedErr: "`LifecycleOperationType` random is invalid",
		}, {
			desc: "happy-path",
			data: pocketLifecycleSchema.PocketLifecycle{
				PocketID:               "888",
				PocketType:             string(constants.PocketTypeSavings),
				LifecycleOperationType: constants.PocketClosureEvent},
			isError:     false,
			expectedErr: "",
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			errs := validatePocketLifecycleEvent(scenario.data, config.ProductVariantCodeConfig{
				BoostPocket: utils.BoostPocketProductVariantCode,
			})
			if scenario.isError {
				assert.EqualError(t, errs[0], scenario.expectedErr)
			} else {
				assert.Equal(t, 0, len(errs))
			}
		})
	}
}

// nolint:gocognit
func Test_HandlePocketLifecycleEventStream(t *testing.T) {
	t.Run("boost pocket creation event", func(t *testing.T) {
		ctx := context.Background()
		defaultError := errors.New("any error")
		data := pocketLifecycleSchema.PocketLifecycle{
			PocketID:               "888",
			PocketName:             "Paris",
			PocketType:             utils.BoostPocketProductVariantCode,
			LifecycleOperationType: constants.BoostPocketCreationEvent,
			PocketMetadata: []byte(utils2.ToJSON(
				dto.PocketLifecycleCreationMetaDTO{
					PocketCategory: "pocket-category",
					PocketTenure: map[string]int32{
						"year": 1,
					},
					AccountOpeningTimestamp: lo.ToPtr(time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)),
					PrincipalAmount:         "800",
					MaturityDate:            "2022-01-01",
					BaseInterest:            "1.00",
					BonusInterest:           "0.50",
					ErrorMessage:            "",
					ErrorCode:               "",
				})),
		}
		scenarios := []struct {
			desc                          string
			isError                       bool
			errorFromPocketFetch          error
			pocketResponse                []*storage.Pocket
			errorFromCustomerAccountFetch error
			customerAccountResponse       *storage.CustomerAccount
			getCustomerResponse           *customerMaster.GetCustomerByCIFNumberResponse
			getCustomerError              error
			errorFromLookUpCall           error
			lookUpCallResponse            *customerMaster.LookupSafeIDAndServiceIDResponse
			errorFromNotif                error
			expectedErr                   string
			data                          pocketLifecycleSchema.PocketLifecycle
			expectedNotificationTemplate  notification.Type
			isCreationFailure             bool
		}{
			{
				desc:                          "error-while-pocket-fetch",
				data:                          data,
				isError:                       true,
				errorFromPocketFetch:          defaultError,
				pocketResponse:                nil,
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       nil,
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            nil,
				errorFromNotif:                nil,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "error-while-account-fetch",
				data:                          data,
				isError:                       true,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: defaultError,
				customerAccountResponse:       nil,
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            nil,
				errorFromNotif:                nil,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "error-from-look-up-call",
				data:                          data,
				isError:                       true,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           defaultError,
				lookUpCallResponse:            nil,
				errorFromNotif:                nil,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "error-from-pigeon",
				data:                          data,
				isError:                       true,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           nil,
				getCustomerResponse: &customerMaster.GetCustomerByCIFNumberResponse{
					Customer: &customerMaster.CustomerData{
						Data: json.RawMessage(`{"PreferredName": "test-name", "ID": "safe-id"}`),
					},
				},
				lookUpCallResponse: &customerMaster.LookupSafeIDAndServiceIDResponse{SafeID: "test-safeID"},
				errorFromNotif:     defaultError,
				expectedErr:        defaultError.Error(),
			},
			{
				desc:                          "happy-path",
				data:                          data,
				isError:                       false,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            &customerMaster.LookupSafeIDAndServiceIDResponse{SafeID: "test-safeID"},
				getCustomerResponse: &customerMaster.GetCustomerByCIFNumberResponse{
					Customer: &customerMaster.CustomerData{
						Data: json.RawMessage(`{"PreferredName": "test-name", "ID": "safe-id"}`),
					},
				},
				errorFromNotif:               nil,
				expectedErr:                  "",
				expectedNotificationTemplate: notification.BoostPocketCreationNotification,
			},
			{
				desc: "happy-path for creation failure event",
				data: func() pocketLifecycleSchema.PocketLifecycle {
					clone := data
					var metadata map[string]any
					_ = json.Unmarshal(clone.PocketMetadata, &metadata)
					metadata["error_code"] = "INSUFFICIENT_BALANCE"
					clone.PocketMetadata = []byte(utils2.ToJSON(metadata))
					return clone
				}(),
				isError:                       false,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            &customerMaster.LookupSafeIDAndServiceIDResponse{SafeID: "test-safeID"},
				getCustomerResponse: &customerMaster.GetCustomerByCIFNumberResponse{
					Customer: &customerMaster.CustomerData{
						Data: json.RawMessage(`{"PreferredName": "test-name", "ID": "safe-id"}`),
					},
				},
				errorFromNotif:               nil,
				expectedErr:                  "",
				expectedNotificationTemplate: notification.BoostPocketCreationFailedBalanceNotification,
				isCreationFailure:            true,
			},
		}

		for _, scenario := range scenarios {
			t.Run(scenario.desc, func(t *testing.T) {
				mockDBStore := &storage.MockDatabaseStore{}
				mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
				mockNotifier := &notification.MockNotifierImpl{}

				mockDBStore.On("GetPockets", mock.Anything, mock.Anything).Return(scenario.pocketResponse, scenario.errorFromPocketFetch)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(scenario.customerAccountResponse, scenario.errorFromCustomerAccountFetch)
				mockCustomerMasterClient.On("LookupSafeIDAndServiceID", mock.Anything, mock.Anything).Return(scenario.lookUpCallResponse, scenario.errorFromLookUpCall)
				mockCustomerMasterClient.On("GetCustomerByCIFNumber", mock.Anything, mock.Anything).Return(scenario.getCustomerResponse, scenario.getCustomerError)
				mockNotifier.On("SendNotification", mock.Anything, mock.Anything).Return(scenario.errorFromNotif).Run(func(args mock.Arguments) {
					notifDto := args[1].(*notification.NotifDTO)
					// output value:
					// {
					// 	"RecipientID": "test-cif",
					// 	"CustomerID": "test-safeID",
					// 	"NotificationType": "boostPocketCreationNotification",
					// 	"Params": {
					// 	"PushParams": {
					// 		"pocket_name": "Paris"
					// 	},
					// 	"EmailParams": {
					// 		"base_interest": "1.00",
					// 			"bonus_interest": "0.50",
					// 			"currency": "RM",
					// 			"pocket_name": "Paris",
					// 			"principal_amount": "800.00",
					// 			"source_fund_account_name": "Main Account",
					// 			"tenure_end_date": "01 Jan 2022",
					// 			"tenure_start_date": "01 Jan 2021",
					// 			"total_interest": "1.50",
					// 			"user_name": "test-name"
					// 	}
					// }
					if !scenario.isCreationFailure {
						assert.Equalf(t, "Paris", notifDto.Params.EmailParams["pocket_name"], "pocket name not equal")
						assert.Equalf(t, "1.00", notifDto.Params.EmailParams["base_interest"], "base interest not equal")
						assert.Equalf(t, "0.50", notifDto.Params.EmailParams["bonus_interest"], "bonus interest not equal")
						assert.Equalf(t, "RM", notifDto.Params.EmailParams["currency"], "currency not equal")
						assert.Equalf(t, "800.00", notifDto.Params.EmailParams["principal_amount"], "principal amount not equal")
						assert.Equalf(t, "Main Account", notifDto.Params.EmailParams["source_fund_account_name"], "source fund account name not equal")
					}
					if lo.IsNotEmpty(scenario.expectedNotificationTemplate) {
						assert.Equalf(t, scenario.expectedNotificationTemplate, notifDto.NotificationType, fmt.Sprintf("expected notification type %s, got %s", scenario.expectedNotificationTemplate, notifDto.NotificationType))
					}
					// val, _ := json.MarshalIndent(notifDto, "", "  ")
					// fmt.Print("notif dto", string(val))
				})

				pocketLifeCycleConsumerDTO := dto.PocketLifecycleConsumerDTO{
					Config: &config.AppConfig{
						AccountServiceConfig: config.AccountServiceConfig{
							ProductVariantCodeConfig: config.ProductVariantCodeConfig{
								BoostPocket: utils.BoostPocketProductVariantCode,
							},
						},
					},
					Store:                mockDBStore,
					CustomerMasterClient: mockCustomerMasterClient,
					Notifier:             mockNotifier,
				}

				errs := HandlePocketLifecycleEventStream(ctx, pocketLifeCycleConsumerDTO, scenario.data)
				if scenario.isError {
					assert.EqualError(t, errs[0], scenario.expectedErr)
				} else {
					assert.Equal(t, 0, len(errs))
				}
			})
		}
	})

	t.Run("savings pocket closure event", func(t *testing.T) {
		ctx := context.Background()
		defaultError := errors.New("any error")
		data := pocketLifecycleSchema.PocketLifecycle{PocketID: "888", PocketType: string(constants.PocketTypeSavings), LifecycleOperationType: constants.PocketClosureEvent}
		scenarios := []struct {
			desc                          string
			isError                       bool
			errorFromPocketFetch          error
			pocketResponse                []*storage.Pocket
			errorFromCustomerAccountFetch error
			customerAccountResponse       *storage.CustomerAccount
			errorFromLookUpCall           error
			lookUpCallResponse            *customerMaster.LookupSafeIDAndServiceIDResponse
			errorFromNotif                error
			expectedErr                   string
		}{
			{
				desc:                          "error-while-pocket-fetch",
				isError:                       true,
				errorFromPocketFetch:          defaultError,
				pocketResponse:                nil,
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       nil,
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            nil,
				errorFromNotif:                nil,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "error-while-account-fetch",
				isError:                       true,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: defaultError,
				customerAccountResponse:       nil,
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            nil,
				errorFromNotif:                nil,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "error-from-look-up-call",
				isError:                       true,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           defaultError,
				lookUpCallResponse:            nil,
				errorFromNotif:                nil,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "error-from-pigeon",
				isError:                       true,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            &customerMaster.LookupSafeIDAndServiceIDResponse{SafeID: "test-safeID"},
				errorFromNotif:                defaultError,
				expectedErr:                   defaultError.Error(),
			},
			{
				desc:                          "happy-path",
				isError:                       false,
				errorFromPocketFetch:          nil,
				pocketResponse:                []*storage.Pocket{{Name: "Paris"}},
				errorFromCustomerAccountFetch: nil,
				customerAccountResponse:       &storage.CustomerAccount{CifNumber: "test-cif"},
				errorFromLookUpCall:           nil,
				lookUpCallResponse:            &customerMaster.LookupSafeIDAndServiceIDResponse{SafeID: "test-safeID"},
				errorFromNotif:                nil,
				expectedErr:                   "",
			},
		}

		for _, scenario := range scenarios {
			t.Run(scenario.desc, func(t *testing.T) {
				mockDBStore := &storage.MockDatabaseStore{}
				mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
				mockNotifier := &notification.MockNotifierImpl{}

				mockDBStore.On("GetPockets", mock.Anything, mock.Anything).Return(scenario.pocketResponse, scenario.errorFromPocketFetch)
				mockDBStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(scenario.customerAccountResponse, scenario.errorFromCustomerAccountFetch)
				mockCustomerMasterClient.On("LookupSafeIDAndServiceID", mock.Anything, mock.Anything).Return(scenario.lookUpCallResponse, scenario.errorFromLookUpCall)
				mockNotifier.On("SendNotification", mock.Anything, mock.Anything).Return(scenario.errorFromNotif)

				pocketLifeCycleConsumerDTO := dto.PocketLifecycleConsumerDTO{
					Config:               &config.AppConfig{},
					Store:                mockDBStore,
					CustomerMasterClient: mockCustomerMasterClient,
					Notifier:             mockNotifier,
				}

				errs := HandlePocketLifecycleEventStream(ctx, pocketLifeCycleConsumerDTO, data)
				if scenario.isError {
					assert.EqualError(t, errs[0], scenario.expectedErr)
				} else {
					assert.Equal(t, 0, len(errs))
				}
			})
		}
	})
}

func Test_getBoostPocketCreationFailureNotificationType(t *testing.T) {
	scenarios := []struct {
		desc              string
		errCode           string
		expectedNotifType notification.Type
	}{
		{
			desc:              "should return generic notification type if error code is empty",
			errCode:           "",
			expectedNotifType: notification.BoostPocketCreationFailedGenericNotification,
		},
		{
			desc:              "should return insufficient balance notification type if error code is INSUFFICIENT_BALANCE",
			errCode:           "INSUFFICIENT_BALANCE",
			expectedNotifType: notification.BoostPocketCreationFailedBalanceNotification,
		},
		{
			desc:              "should return high demand notification type if error code is PRODUCT_UNAVAILABLE",
			errCode:           "PRODUCT_UNAVAILABLE",
			expectedNotifType: notification.BoostPocketCreationFailedAvailabilityNotification,
		},
		{
			desc:              "should return generic notification type if error code is unknown",
			errCode:           "UNKNOWN",
			expectedNotifType: notification.BoostPocketCreationFailedGenericNotification,
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.desc, func(t *testing.T) {
			notifType := getBoostPocketCreationFailureNotificationType(scenario.errCode)
			assert.Equal(t, scenario.expectedNotifType, notifType)
		})
	}
}
