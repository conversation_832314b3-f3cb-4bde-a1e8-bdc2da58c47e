// Package consumer holds logic to consume kafka streams
package consumer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime/debug"
	"slices"
	"time"

	"github.com/samber/lo"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/internal/notification"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	context2 "gitlab.myteksi.net/dakota/common/context"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	pocketLifecycleSchema "gitlab.myteksi.net/dakota/schemas/streams/apis/pocket_lifecycle"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	activeProfile "gitlab.myteksi.net/dbmy/common/active-profile/v2"
)

type pocketLifeCycleDto struct {
	pocketLifecycleSchema.PocketLifecycle
	metadata *dto.PocketLifecycleCreationMetaDTO
}

// HandlePocketLifecycleEventStream ...
func HandlePocketLifecycleEventStream(ctx context.Context, pocketLifecycleEventDTO dto.PocketLifecycleConsumerDTO,
	data pocketLifecycleSchema.PocketLifecycle) (errs []error) {
	defer func() {
		if e := recover(); e != nil {
			slog.FromContext(ctx).Error(constants.DepositsPostingBatchConsumerLogTag, "[PANIC RECOVERED]", slog.CustomTag("err", e),
				slog.CustomTag(constants.StackTraceLogTag, string(debug.Stack())))
			errs = append(errs, fmt.Errorf("panic: %v", e))
		}
	}()
	slog.FromContext(ctx).Info(constants.PocketLifecycleConsumerLogTag, fmt.Sprintf("Event Received: %s", utils.ToJSON(data)))

	// validate the kafka event
	if errs := validatePocketLifecycleEvent(data, pocketLifecycleEventDTO.Config.AccountServiceConfig.ProductVariantCodeConfig); len(errs) != 0 {
		return errs
	}

	// fetch pocket data from DB
	pocket, pocketErr := getPocketByPocketID(ctx, pocketLifecycleEventDTO.Store, data)
	if pocketErr != nil {
		return []error{pocketErr}
	}

	// fetch account data from DB
	account, accountErr := getCustomerAccountByPocketID(ctx, pocketLifecycleEventDTO.Store, data)
	if accountErr != nil {
		return []error{accountErr}
	}

	// fetch recipient ID
	safeID, err := getRecipientIDByCIFNumber(ctx, pocketLifecycleEventDTO.CustomerMasterClient, account.CifNumber, data)
	if err != nil {
		return []error{err}
	}
	ctx = withCommonHTTPHeadersCtx(ctx, safeID, account.CifNumber, constants.ServiceDigibank, activeProfile.RETAIL.String())

	lifecycleMetadata := &dto.PocketLifecycleCreationMetaDTO{}
	if len(data.PocketMetadata) > 0 {
		err = json.Unmarshal(data.PocketMetadata, lifecycleMetadata)
		if err != nil {
			return []error{fmt.Errorf("failed to unmarshal pocket metadata: %w", err)}
		}
	}
	lifecycleEvent := pocketLifeCycleDto{
		PocketLifecycle: data,
		metadata:        lifecycleMetadata,
	}
	lifecycleEvent.PocketName = lo.CoalesceOrEmpty(lifecycleEvent.PocketName, pocket.Name)

	notifDTO, err := buildNotification(ctx, safeID, account.CifNumber, lifecycleEvent, pocketLifecycleEventDTO.CustomerMasterClient)
	if err != nil {
		return []error{err}
	}
	err = pocketLifecycleEventDTO.Notifier.SendNotification(ctx, &notifDTO)
	if err != nil {
		return []error{err}
	}

	slog.FromContext(ctx).Info(constants.PocketLifecycleConsumerLogTag, fmt.Sprintf("Event Successfully processed: %s", utils.ToJSON(data)))
	return nil
}

func buildNotification(ctx context.Context, safeID, cif string, msg pocketLifeCycleDto, customerClient customerMaster.CustomerMaster) (notification.NotifDTO, error) {
	notifDTO := notification.NotifDTO{
		CustomerID:  safeID,
		RecipientID: cif,
		Params: notification.TemplateParams{
			PushParams: map[string]string{"pocket_name": msg.PocketName},
		},
	}

	switch msg.LifecycleOperationType {
	case constants.PocketClosureEvent:
		notifDTO.NotificationType = notification.SavingsPocketClosureNotification
	case constants.PocketCreationEvent:
		notifDTO.NotificationType = notification.SavingsPocketCreationNotification
	case constants.BoostPocketCreationEvent:
		isFailureEvent := lo.IsNotEmpty(lo.CoalesceOrEmpty(msg.metadata.ErrorCode, msg.metadata.ErrorMessage))
		if isFailureEvent {
			notifDTO.NotificationType = getBoostPocketCreationFailureNotificationType(msg.metadata.ErrorCode)
		} else {
			notifDTO.NotificationType = notification.BoostPocketCreationNotification
			emailParams, paramErr := buildBoostPocketCreationEmailParams(ctx, msg, cif, customerClient)
			if paramErr != nil {
				return notifDTO, paramErr
			}
			notifDTO.Params.EmailParams = emailParams
		}
	}
	return notifDTO, nil
}

func getBoostPocketCreationFailureNotificationType(errCode string) notification.Type {
	switch errCode {
	case "INSUFFICIENT_BALANCE":
		return notification.BoostPocketCreationFailedBalanceNotification
	case "PRODUCT_UNAVAILABLE":
		return notification.BoostPocketCreationFailedAvailabilityNotification
	default:
		return notification.BoostPocketCreationFailedGenericNotification
	}
}

func withCommonHTTPHeadersCtx(ctx context.Context, safeID, cif, serviceID string, profileType string) context.Context {
	ctx = context2.WithHTTPHeader(ctx, context2.HeaderXUserID, safeID)
	ctx = context2.WithHTTPHeader(ctx, context2.HeaderXServiceID, serviceID)
	ctx = context2.WithHTTPHeader(ctx, context2.HeaderXProfileID, cif)
	ctx = context2.WithHTTPHeader(ctx, context2.HeaderXProfileType, profileType)
	return ctx
}

// nolint:funlen
func buildBoostPocketCreationEmailParams(ctx context.Context, msg pocketLifeCycleDto, cif string, customerClient customerMaster.CustomerMaster) (map[string]string, error) {
	params := make(map[string]string)
	params["pocket_name"] = msg.PocketName
	customer, err := utils.GetCustomerByCIFFromCustomerMaster(ctx, customerClient, cif)
	if err != nil {
		return nil, err
	}

	msgMetadata := msg.metadata
	if msgMetadata.PocketTenure == nil {
		return nil, errors.New("pocket tenure is nil")
	}

	if msgMetadata.AccountOpeningTimestamp == nil || msgMetadata.AccountOpeningTimestamp.IsZero() {
		return nil, errors.New("account opening timestamp is nil")
	}
	maturityStartDate := utils.GetLocalDate(*msgMetadata.AccountOpeningTimestamp)

	// get customer name
	params["user_name"] = customer.PreferredName
	params["currency"] = utils.LocalisedCurrency(constants.DefaultCurrency)
	principalAmount, err := utils.FormattedAmount(msgMetadata.PrincipalAmount, constants.DefaultCurrency, false)
	if err != nil {
		return nil, fmt.Errorf("failed to format principal amount: %w", err)
	}
	params["principal_amount"] = principalAmount
	params["base_interest"] = msgMetadata.BaseInterest
	params["bonus_interest"] = msgMetadata.BonusInterest
	params["tenure_start_date"] = maturityStartDate.Format(constants.DDMMMYYYYFormat)
	tenureEndDate, err := time.Parse(constants.DateTimeFormat, msgMetadata.MaturityDate)
	if err != nil {
		return nil, fmt.Errorf("failed to format tenure end date %s: %w", msgMetadata.MaturityDate, err)
	}
	params["tenure_end_date"] = tenureEndDate.Format(constants.DDMMMYYYYFormat)
	params["source_fund_account_name"] = "Main Account"

	return params, nil
}

// validatePocketLifecycleEvent will validate pocketLifecycle event
func validatePocketLifecycleEvent(data pocketLifecycleSchema.PocketLifecycle, productVariantConfig config.ProductVariantCodeConfig) []error {
	var errs []error
	if data.PocketID == "" {
		errs = append(errs, errors.New("`PocketID` is invalid"))
	}
	if !slices.Contains([]string{string(constants.PocketTypeSavings), productVariantConfig.BoostPocket}, data.PocketType) {
		errs = append(errs, fmt.Errorf("`PocketType` %s is invalid", data.PocketType))
	}
	allowableEvents := []string{constants.PocketClosureEvent, constants.PocketCreationEvent, constants.BoostPocketCreationEvent}
	if !slices.Contains(allowableEvents, data.LifecycleOperationType) {
		errs = append(errs, fmt.Errorf("`LifecycleOperationType` %s is invalid", data.LifecycleOperationType))
	}
	return errs
}

// getPocketByPocketID will fetch pocket by pocketID
func getPocketByPocketID(ctx context.Context, store storage.DatabaseStore, data pocketLifecycleSchema.PocketLifecycle) (*storage.Pocket, error) {
	pockets, err := store.GetPockets(ctx, []string{data.PocketID})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PocketLifecycleConsumerLogTag,
			fmt.Sprintf("Error in getPocket db call for data: %s, err: %s", utils.ToJSON(data), err.Error()))
		return nil, err
	}
	return pockets[0], nil
}

// getCustomerAccount will fetch account data based on pocketID
func getCustomerAccountByPocketID(ctx context.Context, store storage.DatabaseStore, data pocketLifecycleSchema.PocketLifecycle) (*storage.CustomerAccount, error) {
	account, err := store.GetCustomerAccount(ctx, data.PocketID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PocketLifecycleConsumerLogTag,
			fmt.Sprintf("Error in getCustomerAccount db call for data: %s, err: %s", utils.ToJSON(data), err.Error()))
		return nil, err
	}
	return account, nil
}

// getRecipientIDByCIFNumber will get safeID for the customer.
func getRecipientIDByCIFNumber(ctx context.Context, customerMasterClient customerMaster.CustomerMaster,
	cifNumber string, data pocketLifecycleSchema.PocketLifecycle) (string, error) {
	customerResp, err := customerMasterClient.LookupSafeIDAndServiceID(ctx, &customerMaster.LookupSafeIDAndServiceIDRequest{
		CifNumber: cifNumber,
	})
	if err != nil {
		slog.FromContext(ctx).Warn(constants.PocketLifecycleConsumerLogTag,
			fmt.Sprintf("Error in getRecipientIDByCIFNumber call for data: %s, err: %s", utils.ToJSON(data), err.Error()))
		return "", err
	}
	return customerResp.SafeID, nil
}
