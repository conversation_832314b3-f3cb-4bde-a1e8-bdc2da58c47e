package consumer

import (
	"context"
	"fmt"

	"gitlab.myteksi.net/bersama/core-banking/account-service/logic/workflow/deposits/createoperationaccount"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils/common"

	"gitlab.myteksi.net/gophers/go/commons/util/tags"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkareader"

	"gitlab.myteksi.net/dakota/schemas/streams"
	"gitlab.myteksi.net/dakota/schemas/streams/apis/deposits_account_create_event"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	depositsAccountConsumer kafkareader.Client
)

// startDepositsAccountCreatingStream is the very first entry point to start consuming deposits account creation stream
func startDepositsAccountCreatingStream(appCfg *config.AppConfig) {
	kafkaConfig := appCfg.DepositsAccountCreationEventKafkaConfig.KafkaConfig
	reader, err := streams.NewStaticReader(context.Background(), constants.DepositsAccountCreationConsumerLogTag, *kafkaConfig,
		&deposits_account_create_event.DepositsAccountCreateEvent{})
	if err != nil {
		slog.FromContext(context.Background()).Warn(constants.DepositsAccountCreationConsumerLogTag, fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", kafkaConfig, err))
		panic(fmt.Sprintf("failed to create new Reader, config=[%+v], err=[%+v]", kafkaConfig, err))
	}

	depositsAccountConsumer = reader
	asyncConsumeDepositsccountCreationStream(constants.DepositsAccountCreationConsumerLogTag, appCfg, depositsAccountConsumer.GetDataAckChan())
}

var asyncConsumeDepositsccountCreationStream = func(tag string, appCfg *config.AppConfig, ch <-chan *kafkareader.AckEntity) {
	wg.Go(tag, func() {
		consumeDepositsAccountCreationStream(context.Background(), appCfg, ch)
	})
}

// nolint:dupl
func consumeDepositsAccountCreationStream(ctx context.Context, appCfg *config.AppConfig, ch <-chan *kafkareader.AckEntity) {
	kafkaCfg := appCfg.DepositsAccountCreationEventKafkaConfig

	for event := range ch {
		accountData, ok := event.Event.(*deposits_account_create_event.DepositsAccountCreateEvent)
		if !ok {
			slog.FromContext(ctx).Fatal(constants.DepositsAccountCreationConsumerLogTag, fmt.Sprintf("Wrong entity in reader, event=[%+v]", event.Event),
				commonTags(kafkaCfg.Stream, kafkaCfg.DtoName)...)
			continue
		}

		if err := common.RetryFunction(ctx, func() error {
			return handleDepositsAccountCreationStream(ctx, *accountData)
		}, constants.DepositsAccountCreationConsumerLogTag, kafkaCfg.MaxRetryCount, kafkaCfg.DelayInMilliSeconds); err != nil {
			slog.FromContext(ctx).Warn(constants.DepositsAccountCreationConsumerLogTag, "Handling stream event failed",
				commonTags(kafkaCfg.Stream, kafkaCfg.DtoName, tags.T("error", err))...)
		}

		if ackErr := event.Ack(); ackErr != nil {
			slog.FromContext(ctx).Warn(constants.DepositsAccountCreationConsumerLogTag, "Failed to ack message from DepositsAccountCreationStream", slog.Error(ackErr))
		}
	}
}

func handleDepositsAccountCreationStream(ctx context.Context, depositsAccountData deposits_account_create_event.DepositsAccountCreateEvent) error {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constants.IdempotencyKeyHeader, depositsAccountData.IdempotencyKey))
	slog.FromContext(ctx).Info(constants.DepositsAccountCreationConsumerLogTag, fmt.Sprintf("received message for deposits core client, event=[%+v]", depositsAccountData))
	depositsStreamMessage := &dto.DepositsAccountStreamEventDTO{
		IdempotencyKey:     depositsAccountData.IdempotencyKey,
		AccountID:          depositsAccountData.AccountID,
		CifNumber:          depositsAccountData.CifNumber,
		SafeID:             depositsAccountData.SafeID,
		ProductCode:        depositsAccountData.ProductCode,
		ProductVariantCode: depositsAccountData.ProductVariantCode,
		ProductVersionID:   depositsAccountData.ProductVersionID,
		InstanceParams:     depositsAccountData.InstanceParams,
		Status:             depositsAccountData.Status,
		ErrorCode:          depositsAccountData.ErrorCode,
		ErrorMessage:       depositsAccountData.ErrorMessage,
		OpeningTimestamp:   depositsAccountData.OpeningTimestamp,
		ClosingTimestamp:   depositsAccountData.ClosingTimestamp,
	}

	if errStream := createoperationaccount.ConsumeFromDepositsAccountCreationStream(ctx, depositsStreamMessage); errStream != nil {
		return errStream
	}

	return nil
}

func stopDepositsAccountCreationStream() {
	if err := depositsAccountConsumer.Shutdown(); err != nil {
		slog.Error(err)
	}
}
