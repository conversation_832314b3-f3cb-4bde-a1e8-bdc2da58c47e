module gitlab.myteksi.net/bersama/core-banking/account-service

replace (
	github.com/gogo/protobuf => github.com/gogo/protobuf v1.3.2
	github.com/miekg/dns => github.com/miekg/dns v1.1.25
	github.com/satori/go.uuid => github.com/satori/go.uuid v1.2.0
	gitlab.myteksi.net/bersama/core-banking/account-service/constants => ./constants
	gitlab.myteksi.net/bersama/core-banking/account-service/external => ./external
	gitlab.myteksi.net/bersama/core-banking/account-service/handlers => ./handlers
	gitlab.myteksi.net/bersama/core-banking/account-service/storage => ./storage
	gitlab.myteksi.net/dbmy/core-banking/account-service/api => ./api
	golang.org/x/text => golang.org/x/text v0.3.8
	gopkg.in/yaml.v2 => gopkg.in/yaml.v2 v2.4.0
	gopkg.in/yaml.v3 => gopkg.in/yaml.v3 v3.0.1
)

go 1.24.1

require (
	github.com/DATA-DOG/go-sqlmock v1.5.0
	github.com/ProtonMail/gopenpgp/v2 v2.7.5
	github.com/go-co-op/gocron v1.11.0
	github.com/google/uuid v1.6.0
	github.com/onsi/ginkgo v1.16.5
	github.com/onsi/gomega v1.32.0
	github.com/stretchr/testify v1.10.0
	gitlab.myteksi.net/bersama/core-banking/account-service/external v0.0.0-**************-2649e6155d78
	gitlab.myteksi.net/dakota/common/aws v1.4.3
	gitlab.myteksi.net/dakota/common/context v0.17.0
	gitlab.myteksi.net/dakota/common/executor v1.0.0
	gitlab.myteksi.net/dakota/common/redis v0.20.0
	gitlab.myteksi.net/dakota/common/servicename v1.55.0
	gitlab.myteksi.net/dakota/common/tenants v1.1.0
	gitlab.myteksi.net/dakota/common/testauto v1.18.0
	gitlab.myteksi.net/dakota/common/tracing v1.10.0
	gitlab.myteksi.net/dakota/core-banking/goal-core/api v1.9.0
	gitlab.myteksi.net/dakota/customer-master/api/v2 v2.52.0
	gitlab.myteksi.net/dakota/klient v1.26.0
	gitlab.myteksi.net/dakota/lending/loan-core/api v1.138.0
	gitlab.myteksi.net/dakota/schemas v1.16.162
	gitlab.myteksi.net/dakota/servus/v2 v2.55.0
	gitlab.myteksi.net/dakota/workflowengine v1.17.0
	gitlab.myteksi.net/dbmy/core-banking/account-service/api v0.0.0-**************-************
	gitlab.myteksi.net/dbmy/core-banking/deposits-core/api v1.37.0-dbmy
	gitlab.myteksi.net/dbmy/core-banking/deposits-exp/api v1.28.1-dbmy
	gitlab.myteksi.net/dbmy/core-banking/product-master/api v1.20.0-dbmy
	gitlab.myteksi.net/dbmy/customer-journal/api v1.39.0-dbmy
	gitlab.myteksi.net/dbmy/customer-master/api/v2 v2.57.0-dbmy
	gitlab.myteksi.net/dbmy/hermes/api v1.0.0
	gitlab.myteksi.net/dbmy/pigeon/api v1.33.0-dbmy
	gitlab.myteksi.net/dbmy/schemas v1.2.91
	gitlab.myteksi.net/gophers/go/commons/data v1.0.11
	gitlab.myteksi.net/gophers/go/commons/util/log/logging v1.1.8
	gitlab.myteksi.net/gophers/go/commons/util/log/yall v1.0.23
	gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent v1.0.0
	gitlab.myteksi.net/gophers/go/commons/util/tags v1.1.17
	gitlab.myteksi.net/gophers/go/commons/util/time/grabtime v1.0.0
	gitlab.myteksi.net/gophers/go/staples/statsd v1.0.14
	gitlab.myteksi.net/snd/streamsdk v1.6.10
)

require (
	github.com/Masterminds/squirrel v1.5.4
	github.com/bmizerany/assert v0.0.0-**************-b7ed37b82869
	github.com/go-sql-driver/mysql v1.6.0
	github.com/jszwec/csvutil v1.8.0
	github.com/olekukonko/tablewriter v0.0.5
	github.com/pkg/errors v0.9.1
	github.com/samber/lo v1.50.0
	github.com/shopspring/decimal v1.4.0
	github.com/slack-go/slack v0.13.1
	gitlab.myteksi.net/dakota/common/currency v1.6.0
	gitlab.myteksi.net/dakota/common/querycacher v0.1.0
	gitlab.myteksi.net/dakota/common/retryable-stream v1.16.0
	gitlab.myteksi.net/dakota/flow v1.2.0
	gitlab.myteksi.net/dakota/servus v1.64.0
	gitlab.myteksi.net/dbmy/common/active-profile/v2 v2.0.0
	gitlab.myteksi.net/dbmy/common/retryable-stream v1.1.1
	gitlab.myteksi.net/dbmy/core-banking/common v1.5.0-dbmy
	gitlab.myteksi.net/dbmy/core-banking/external-lib/v2 v2.2.0-dbmy
	gitlab.myteksi.net/dbmy/customer-experience/api v1.57.4
	gopkg.in/DataDog/dd-trace-go.v1 v1.51.0
)

require (
	github.com/DataDog/appsec-internal-go v1.0.0 // indirect
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.45.0-rc.1 // indirect
	github.com/DataDog/datadog-agent/pkg/remoteconfig/state v0.45.0-rc.1 // indirect
	github.com/DataDog/datadog-go v4.8.3+incompatible // indirect
	github.com/DataDog/datadog-go/v5 v5.1.1 // indirect
	github.com/DataDog/go-libddwaf v1.2.0 // indirect
	github.com/DataDog/go-tuf v0.3.0--fix-localmeta-fork // indirect
	github.com/DataDog/sketches-go v1.4.1 // indirect
	github.com/Microsoft/go-winio v0.5.2 // indirect
	github.com/ProtonMail/go-crypto v0.0.0-**************-5aa5874ade95 // indirect
	github.com/ProtonMail/go-mime v0.0.0-**************-7d82a3887f2f // indirect
	github.com/Rhymond/go-money v1.0.15 // indirect
	github.com/alecholmes/xfccparser v0.1.0 // indirect
	github.com/alecthomas/participle v0.4.1 // indirect
	github.com/aws/aws-sdk-go v1.44.294 // indirect
	github.com/aws/aws-sdk-go-v2 v1.26.0 // indirect
	github.com/aws/aws-sdk-go-v2/credentials v1.17.8 // indirect
	github.com/aws/aws-sdk-go-v2/internal/configsources v1.3.4 // indirect
	github.com/aws/aws-sdk-go-v2/internal/endpoints/v2 v2.6.4 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/accept-encoding v1.11.1 // indirect
	github.com/aws/aws-sdk-go-v2/service/internal/presigned-url v1.11.6 // indirect
	github.com/aws/aws-sdk-go-v2/service/sqs v1.31.3 // indirect
	github.com/aws/aws-sdk-go-v2/service/sts v1.28.5 // indirect
	github.com/aws/smithy-go v1.20.1 // indirect
	github.com/cactus/go-statsd-client/v4 v4.0.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/cloudflare/circl v1.3.3 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/eapache/go-resiliency v1.5.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/flosch/pongo2 v0.0.0-20200913210552-0d938eb266f3 // indirect
	github.com/fsnotify/fsnotify v1.5.4 // indirect
	github.com/garyburd/redigo v1.6.3 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-redis/redis v6.15.9+incompatible // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/gorilla/schema v1.4.1 // indirect
	github.com/gorilla/websocket v1.4.2 // indirect
	github.com/grpc-ecosystem/grpc-opentracing v0.0.0-20170512040955-6c130eed1e29 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-retryablehttp v0.7.7 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/julienschmidt/httprouter v1.3.0 // indirect
	github.com/klauspost/compress v1.16.7 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/lann/builder v0.0.0-20180802200727-47ae307949d0 // indirect
	github.com/lann/ps v0.0.0-20150810152359-62de8c46ede0 // indirect
	github.com/lightstep/lightstep-tracer-common/golang/gogo v0.0.0-20210210170715-a8dfcb80d3a7 // indirect
	github.com/lightstep/lightstep-tracer-go v0.25.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mattn/go-runewidth v0.0.13 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/myteksi/hystrix-go v1.1.3 // indirect
	github.com/myteksi/schema v0.0.0-20180214071320-149151f79a92 // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/outcaste-io/ristretto v0.2.1 // indirect
	github.com/philhofer/fwd v1.1.2 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/redis/go-redis/v9 v9.7.0 // indirect
	github.com/reterVision/go-kinesis v0.0.0-20150928061512-c0f0783318c3 // indirect
	github.com/rivo/uniseg v0.2.0 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/rogpeppe/go-internal v1.12.0 // indirect
	github.com/secure-systems-lab/go-securesystemslib v0.5.0 // indirect
	github.com/shirou/gopsutil/v3 v3.22.4 // indirect
	github.com/showa-93/go-mask v0.6.1 // indirect
	github.com/spiffe/go-spiffe/v2 v2.1.1 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tinylib/msgp v1.1.8 // indirect
	github.com/tklauser/go-sysconf v0.3.10 // indirect
	github.com/tklauser/numcpus v0.4.0 // indirect
	github.com/xdg/scram v0.0.0-20180814205039-7eeb5667e42c // indirect
	github.com/xdg/stringprep v1.0.3 // indirect
	github.com/yusufpapurcu/wmi v1.2.2 // indirect
	gitlab.myteksi.net/dakota/common/env-injection v1.0.0 // indirect
	gitlab.myteksi.net/dakota/common/secrets-injection v1.0.3 // indirect
	gitlab.myteksi.net/dakota/gaia v0.1.1 // indirect
	gitlab.myteksi.net/dakota/state-machine v1.4.0 // indirect
	gitlab.myteksi.net/dbmy/common/aws/v2 v2.1.0 // indirect
	gitlab.myteksi.net/dbmy/common/metrics v1.1.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/algo/cmap v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/deprecation v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/aws/grabkinesis v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/conf v1.0.2 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/config/mode v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/encoding/grabjson v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/ldflags v1.0.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/logdefaults v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/log/timerlog v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/monitor/statsd v1.0.10 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/network/iputil v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredis v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/redis/gredismigrate v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker v1.1.1 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/resilience/hystrix v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/server/swaggergen v1.0.4 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/tracer v1.0.5 // indirect
	gitlab.myteksi.net/gophers/go/commons/util/validate v1.1.4 // indirect
	gitlab.myteksi.net/gophers/go/spartan/lechuck v1.0.0 // indirect
	gitlab.myteksi.net/gophers/go/staples/gredis3 v1.0.8 // indirect
	gitlab.myteksi.net/gophers/go/staples/logging v1.0.0 // indirect
	gitlab.myteksi.net/snd/sarama v1.42.2 // indirect
	gitlab.myteksi.net/spartan/hystrix-go/v2 v2.0.2 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	go4.org/intern v0.0.0-20211027215823-ae77deb06f29 // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20220617031537-928513b29760 // indirect
	golang.org/x/crypto v0.36.0 // indirect
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/sync v0.6.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto v0.0.0-20230920204549-e6e6cdab5c13 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20230913181813-007df8e322eb // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20231002182017-d307bd883b97 // indirect
	google.golang.org/grpc v1.58.3 // indirect
	google.golang.org/protobuf v1.35.1 // indirect
	gopkg.in/redsync.v1 v1.0.1 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	inet.af/netaddr v0.0.0-20220811202034-502d2d690317 // indirect
)
