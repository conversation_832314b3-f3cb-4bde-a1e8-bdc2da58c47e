{"name": "account-service Service", "serviceName": "account-service", "env": "dev", "host": "0.0.0.0", "port": 8000, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "locAccountEventKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "loan-core-client-local", "stream": "dev-loan-core-loc-account", "clusterType": "critical", "enableTLL": true, "packageName": "pb", "offsetType": "oldest", "dtoName": "LoanCoreLoc"}, "locAccountCreationEventKafkaConfig": {"brokers": ["b-1.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.dev-cb-common-kaf.b8th7n.c3.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-client-local", "stream": "local-loc-account-creation-event", "clusterType": "critical", "enableTLL": true, "packageName": "pb", "offsetType": "oldest", "dtoName": "LOCAccountCreationEvent"}, "data": {"mysql": {"master": {"dsn": "root:@tcp(localhost:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "slave": {"dsn": "root:@tcp(localhost:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 2, "maxOpen": 10}, "masterCircuitBreaker": {"timeoutInMs": 1000}, "slaveCircuitBreaker": {"timeoutInMs": 1000}}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126}, "logger": {"syslogTag": "structuredlog.accountservice", "workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "json", "development": true, "headerMaskConfig": [{"key": "grab", "hideHeader": true}]}, "accountServiceConfig": {"productVariantCodeConfig": {"depositAccount": "deposit_account", "operatingAccount": "BIZ_DEPOSIT_ACCOUNT", "savingsPocket": "SAVINGS_POCKET", "boostPocket": "BOOST_POCKET"}, "clientConfig": {"maxIdleConnsPerHost": 64, "idleConnTimeoutInSMillis": 10000, "timeoutInMillis": 4000, "requestLogLockTimeoutInMillis": 5000}, "savingsPocketConfig": {"maxPocketLimit": 8}, "pendingActivationAutoCloseExcludedAccounts": [], "pendingActivationThresholdInMinutes": 3000, "pendingFirstDepositThresholdInMinutes": 3000, "bizOAPendingActivationThresholdInMinutes": 43200, "pendingActivationAutoCloseReminder": {"reminderText": ["1 Day", "3 Days", "5 Days"], "reminderTimeInMinutes": [1440, 4320, 7200], "bufferTimeInHours": 24, "_bufferTimeInHours": "Buffer time acts as an extension of the notification window, ensuring that notifications are sent to accounts that fall within the extended time frame. For example, if cron run at 14 Aug, when query it will add the buffer time (1 day), so the account that fall within 14 Aug and 15 Aug will received notification"}, "bizOAPendingActivationAutoCloseReminder": {"reminderText": ["1", "3", "5"], "reminderTimeInMinutes": [1440, 4320, 7200], "bufferTimeInHours": 24}, "pendingFirstDepositAutoCloseReminder": {"reminderText": ["1 Day", "3 Days", "5 Days"], "reminderTimeInMinutes": [1440, 4320, 7200], "bufferTimeInHours": 24, "_bufferTimeInHours": "Buffer time acts as an extension of the notification window, ensuring that notifications are sent to accounts that fall within the extended time frame. For example, if cron run at 14 Aug, when query it will add the buffer time (1 day), so the account that fall within 14 Aug and 15 Aug will received notification"}, "accountStatus": {"disableCloseFromHour": "00:00", "disableCloseFromHour.description": "The hour format should be something like 23:00 and will be parsed as UTC time", "disableCloseDurationSecond": 7200, "asyncClosureProductVariants": ["DEPOSITS_ACCOUNT", "BIZ_DEPOSIT_ACCOUNT", "SAVINGS_POCKET", "BOOST_POCKET"]}, "baseURL": "https://backend.dev.g-bank.app/account-service", "workflowMonitors": [{"workflowID": "create_casa_account_v3", "targetSlackChannel": "channel-id", "statusFetchDurationInMinutes": 3605, "batchSizeInMinutes": 3600, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [200, 205, 210, 225, 230, 235, 240, 245, 250, 500, 505, 510, 905, 915, 920], "spamCoolingOffInMinutes": 60, "enableAntiSpam": true}, {"workflowID": "dormant_casa_status", "targetSlackChannel": "channel-id", "statusFetchDurationInMinutes": 3605, "batchSizeInMinutes": 3600, "dataFetchCOPInMs": 500, "messagePerBatch": 50, "states": [200, 205, 210, 215, 220, 230, 235, 240, 245, 500, 505, 510, 515, 520, 525, 530, 535], "spamCoolingOffInMinutes": 60, "enableAntiSpam": true}]}, "snowflakeConfig": {"engine": "mysql", "dsn": "root:admin@tcp(localhost:3306)/analytics?parseTime=true&loc=UTC", "batchSize": 10}, "accountNADWorkflowConfig": {"workflowID": "account-nad-workflow-id", "filePattern": "nad-verified-%s.csv", "localFolder": "/tmp", "dataS3Bucket": "data-account", "coreBankingS3Bucket": "account-nad-verified", "pendingFolder": "pending", "completedFolder": "completed", "persistStateEvery": 5, "rsaPrivateKey": ""}, "bankRestrictedEventKafkaConfig": {"brokers": ["127.0.0.1:9092"], "clientID": "bank-restricted-action-event-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-bank-restricted-action-event", "packageName": "pb", "dtoName": "BankRestricted", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "processingDelayInMilliSeconds": 100, "enabled": true}, "depositsAccountCreationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-creation-event-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-account-creation-event", "packageName": "pb", "dtoName": "DepositsAccountCreateEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enabled": true, "enable": true}, "depositsAccountCreatedEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "deposits-account-created-event-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-account-created-event", "packageName": "pb", "dtoName": "DepositsAccountCreateEvent", "syncprod": true, "requiredAcks": -1}, "dormantAccountEventKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "deposits-dormant-account-event-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-deposits-dormant-account-event", "packageName": "pb", "dtoName": "DepositsDormantAccount", "offsetType": "oldest"}, "accountUpdateKafkaConfig": {"brokers": ["127.0.0.1:9092"], "clientID": "account-update-event-local", "clusterType": "critical", "enableTLL": false, "stream": "local-account-update-event", "packageName": "pb", "dtoName": "AccountStatusUpdateEvent", "offsetType": "oldest"}, "depositsAccountDetailEventKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "deposits-account-detail-event-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-deposits-account-detail-event", "packageName": "pb", "dtoName": "AccountDetail", "offsetType": "oldest"}, "depositsPostingBatchEventKafkaConfig": {"brokers": ["127.0.0.1:9092"], "clientID": "deposits-posting-batch-event-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-deposits-core-tx", "packageName": "pb", "dtoName": "DepositsCoreTx", "offsetType": "oldest"}, "depositsNotificationEventKafkaConfig": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "account-service-deposits-notification-event-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-notification-event", "packageName": "pb", "dtoName": "DepositsCustomerNotification", "syncprod": true, "requiredAcks": -1}, "pocketLifecycleEventKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "account-service-client-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-pocket-lifecycle-event", "packageName": "pb", "dtoName": "PocketLifecycle", "offsetType": "oldest"}, "customerJournalEventKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "account-service-client-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-audit-log", "packageName": "pb", "dtoName": "AuditLog", "offsetType": "oldest"}, "accountPendingActionKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "account-service-client-local", "clusterType": "critical", "enableTLL": false, "stream": "dev-account-pending-action-event", "packageName": "pb", "dtoName": "AccountPendingAction", "offsetType": "oldest"}, "depositsAccountStatusUpdateEventKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "account-status-update-event-local", "clusterType": "critical", "enableTLL": true, "stream": "dev-deposits-core-account-status-update-event", "packageName": "pb", "dtoName": "DepositsAccountStatusUpdateEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enable": true}, "accountServiceDormantAccountEventKafkaConfig": {"brokers": ["localhost:9092"], "clientID": "account-service-dormant-account-event-dev", "clusterType": "critical", "enableTLL": true, "stream": "dev-account-service-dormant-account-event", "packageName": "pb", "dtoName": "AccountServiceDormantAccountEvent", "offsetType": "oldest", "maxRetryCount": 5, "delayInMilliSeconds": 100, "enable": true}, "depositsCoreConfig": {"baseURL": "https://debug.sgbank.dev/deposits-core"}, "customerMasterConfig": {"baseURL": "https://debug.sgbank.dev/customer-master"}, "customerExperienceConfig": {"baseURL": "http://customer-experience.onboarding.svc.cluster.local"}, "goalCoreConfig": {"baseURL": "https://debug.sgbank.dev/goal-core", "maxGoalAmountInCent": *********}, "pixieConfig": {"baseURL": "https://debug.sgbank.dev/pixie"}, "hermesConfig": {"baseURL": "https://debug.sgbank.st/hermes"}, "pigeonConfig": {"baseURL": "https://debug.sgbank.st/pigeon"}, "productMasterConfig": {"baseURL": "https://debug.sgbank.dev/product-master"}, "loanCoreConfig": {"baseURL": "https://cb-debug.sgbank.dev/loan-core"}, "depositsExpConfig": {"baseURL": "http://localhost:8082"}, "appian": {"hostAddress": "https://digibankdev.appiancloud.com", "clientID": "account-service", "circuitConfig": {"appian": {"timeout": 15}}, "registeredClientID": "{{APPIAN_CLIENT_ID}}", "registeredClientSecret": "{{APPIAN_CLIENT_SECRET}}", "grantType": "client_credentials"}, "featureFlags": {"enableR2": true, "enableClientCloseConnHandling": true, "_enableClientCloseConnHandling": "this is a release feature flag, it should be clean up after release", "enableRemoveSchedulerInAsync": false, "_enableRemoveSchedulerInAsync": "this is a release feature flag to enable the remove scheduler flow in async way, it should be clean up after release", "enableReplicaRead": true, "enableAsyncAccountCreation": true, "enableLending": false, "enableMicrosaver": true, "enableMsmeFlexiCredit": true, "enableBIZAuthorisation": true, "enableNADDecryption": false, "enableAccountDormant": true, "enableLOCAccountCreationEventPublish": true, "enableRetryableStream": true, "enableDepositsCreationRetryableStream": true, "enableBoostPocket": true, "enableAuthzWithProfileID": true}, "locale": {"currency": "MYR", "countryCode": "MY", "timezone": "MYT"}, "workerConfig": {"closePendingActivationAccountConf": {"lockDurationInMinutes": 200, "cronExpression": "0 17 * * *", "enabled": true, "batchLimit": 10000, "globalBatchStartTime": "2024-08-01T00:00:00Z", "queryBatchSizeInDays": 10}, "autoCloseBizOAPendingActivationAccount": {"globalBatchStartTime": "2024-08-01T00:00:00Z", "queryBatchSizeInDays": 10}, "closePendingFirstDepositAccountConf": {"lockDurationInMinutes": 200, "cronExpression": "0 18 * * *", "enabled": true}, "pendingActivationAutoCloseReminderNotificationConf": {"lockDurationInMinutes": 200, "cronExpression": "0 3 * * *", "enabled": true, "rateLimitMilliSecond": 150}, "pendingFirstDepositAutoCloseReminderNotificationConf": {"lockDurationInMinutes": 200, "cronExpression": "0 3 * * *", "enabled": true, "rateLimitMilliSecond": 150}, "bizOAPendingFirstDepositAutoCloseReminderNotificationConf": {"rateLimitMilliSecond": 150}}, "tenant": "MY", "notificationConfig": {"holdcodeAccountBlockNotification": {"PushTemplate": "", "EmailTemplate": "holdcode_email_block_account"}, "holdcodeAccountLockNotification": {"PushTemplate": "", "EmailTemplate": "holdcode_email_lock_account"}, "holdcodeAccountUnblockNotification": {"PushTemplate": "holdcode_push_unblock_account", "EmailTemplate": "holdcode_email_unblock_account"}, "holdcodeAccountUnlockNotification": {"PushTemplate": "holdcode_push_unlock_account", "EmailTemplate": "holdcode_email_unlock_account"}, "bizOAHoldcodeAccountBlockNotification": {"PushTemplate": "", "EmailTemplate": "biz_oa_holdcode_email_block_account"}, "bizOAHoldcodeAccountUnblockNotification": {"PushTemplate": "biz_oa_holdcode_push_unblock_account", "EmailTemplate": "biz_oa_holdcode_email_unblock_account"}, "holdCodeBankRestrictedBlockAccountNotification": {"PushTemplate": "holdcode_push_bank_restricted_block_account", "EmailTemplate": "holdcode_email_bank_restricted_block_account"}, "holdCodeBankRestrictedUnblockAccountNotification": {"PushTemplate": "holdcode_push_bank_restricted_unblock_account", "EmailTemplate": "holdcode_email_bank_restricted_unblock_account"}, "holdCodeMissingDeviceBlockAccountNotification": {"PushTemplate": "", "EmailTemplate": "holdcode_email_missing_device_block_account"}, "holdCodeMissingDeviceUnblockAccountNotification": {"PushTemplate": "holdcode_push_missing_device_unblock_account", "EmailTemplate": "holdcode_email_missing_device_unblock_account"}, "savingsPocketClosureNotification": {"PushTemplate": "", "EmailTemplate": ""}, "savingsPocketCreationNotification": {"PushTemplate": "", "EmailTemplate": ""}, "boostPocketCreationNotification": {"PushTemplate": "boost_pocket_creation_push", "EmailTemplate": "boost_pocket_creation_email"}, "boostPocketCreationFailedGenericNotification": {"PushTemplate": "boost_pocket_creation_failed_generic_push", "EmailTemplate": ""}, "boostPocketCreationFailedAvailabilityNotification": {"PushTemplate": "boost_pocket_creation_failed_high_demand_push", "EmailTemplate": ""}, "boostPocketCreationFailedBalanceNotification": {"PushTemplate": "boost_pocket_creation_failed_balance_push", "EmailTemplate": ""}, "pendingActivationAutoCloseReminderNotification": {"PushTemplate": "pending_activation_auto_close_reminder_push", "EmailTemplate": "pending_first_fund_auto_close_reminder_email"}, "bizOAPendingActivationAutoCloseReminderNotification": {"PushTemplate": "biz_oa_pending_activation_auto_close_reminder_push", "EmailTemplate": "biz_oa_pending_activation_auto_close_reminder_email"}, "pendingFirstFundAutoCloseReminderNotification": {"PushTemplate": "pending_action_auto_close_reminder_push", "EmailTemplate": "pending_first_fund_auto_close_reminder_email"}, "locAccountPendingClosureNotification": {"PushTemplate": "loc_closure_processing_push", "EmailTemplate": "loc_closure_processing_email"}, "locAccountFailedClosureNotification": {"PushTemplate": "loc_closure_failed_push", "EmailTemplate": "loc_closure_failed_email"}, "locAccountClosedNotification": {"PushTemplate": "loc_closure_completed_push", "EmailTemplate": "loc_closure_completed_email"}, "closedAccountPendingActivationNotification": {"PushTemplate": "", "EmailTemplate": "closed_account_pending_activation_email"}, "autoClosedBizOAPendingActivationNotification": {"PushTemplate": "", "EmailTemplate": "auto_closed_biz_oa_account_pending_activation_email"}, "reactivatedDormantAccountNotification": {"PushTemplate": "reactivated_dormancy_push", "EmailTemplate": "reactivated_dormancy_email"}, "dormantAccountNotification": {"PushTemplate": "", "EmailTemplate": "dormancy_email"}, "bizHoldCodeBankRestrictedBlockAccountNotification": {"PushTemplate": "", "EmailTemplate": "biz_holdcode_email_bank_restricted_block_account"}, "bizHoldCodeBankRestrictedUnblockAccountNotification": {"PushTemplate": "biz_holdcode_push_bank_restricted_unblock_account", "EmailTemplate": "biz_holdcode_email_bank_restricted_unblock_account"}}, "workflowRetryConfig": {"createCASAAccountV3": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 5, "maxTimeWindowInSeconds": 86400}}, "createLOCAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}, "auxiliary": {"intervalInSeconds": 30, "maxAttempt": 5}}, "deactivateLOCAccount": {"auxiliary": {"intervalInSeconds": 30, "maxAttempt": 10}}, "closeLOCAccount": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10}}, "updateCASAAccountStatusV2": {"transactional": {"intervalInSeconds": 30, "maxAttempt": 10, "maxTimeWindowFromCreatedAt": 30}}}, "redisConfig": {"addr": "localhost:30001", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1, "timeout": 500, "command_name": "redis_AcntServ", "command_group": "AccountService", "max_concurrent_request": 1000, "error_percent_threshold": 20, "sleep_window": 5000, "max_concurrent_requests": 1000}, "slackConfig": {"token": "{{SLACK_TOKEN}}", "defaultChannelID": "channel-id", "enabled": true}, "boostPocketConfig": {"whitelistAllUsers": true, "isBetaEnabled": false, "isLimitControlEnabled": false, "betaUserSafeIDList": [], "enableBoostPocketWhitelistCaching": false, "enableScheduledMaintenanceWindow": false}, "messagesToUser": {"boostPocketCreationFreezeMessage": {"code": "BP_CREATION_BLOCKED", "message": "We're unable to create your Boost pocket due to high demand. Please check back again soon!"}, "boostPocketCreationNumberLimitReachedMessage": {"code": "BP_CREATION_BLOCKED", "message": "We are unable to create your Boost pocket due to maximum limit of #maxLimit Boost Pocket(s) is reached"}}, "queryCacherConfig": {"redisKeyPrefix": "qcAccountService", "statsdContextTag": "qcAccountService", "queryConfigs": {"listCASAAccountsForCustomer": {"enable": true, "enableReadCache": true, "enableCacheStats": true, "cacheEntryVersion": 1, "cachedResultTtlMs": 2000, "readCacheTimeoutMs": 60, "updateCacheTimeoutMs": 2000}, "getCASAAccount": {"enable": true, "enableReadCache": true, "enableCacheStats": true, "cacheEntryVersion": 1, "cachedResultTtlMs": 2000, "readCacheTimeoutMs": 60, "updateCacheTimeoutMs": 2000}}}}