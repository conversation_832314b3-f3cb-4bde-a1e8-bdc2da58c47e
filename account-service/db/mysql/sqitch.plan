%syntax-version=1.0.0
%project=account-service
%uri=https://gitlab.myteksi.net/dbmy/core-banking/-/tree/master/account-service

0000-customer_account 2021-05-06T04:10:24Z <PERSON><PERSON> Mathew <<EMAIL>> # Adds the customer_account table
0001-customer_account_status_history 2021-05-07T05:02:53Z <PERSON>bin Mathew <<EMAIL>> # Adds the customer_account_status_history table
0002-request_log 2021-05-23T16:12:24Z <PERSON><PERSON> Mathew <<EMAIL>> # Adds the request_log table
0003-pocket 2022-05-17T09:00:48Z System Administrator <root@ITIN000593-MAC> # Add Pocket schema
0004-pocket_template_answer 2022-05-17T09:51:33Z System Administrator <root@ITIN000593-MAC> # Add Pocket-template-answer schema
0005-pending_action 2023-02-09T07:22:24Z daniel.lee <<EMAIL>> # Create pending_actions table
0006-scheduler_lock 2023-03-14T15:41:16Z daniel.lee <<EMAIL>> # Create scheduler_lock table
0007-cif_number_index 2023-12-02T07:01:16Z daniel.lee <<EMAIL>> # Create cif_number index
0008-request_id_index 2024-01-16T03:47:00Z daniel.lee <<EMAIL>> # Create request_id index
increase_datetime_precision 2023-05-09T09:19:15Z Chun Pin Hiew <<EMAIL>> # Increase datetime precision for all datetime columns
0005-customer_account_unique_constraint 2022-12-27T15:03:02Z zaw.latt <<EMAIL>> # Add unique constraint for name and cif fields
0006-customer_account_unique_constraint_cifnumber 2022-12-27T15:27:32Z zaw.latt <<EMAIL>> # Add unique constraint for cif-number field
0007-add_sub_status_column 2023-06-20T15:27:32Z zaw.latt <<EMAIL>> # Add sub_status field to account table
0008-workflow_execution 2023-06-21T15:27:32Z zaw.latt <<EMAIL>> # Add sub_status field to account table
0010-customer_account_product_variant_id_column_size 2024-10-17T07:40:26Z mradul.agrawal <<EMAIL>> # Modify product variant id column in customer account table