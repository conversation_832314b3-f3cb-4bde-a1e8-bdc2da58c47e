include:
  - project: dbmy/ci
    ref: master
    file: pipelines/go/service.yml

variables:
  CMD_NAME: account-service
  ECR_URI: ************.dkr.ecr.ap-southeast-1.amazonaws.com/account-service
  MODULE_PATH: account-service
  UNITTEST_MIN_COVERAGE: 75
  ALL_DEPRECATED_INITS_MIGRATED: 'true'
#  DISABLE_TAG_RC: "true" # disable this until we figure out how to tag our own api module
  DISABLE_VERIFY_MYSQL: "true"
  DISABLE_BUNDLE_MYSQL: "true"
  DISABLE_PACKAGE_DOCKER_MYSQL: "true"
  DISABLE_SAST: "true"
  SEMVER_SUFFIX: "-dbmy"
  REPO_SPECIFIC_IGNORE_PATHS: /mock_|storage/z_|storage/(database_store|workflowexecutor_dao|workflow)\.go|.*routes\.go|tenant.go|worker.go|worker/utils|server|example/*|mockservices|utils/client/|workflow\.go|\.bersama\.go|consumer/consumer\.go|constants/|mockpubsub|internal\.handler\.go|\.test\.go|mysql/queries|noop|kafka/consumer
  DISABLE_DEPLOY_DEV: 'true'
  GO_VERSION: 1.24.1a
  GOLINT_VERSION: 'v1.64.2-alpine-a'
