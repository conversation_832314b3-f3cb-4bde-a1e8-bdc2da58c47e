syntax = "proto3";

package accountService;

option go_package = "gitlab.myteksi.net/dbmy/core-banking/account-service/api";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "gxs/api/annotations.proto";

message LookUpAccountParentRequest{
    string accountID = 1;
}

message LookUpAccountParentResponse{
    string accountID = 1;
    string parentAccountID = 2;
}

message GetAccountRequest {
    string accountID = 1;
    bool fetchBalance = 2;
}

message GetAccountResponse {
    Account account = 1;
}

message CheckPermissionsForAccountRequest{
    string accountID = 1;
    string cifNumber = 2;
}

message CheckPermissionsForAccountResponse{
    AccountPermission status = 1;
}

message CreateCASAAccountRequest {
    string productVariantCode = 1;
    string cifNumber = 2;
    string createdBy = 3;
    CASAAccountParams instanceParams = 4;
    string parentAccountID = 5;
    string name = 6;
    string idempotencyKey = 7;
    PocketTemplateDetail pocketTemplateDetail = 8;
}

message CreateCASAAccountResponse {
    CASAAccount account = 1;
    SavingsPocket savingsPocket = 2;
    string createdBy = 3;
}

message BoostPocketParameters {
    int64 principalBalance = 1;
    string fundSourceAccount = 2;
    MaturityInstructionType maturityInstructionType = 3;
    CalendarDate calendarDate = 4;
    PocketTemplateDetail pocketTemplateDetail = 5;
}

message CASACreationParameters {
    BoostPocketParameters boostPocket = 1;
}

message CreateCASAAccountV3Request {
    string productVariantCode = 1;
    string cifNumber = 2;
    string createdBy = 3;
    CASAAccountParams instanceParams = 4;
    string parentAccountID = 5;
    string name = 6;
    string idempotencyKey = 7;
    CASACreationParameters productParameters = 8;
}

message CreateCASAAccountV3Response {
    string requestIdempotencyKey = 1;
    string operation = 2;
    AccountStatus requestStatus = 3;
    string createdBy = 4;
}

message GetCASAAccountRequest{
    string accountID = 1;
}

message GetCASAAccountResponse{
    CASAAccount account = 1;
}

message GetAccountBalanceRequest{
    string accountID = 1;
}

message GetAccountBalanceResponse{
    string accountID = 1;
    Money availableBalance = 2 [(gxs.api.noomit) = true];
    repeated AccountBalance balances = 3;
}

message GetCASAAccountSummaryRequest {
    string accountID = 1;
}

message GetCASAAccountSummaryResponse {
    Money totalBalance = 1 [(gxs.api.noomit) = true];
    DailyPocketSummary dailyPocketSummary = 2;
    SavingsPocketSummary savingsPocketSummary = 3;
}

message ListCASAAccountsForCustomerResponse{
    repeated CASAAccountWithBalance accounts = 1;
}

message ListCASAAccountsForCustomerDetailRequest{
    string cifNumber = 1;
    bool fetchBalance = 2;
    repeated string productVariantIDs = 3;
    repeated string status = 4;
    int64 limit = 5;
    int64 page = 6;
}

message ListCASAAccountsForCustomerDetailResponse{
    repeated CASAAccountDetail accounts = 1;
    int64 maxChildAccountLimit = 2;
}

message UpdateCASAAccountStatusRequest{
    string accountID = 1;
    AccountStatus status = 2;
    repeated string reasons = 3;
    google.protobuf.Timestamp closingTimestamp = 4;
    string updatedBy = 5;
}

message UpdateCASAAccountStatusResponse{
    string accountID = 1;
    AccountStatus status = 2;
    string updatedBy = 3;
}

message UpdateCASAAccountStatusV2Request{
    string idempotencyKey = 1;
    string accountID = 2;
    AccountStatus status = 3;
    repeated string reasons = 4;
    string updatedBy = 5;
}

message UpdateCASAAccountStatusV2Response{
    string requestIdempotencyKey = 1;
    string accountID = 2;
    string operation = 3;
    AccountStatus requestStatus = 4;
}

message UpdateCASAAccountParametersRequest{
    string accountID = 1;
    CASAAccountParams productSpecificParameters = 2;
    string updatedBy = 3;
}

message UpdateCASAAccountParametersResponse{
    string accountID = 1;
    string status = 2;
    string failureReason = 3;
}

message  GetRequestStatusRequest {
    string requestID = 1;
    string operation = 2;
}

message  GetRequestStatusResponse {
    string requestID = 1;
    string accountID = 2;
    string operation = 3;
    RequestStatus.Status requestStatus = 4;
    string errorMessage = 5;
    map<string,string> metadata = 6;
}

message CreateSavingsPocketRequest{
    string idempotencyKey = 1;
    string accountID = 2;
    PocketTemplateDetail pocketTemplateDetail = 3;
    SavingsPocketParams instanceParams = 4;
}

message CreateSavingsPocketResponse{
    SavingsPocket savingsPocket = 1;
}

message GetSavingsPocketDetailRequest{
    string pocketID = 1;
    string goalID = 2;
    string schedulerID = 3;
}

message GetSavingsPocketDetailResponse{
    SavingsPocketDetail savingsPocketDetail = 1;
}

message GetSavingsPocketRequest {
    string pocketID = 1;
}

message GetSavingsPocketResponse{
    SavingsPocket savingsPocket = 1;
}

message ListSavingsPocketDetailRequest{
    string accountID = 1;
}

message ListSavingsPocketDetailResponse{
    Money totalBalance = 1 [(gxs.api.noomit) = true];
    repeated SavingsPocketDetail savingsPocketDetail = 2;
}

message UpdateSavingsPocketNameRequest {
    string idempotencyKey = 1;
    string pocketID = 2;
    string name = 3;
}

message UpdateSavingsPocketNameResponse {
    string pocketID = 1;
    string name = 2;
}

message UpdateSavingsPocketImageRequest {
    string idempotencyKey = 1;
    string pocketID = 2;
    string imageID = 3;
}

message UpdateSavingsPocketImageResponse {
    string pocketID = 1;
    string imageID = 2;
}

message UpdateSavingsPocketStatusRequest {
    string idempotencyKey = 1;
    string pocketID = 2;
    AccountStatus status = 3;
}

message UpdateSavingsPocketStatusResponse {
    string pocketID = 1;
    AccountStatus status = 2;
}

message CloseSavingsPocketRequest {
    string idempotencyKey = 1;
    string pocketID = 2;
}

message CloseSavingsPocketResponse {
    string pocketID = 1;
    AccountStatus status = 2;
}

message CreateGoalRequest {
    string idempotencyKey = 1;
    string pocketID = 2;
    int64 targetAmount = 3;
    google.protobuf.Timestamp targetDate = 4;
}

message CreateGoalResponse {
    Goal goal = 1;
}

message GetGoalRequest {
    string goalID = 1;
    string pocketID = 2;
}

message GetGoalResponse {
    Goal goal = 1;
}

message UpdateGoalRequest{
    string idempotencyKey = 1;
    string goalID = 2;
    string pocketID = 3;
    int64 targetAmount = 4;
    google.protobuf.Timestamp targetDate = 5;
}

message UpdateGoalResponse {
    Goal goal = 1;
}

message CloseGoalRequest {
    string idempotencyKey = 1;
    string goalID = 2;
    string pocketID = 3;
}

message CloseGoalResponse {
    Goal goal = 1;
}

message Account {
    string id = 1;
    string name = 6;
    string parentAccountID = 2;
    string cifNumber = 3;
    string accountType = 14;
    repeated string permittedCurrencies = 8;
    Money availableBalance = 12 [(gxs.api.noomit) = true];
    AccountStatus status = 4;
    string productID = 9;
    string productVariantID = 10;
    map<string, string> productSpecificParameters = 5;
    google.protobuf.Timestamp openingTimestamp = 11;
    repeated AccountPendingAction pending_actions = 13 [(gxs.api.noomit) = true];
}

message CASAAccountParams {
    repeated ApplicableHoldcode applicableHoldcodes = 1 [(gxs.api.noomit) = true];
}

message CASAAccount {
    string id = 1;
    string name = 2;
    string parentAccountID = 3;
    string cifNumber = 4;
    repeated string permittedCurrencies = 5;
    AccountStatus status = 6;
    string productID = 7;
    string productVariantID = 8;
    string productVersionID = 9;
    map<string, string> productSpecificParameters = 10;
    map<string, string> derivedParams = 11;
    google.protobuf.Timestamp openingTimestamp = 12;
    google.protobuf.Timestamp closingTimestamp = 13;
    repeated AccountPendingAction pendingActions = 14 [(gxs.api.noomit) = true];
}

message AccountBalance{
    string accountAddress = 1;
    string phase = 2;
    Money amount = 3;
}

// Represents an amount of money with its currency type.
message Money {
    // The three-letter currency code defined in ISO 4217.
    string currencyCode = 1 [(gxs.api.noomit) = true];
    // Monetary value using a currency's smallest unit
    int64 val = 2 [(gxs.api.noomit) = true];
}

message CASAAccountWithBalance {
    string id = 1;
    string productVariantID = 2;
    AccountStatus status = 3;
    Money availableBalance = 4 [(gxs.api.noomit) = true];
    repeated AccountBalance balances = 5;
}

message CASAAccountDetail {
    string id = 1;
    string parentAccountID = 2;
    string productVariantID = 3;
    string accountType = 4;
    string accountName = 5;
    AccountStatus status = 6;
    Money availableBalance = 7;
    map<string, string> productSpecificParameters = 8 [(gxs.api.noomit) = true];
    Feature features = 9;
    google.protobuf.Timestamp openingTimestamp = 10;
    Money onHoldAmount = 11;
    Balance balance = 12;
}

message Balance {
    Money availableBalance = 1;
    Money ledgerBalance = 2;
    Money earmarkedBalance = 3;
}


message Feature {
    bool credit = 1;
    bool debit = 2;
}

message DailyPocketSummary{
    Money balance = 1 [(gxs.api.noomit) = true];
}

message SavingsPocketSummary{
    Money balance = 1;
    int32 count = 2;
    repeated string images = 3;
    repeated PocketImageDetail pocketImageDetails = 4;
}

message PocketImageDetail{
    string pocketID = 1;
    string pocketImageURL = 2;
    string pocketImageID = 3;
    ImageStatus pocketImageStatus = 4;
}

message SavingsPocket {
    string id = 1;
    string name = 2;
    string accountID = 3;
    AccountStatus status = 4;
}

message PocketTemplateDetail{
    string templateID = 1;
    repeated PocketTemplateQuestionAnswer questionAnswerPairs = 2;
    string customName = 3;
    string imageID = 4;
}

message PocketTemplateQuestionAnswer{
    string templateQuestionID = 1;
    string answerText = 2;
}

message SavingsPocketParams{
}

message SavingsPocketDetail {
    string ID = 1;
    string accountID = 2;
    string name = 3;
    Image image = 4;
    repeated Image imageSuggestions = 5;
    Money balance = 6;
    AccountStatus status = 7;
    GoalDetail goalDetail = 8;
    SchedulerDetail schedulerDetail = 9;
    string ProductVariantCode = 10;
}

message Image {
    string ID = 1;
    string URL = 2;
    ImageStatus status = 3;
}

message Goal {
    string ID = 1;
    string pocketID = 2;
    int64 targetAmount = 3;
    Currency currency = 4;
    google.protobuf.Timestamp targetDate = 5;
    GoalStatus.Status status = 6;
}

message GoalDetail {
    string ID = 1;
    Money targetAmount = 2 [(gxs.api.noomit) = true];
    google.protobuf.Timestamp targetDate = 3;
    GoalStatus.Status status = 4;
    uint32 offtrackPercentage = 5 [(gxs.api.noomit) = true];
    uint32 ontrackPercentage = 6 [(gxs.api.noomit) = true];
    Money outstandingBalance = 7 [(gxs.api.noomit) = true];
}

message SchedulerDetail {
    string schedulerID = 1;
    string schedulerName = 2;
    string status = 3;
    Money amount = 4;
    string startDate = 5;
    string endDate = 6;
    string frequencyType = 7;
    string frequencyLabel = 8;
    int64 repeatCount = 9;
}

message GetGoalEstimateRequest {
    string pocketID = 1;
    int64 targetAmount = 2;
    google.protobuf.Timestamp targetDate = 3;
}

message GetGoalEstimateResponse {
    string pocketID = 1;
    Money targetAmount = 2 [(gxs.api.noomit) = true];
    Money estimatedMonthlyContribution = 3 [(gxs.api.noomit) = true];
    Money customerTotalContribution = 4 [(gxs.api.noomit) = true];
    Money estimatedInterestEarned = 5 [(gxs.api.noomit) = true];
}

message GetAccountDetailsRequest {
    string accountType = 1;
    repeated string accountIDs = 2;
}

message GetAccountDetailsResponse {
    map<string, Image> accountImageDetails = 1;
    map<string, string> accountNameDetails = 2;
}

message MicroSaverInfo {
    string nominalAmount = 1;
    bool isBoosterFlagEnabled = 2;
}
message CreateTermLoanAccountRequest {
    string idempotencyKey = 1;
    string locAccountID  = 2 ;
    string termLoanAccountID = 3;
    string accountName = 4;
    string productCode = 5;
    string productVariantCode = 6;
    string productVariantVersion = 7;
    string cifNumber = 8;
    string currentStatus = 9;
    string createdBy = 10;
    google.protobuf.Timestamp openingTimestamp = 11;
    string countryCode = 12;
    string currencyCode = 13;
    string subStatus = 14;
}

message CreateTermLoanAccountResponse{
    string termLoanAccountID = 1;
    string createdBy = 2;
}

message CreateLOCAccountRequest {
    string idempotencyKey = 1;
    string productVariantCode = 2;
    string cifNumber = 3;
    LOCInstanceParameters instanceParameters = 4;
    string webhookURL = 5;
    string createdBy = 6;
    string applicationID = 7;
}

message LOCInstanceParameters {
    CreateLOCMoney offeredLOC = 1;
    float offeredInterestRate = 2;
    int64 offeredMaxTenor = 3;
}

// Represents an amount of money with its currency type.
message CreateLOCMoney {
    // The three-letter currency code defined in ISO 4217.
    string currencyCode = 1;
    // Monetary value using a currency's smallest unit
    double val = 2;
}

message CreateLOCAccountResponse {
    LOCAccountDetail LOC = 1;
}

message LOCAccountDetail {
    string referenceID = 1;
    string accountID = 2;
    string productID = 3;
    string productVariantCode = 4;
    string productVersionID = 5;
    repeated string permittedCurrencies = 6;
    string cifNumber = 7;
    LOCAccountStatus.Status status = 8;
    string statusReason = 9;
    string statusReasonDescription = 10;
    google.protobuf.Timestamp openingTimestamp = 11;
    google.protobuf.Timestamp closingTimestamp = 12;
    map<string, string> instanceParameters = 13;
    map<string, string> derivedParameters = 14;
    string createdBy = 15;
    string webhookURL = 16;
}

message GetLendingAccountBankDetailsRequest {
    string productVariantCode = 1;
}

message LendingAccountBankDetail {
    string accountID = 1;
    string bankCode = 2;
    string bankName = 3;
    string branchCode = 4;
    string beneficiaryName = 5;
    string status = 6;
    string productVariantCode = 7;
}
message GetLendingAccountBankDetailsResponse {
    repeated LendingAccountBankDetail lendingAccountBankDetails = 1;
}

message AccountDetailForCustomer{
    string accountID = 1;
    string status = 2;
    string accountType = 3;
    string productVariantCode = 4;
    string subStatus = 5;
}

// UpdateTermLoanAccountStatusRequest : request format to update term loan account status
message UpdateTermLoanAccountStatusRequest{
    string accountID = 1;
    TermLoanAccountStatus.Status status = 2;
    TermLoanAccountSubStatus.SubStatus subStatus = 3;
    google.protobuf.Timestamp closingTimestamp = 4;
    string updatedBy = 5;
}

// UpdateTermLoanAccountStatusResponse : response format for term loan account status
message UpdateTermLoanAccountStatusResponse{
    string accountID = 1;
    TermLoanAccountStatus.Status status = 2;
    TermLoanAccountSubStatus.SubStatus subStatus = 3;
    string updatedBy = 4;
}

message LOCAccountClosureRequest{
    string accountID = 1;
    string idempotencyKey = 2;
    string comment = 3;
    repeated string reasons = 4;
}

message LOCAccountClosureResponse{
    string status = 1;
    string statusReason = 2;
    string statusReasonDescription = 3;
    string accountID = 4;
    string idempotencyKey = 5;
    google.protobuf.Timestamp createdAt = 6;
}

// LOCAccountDeactivationRequest...
message LOCAccountDeactivationRequest{
    string idempotencyKey = 1;
    string productVariantCode = 2;
    string safeID = 3;
    string parentAccountID = 4;
    map<string,string> metadata = 5;
    string reasonCode = 6;
    string createdBy = 7;
}

// LOCAccountDeactivationResponse...
message LOCAccountDeactivationResponse{
    string idempotencyKey = 1;
    string productVariantCode = 2;
    string safeID = 3;
    string parentAccountID = 4;
    string status = 5;
    string statusReason = 6;
    string statusDescription = 7;
    string reasonCode = 8;
    string createdBy = 9;
}

// UpdateLOCAccountStatusRequest : request format to update loc account status
message UpdateLOCAccountStatusRequest{
    string accountID = 1;
    LOCAccountStatus.Status status = 2;
    LOCAccountSubStatus.SubStatus subStatus = 3;
    google.protobuf.Timestamp closingTimestamp = 4;
    string updatedBy = 5;
}

// UpdateLOCAccountStatusResponse : response format for loc account status
message UpdateLOCAccountStatusResponse{
    string accountID = 1;
    LOCAccountStatus.Status status = 2;
    LOCAccountSubStatus.SubStatus subStatus = 3;
    string updatedBy = 4;
}


enum AccountPermission{
    ALLOWED = 0;
    FORBIDDEN = 1;
}

enum Currency{
    SGD = 0;
    XXX = 1;
    IDR = 2;
    MYR = 3;
}

enum AccountStatus {
    ACTIVE = 0;
    DORMANT = 1;
    CLOSED = 2;
    CANCELLED = 3;
    PENDING = 4;
    PENDING_ACTIVATION = 5;
    PENDING_CLOSURE = 6;
}

enum InterestPostingFrequency{
    DAYS = 0;
    WEEKS = 1;
    MONTHS = 2;
    YEARS = 3;
}

enum ApplicableHoldcode{
    ADVERSE_NEWS = 0;
    HITS = 1;
    BANKRUPT = 2;
    DECEASED = 3;
    FRAUD_ACTOR = 4;
    FRAUD_VICTIM = 5;
    MENTAL_INCAPACITY = 6;
    MISSING_DEVICE = 7;
    MULE_ACCOUNT = 8;
    ORDER = 9;
    KILL_SWITCH = 10;
    GARNISHEE_OR_CLAWBACK = 11;
    BANK_RESTRICTED = 12;
}

message GoalStatus{
    enum Status {
        EMPTY = 0;
        OFFTRACK = 1;
        ONTRACK = 2;
        COMPLETED = 3;
        CLOSED = 4;
    }
}

enum ImageStatus {
    PROCESSING = 0;
    SUCCESS = 1;
    FAILED = 2;
}

message RequestStatus {
    enum Status {
        PROCESSING = 0;
        SUCCESS = 1;
        FAILED = 2;
    }
}

message AccountPendingAction{
    string ID = 1 [(gxs.api.noomit) = true];
    string name = 2 [(gxs.api.noomit) = true];
    string status = 3 [(gxs.api.noomit) = true];
}

message LOCAccountStatus {
    enum Status {
        ACTIVE = 0;
        CLOSED = 1;
        PENDING_ACTIVE = 2;
        CANCELLED = 3;
        PENDING_CLOSURE = 4;
    }
}

message LOCAccountSubStatus {
    enum SubStatus {
        NORMAL = 0;
        LIMITED = 1;
        NON_TERMINAL = 2;
        TERMINAL = 4;
    }
}

message TermLoanAccountStatus {
    enum Status {
        ACTIVE = 0;
        WRITTEN_OFF = 1;
        CLOSED = 2;
        SETTLED_FULL = 3;
        SETTLED_NEGOTIATED = 4;
        SOLD_OFF = 5;
    }
}

message TermLoanAccountSubStatus {
    enum SubStatus {
        NORMAL = 0;
        DEFAULT = 1;
        BAD_DEBT = 2;
        OPS_LOSS = 3;
        WAIVED = 4;
        RECOVERED =5;
    }
}

message ReactivateDormantCasaAccountRequest{
    string idempotencyKey = 1;
    string accountID = 2;
    string updatedBy = 3;
}
message ReactivateDormantCasaAccountResponse{
    string requestIdempotencyKey = 1;
    string accountID = 2;
    string operation = 3;
    string status = 4;
}

enum MaturityInstructionType {
    NO_AUTO_RENEW = 0;
    AUTO_RENEW = 1;
}

message CalendarDate {
    int32 day = 1;
    int32 month = 2;
    int32 year = 3;
}

// AccountService
service AccountService {

    // LookUpAccountParent look up parent value for the mentioned accountID
    rpc LookUpAccountParent (LookUpAccountParentRequest) returns (LookUpAccountParentResponse) {
        option (google.api.http) = {
            get: "/v1/accounts/{accountID}/parent",
            body: "*",
        };
    }

    // GetAccount fetches account details
    rpc GetAccount (GetAccountRequest) returns (GetAccountResponse) {
        option (google.api.http) = {
            get: "/v1/accounts/{accountID}",
            body: "*",
        };
    }

    // CheckPermissionsForAccount checks permission between account and customer
    rpc CheckPermissionsForAccount (CheckPermissionsForAccountRequest) returns (CheckPermissionsForAccountResponse) {
        option (google.api.http) = {
            get: "/v1/casa-accounts/{accountID}/permissions",
            body: "*",
        };
    }

    // CreateCASAAccount creates a new casa account
    rpc CreateCASAAccount (CreateCASAAccountRequest) returns (CreateCASAAccountResponse){
        option (google.api.http) = {
            post: "/v2/casa-accounts",
            body: "*",
        };
    }

    // CreateCASAAccountV3 creates a new casa account
    rpc CreateCASAAccountV3 (CreateCASAAccountV3Request) returns (CreateCASAAccountV3Response){
        option (google.api.http) = {
            post: "/v3/casa-accounts",
            body: "*",
        };
    }

    // GetCASAAccount fetches details of a casa account
    rpc GetCASAAccount (GetCASAAccountRequest) returns (GetCASAAccountResponse) {
        option (google.api.http) = {
            get: "/v1/casa-accounts/{accountID}",
            body: "*",
        };
    }

    // GetAccountBalance fetches balance of a casa account
    rpc GetAccountBalance (GetAccountBalanceRequest) returns (GetAccountBalanceResponse) {
        option (google.api.http) = {
            get: "/v1/casa-accounts/{accountID}/balances",
            body: "*",
        };
    }

    // GetCASAAccountSummary fetches summary of a casa account
    rpc GetCASAAccountSummary (GetCASAAccountSummaryRequest) returns (GetCASAAccountSummaryResponse) {
        option (google.api.http) = {
            get: "/v1/casa-accounts/{accountID}/summary",
            body: "*",
        };
    }

    // ListCASAAccountsForCustomer list all the details of all casa accounts
    rpc ListCASAAccountsForCustomer (google.protobuf.Empty) returns (ListCASAAccountsForCustomerResponse) {
        option (google.api.http) = {
            get: "/v1/casa-accounts",
            body: "*",
        };
    }

    // ListCASAAccountsForCustomerDetail list all the details of all casa accounts for a cif number
    rpc ListCASAAccountsForCustomerDetail (ListCASAAccountsForCustomerDetailRequest) returns (ListCASAAccountsForCustomerDetailResponse) {
        option (google.api.http) = {
            post: "/v2/accounts/list",
            body: "*",
        };
    }

    // UpdateCASAAccountStatus updates the status of a casa account
    rpc UpdateCASAAccountStatus (UpdateCASAAccountStatusRequest) returns (UpdateCASAAccountStatusResponse) {
        option (google.api.http) = {
            put: "/v1/casa-accounts/{accountID}/status",
            body: "*",
        };
    }

    // V2UpdateCASAAccountStatus updates the status of a casa account in async (deprecated, use UpdateCASAAccountStatusV2)
    rpc V2UpdateCASAAccountStatus (UpdateCASAAccountStatusRequest) returns (UpdateCASAAccountStatusResponse) {
        option (google.api.http) = {
            put: "/v2/casa-accounts/{accountID}/status",
            body: "*",
        };
    }

    // UpdateCASAAccountStatusV2 updates the status of a casa account in async
    rpc UpdateCASAAccountStatusV2 (UpdateCASAAccountStatusV2Request) returns (UpdateCASAAccountStatusV2Response) {
        option (google.api.http) = {
            put: "/v2/casa-accounts/{accountID}/status",
            body: "*",
        };
    }

    // UpdateCASAAccountParameters updates the instance parameters of a casa account
    rpc UpdateCASAAccountParameters (UpdateCASAAccountParametersRequest) returns (UpdateCASAAccountParametersResponse) {
        option (google.api.http) = {
            put: "/v1/casa-accounts/{accountID}/parameters",
            body: "*",
        };
    }

    // GetRequestStatus fetches the Status of current request
    rpc GetRequestStatus(GetRequestStatusRequest) returns (GetRequestStatusResponse) {
        option (google.api.http) = {
            post: "/v1/accounts/get-request-status",
            body: "*",
        };
        option (gxs.api.auth) = {
            client_identities: ["servicename.SentryT6"]
        };
    }

    // CreateSavingsPocket creates a new savings pocket
    rpc CreateSavingsPocket (CreateSavingsPocketRequest) returns (CreateSavingsPocketResponse) {
        option (google.api.http) = {
            post: "/v1/savings-pockets",
            body: "*",
        };
    }

    // GetSavingsPocketDetail fetches all the details of a savings pocket
    rpc GetSavingsPocketDetail (GetSavingsPocketDetailRequest) returns (GetSavingsPocketDetailResponse) {
        option (google.api.http) = {
            post: "/v1/savings-pockets/pocket-detail",
            body: "*",
        };
    }

    // V2GetSavingsPocketDetail fetches all the details of a savings pocket
    rpc V2GetSavingsPocketDetail (GetSavingsPocketDetailRequest) returns (GetSavingsPocketDetailResponse) {
        option (google.api.http) = {
            post: "/v2/savings-pockets/pocket-detail",
            body: "*",
        };
    }

    // GetSavingsPocket fetches savings pocket data
    rpc GetSavingsPocket (GetSavingsPocketRequest) returns (GetSavingsPocketResponse) {
        option (google.api.http) = {
            get: "/v1/savings-pockets/{pocketID}",
            body: "*",
        };
    }

    // ListSavingsPocketDetail fetches details of all savings pockets
    rpc ListSavingsPocketDetail (ListSavingsPocketDetailRequest) returns (ListSavingsPocketDetailResponse) {
        option (google.api.http) = {
            post: "/v1/savings-pockets/summary",
            body: "*",
        };
    }

    // ListSavingsPocketDetail fetches details of all savings pockets
    rpc V2ListSavingsPocketDetail (ListSavingsPocketDetailRequest) returns (ListSavingsPocketDetailResponse) {
        option (google.api.http) = {
            post: "/v2/savings-pockets/summary",
            body: "*",
        };
    }

    // UpdateSavingsPocketName updates the name of a pocket
    rpc UpdateSavingsPocketName (UpdateSavingsPocketNameRequest) returns (UpdateSavingsPocketNameResponse) {
        option (google.api.http) = {
            put: "/v1/savings-pockets/name",
            body: "*",
        };
    }

    // UpdateSavingsPocketImage updates the image of a pocket
    rpc UpdateSavingsPocketImage (UpdateSavingsPocketImageRequest) returns (UpdateSavingsPocketImageResponse) {
        option (google.api.http) = {
            put: "/v1/savings-pockets/image",
            body: "*",
        };
    }

    // UpdateSavingsPocketStatus updates the status of a pocket
    rpc UpdateSavingsPocketStatus (UpdateSavingsPocketStatusRequest) returns (UpdateSavingsPocketStatusResponse) {
        option (google.api.http) = {
            put: "/v1/savings-pockets/status",
            body: "*",
        };
    }

    // CloseSavingsPocket closes a savings pocket
    rpc CloseSavingsPocket (CloseSavingsPocketRequest) returns (CloseSavingsPocketResponse) {
        option (google.api.http) = {
            put: "/v1/savings-pockets/status-close",
            body: "*",
        };
    }

    // CreateGoal creates a new goal
    rpc CreateGoal(CreateGoalRequest) returns (CreateGoalResponse) {
        option (google.api.http) = {
            post: "/v1/savings-pockets/goal",
            body: "*",
        };
    }

    // GetGoal fetches all the details of a goal
    rpc GetGoal(GetGoalRequest) returns (GetGoalResponse) {
        option (google.api.http) = {
            post: "/v1/savings-pockets/get-goal",
            body: "*",
        };
    }

    // UpdateGoal updates the targetDate/targetAmount of goal
    rpc UpdateGoal(UpdateGoalRequest) returns (UpdateGoalResponse) {
        option (google.api.http) = {
            put: "/v1/savings-pockets/goal",
            body: "*",
        };
    }

    // CloseGoal updates the status of goal to CLOSED
    rpc CloseGoal(CloseGoalRequest) returns (CloseGoalResponse) {
        option (google.api.http) = {
            put: "/v1/savings-pockets/goal/status-close",
            body: "*",
        };
    }

    // GetGoalEstimate returns contributions and interest earned
    rpc GetGoalEstimate(GetGoalEstimateRequest) returns (GetGoalEstimateResponse) {
        option (google.api.http) = {
            post: "/v1/savings-pockets/goal/estimate",
            body: "*",
        };
    }

    // GetAccountDetails returns mapping between accounts to the imageURLS and name.
    rpc GetAccountDetails(GetAccountDetailsRequest) returns (GetAccountDetailsResponse) {
        option (google.api.http) = {
            post: "/v1/accounts/account-details",
            body: "*",
        };
    }

    // GetAccountDetailsByAccountID returns Account Details
    rpc GetAccountDetailsByAccountID(GetAccountRequest) returns
        (GetAccountResponse) {
        option (google.api.http) = {
            post: "/v2/accounts/get",
            body: "*",
        };
    }

    // CreateTermLoanAccount : Internal API to create term loan account entry in customer_account table, post creation in TM
    rpc CreateTermLoanAccount(CreateTermLoanAccountRequest) returns (CreateTermLoanAccountResponse){
        option (google.api.http) = {
            post: "/v1/lending/term-loan/create",
            body: "*",
        };
    }

    // CreateLOCAccount creates line of credit account for customer
    rpc CreateLOCAccount(CreateLOCAccountRequest) returns (CreateLOCAccountResponse) {
        option (google.api.http) = {
            post: "/v1/lending/loc/create",
            body: "*",
        };
    }

    // GetLendingAccountDetails returns account details of credit account for customer
    rpc GetLendingAccountBankDetails (GetLendingAccountBankDetailsRequest) returns (GetLendingAccountBankDetailsResponse) {
        option (google.api.http) = {
            get: "/v1/lending/account-bank-details",
            body: "*",
        };
    }

    // UpdateTermLoanAccountStatus : Internal API to update term loan account entry in customer_account table, currently used for closure
    rpc UpdateTermLoanAccountStatus(UpdateTermLoanAccountStatusRequest) returns (UpdateTermLoanAccountStatusResponse){
        option (google.api.http) = {
            put: "/v1/lending/term-loan/status",
            body: "*",
        };
    }

    // CloseLOCAccount API closes a line of credit account for a user
    rpc CloseLOCAccount(LOCAccountClosureRequest) returns (LOCAccountClosureResponse){
        option (google.api.http) = {
            post: "/v1/lending/loc/close",
            body: "*",
        };
    }

    // FlexiLOCDeactivation : API to deactivate line of credit account
    rpc FlexiLOCDeactivation(LOCAccountDeactivationRequest) returns (LOCAccountDeactivationResponse) {
        option (google.api.http) = {
            post: "/v1/lending/loc/deactivate",
            body: "*",
        };
    }

    // UpdateLOCAccountStatus API to update line of credit account status for a user
    rpc UpdateLOCAccountStatus(UpdateLOCAccountStatusRequest) returns (UpdateLOCAccountStatusResponse){
        option (google.api.http) = {
            put: "/v1/lending/loc/status",
            body: "*",
        };
    }

    // ReactivateDormantCasaAccount updates the status of a casa account from dormant to active in async
    rpc ReactivateDormantCasaAccount (ReactivateDormantCasaAccountRequest) returns (ReactivateDormantCasaAccountResponse) {
        option (google.api.http) = {
            post: "/v1/casa-accounts/dormancy-reactivation",
            body: "*",
        };
    }
}