// Code generated by protoc-gen-go-svc. DO NOT EDIT.
// source: service.proto
package api

import (
	context "context"
	time "time"
)

type AccountPermission string

const (
	AccountPermission_ALLOWED   AccountPermission = "ALLOWED"
	AccountPermission_FORBIDDEN AccountPermission = "FORBIDDEN"
)

type Currency string

const (
	Currency_SGD Currency = "SGD"
	Currency_XXX Currency = "XXX"
	Currency_IDR Currency = "IDR"
	Currency_MYR Currency = "MYR"
)

type AccountStatus string

const (
	AccountStatus_ACTIVE                                   AccountStatus = "ACTIVE"
	AccountStatus_DORMANT                                  AccountStatus = "DORMANT"
	AccountStatus_CLOSED                                   AccountStatus = "CLOSED"
	AccountStatus_CANCELLED                                AccountStatus = "CANCELLED"
	AccountStatus_PENDING                                  AccountStatus = "PENDING"
	AccountStatus_PENDING_ACTIVATION                       AccountStatus = "PENDING_ACTIVATION"
	AccountStatus_PENDING_CLOSURE                          AccountStatus = "PENDING_CLOSURE"
	AccountStatus_LOCAccountStatus_PENDING_ACTIVE          AccountStatus = "PENDING_ACTIVE"
	AccountStatus_LOCAccountStatus_CANCELLED               AccountStatus = "CANCELLED"
	AccountStatus_LOCAccountStatus_PENDING_CLOSURE         AccountStatus = "PENDING_CLOSURE"
	AccountStatus_TermLoanAccountStatus_WRITTEN_OFF        AccountStatus = "WRITTEN_OFF"
	AccountStatus_TermLoanAccountStatus_SETTLED_FULL       AccountStatus = "SETTLED_FULL"
	AccountStatus_TermLoanAccountStatus_SETTLED_NEGOTIATED AccountStatus = "SETTLED_NEGOTIATED"
	AccountStatus_TermLoanAccountStatus_SOLD_OFF           AccountStatus = "SOLD_OFF"
)

type InterestPostingFrequency string

const (
	InterestPostingFrequency_DAYS   InterestPostingFrequency = "DAYS"
	InterestPostingFrequency_WEEKS  InterestPostingFrequency = "WEEKS"
	InterestPostingFrequency_MONTHS InterestPostingFrequency = "MONTHS"
	InterestPostingFrequency_YEARS  InterestPostingFrequency = "YEARS"
)

type ApplicableHoldcode string

const (
	ApplicableHoldcode_ADVERSE_NEWS          ApplicableHoldcode = "ADVERSE_NEWS"
	ApplicableHoldcode_HITS                  ApplicableHoldcode = "AML/SANCTION_HITS"
	ApplicableHoldcode_BANKRUPT              ApplicableHoldcode = "BANKRUPT"
	ApplicableHoldcode_DECEASED              ApplicableHoldcode = "DECEASED"
	ApplicableHoldcode_FRAUD_ACTOR           ApplicableHoldcode = "FRAUD_ACTOR"
	ApplicableHoldcode_FRAUD_VICTIM          ApplicableHoldcode = "FRAUD_VICTIM"
	ApplicableHoldcode_MENTAL_INCAPACITY     ApplicableHoldcode = "MENTAL_INCAPACITY"
	ApplicableHoldcode_MISSING_DEVICE        ApplicableHoldcode = "MISSING_DEVICE"
	ApplicableHoldcode_MULE_ACCOUNT          ApplicableHoldcode = "MULE_ACCOUNT"
	ApplicableHoldcode_ORDER                 ApplicableHoldcode = "POLICE/COURT/SEIZURE/LEA_ORDER"
	ApplicableHoldcode_KILL_SWITCH           ApplicableHoldcode = "KILL_SWITCH"
	ApplicableHoldcode_GARNISHEE_OR_CLAWBACK ApplicableHoldcode = "GARNISHEE/CLAWBACK"
	ApplicableHoldcode_BANK_RESTRICTED       ApplicableHoldcode = "BANK_RESTRICTED"
)

type ImageStatus string

const (
	ImageStatus_PROCESSING ImageStatus = "PROCESSING"
	ImageStatus_SUCCESS    ImageStatus = "SUCCESS"
	ImageStatus_FAILED     ImageStatus = "FAILED"
)

type MaturityInstructionType string

const (
	MaturityInstructionType_NO_AUTO_RENEW MaturityInstructionType = "NO_AUTO_RENEW"
	MaturityInstructionType_AUTO_RENEW    MaturityInstructionType = "AUTO_RENEW"
)

type GoalStatus_Status string

const (
	GoalStatus_Status_EMPTY     GoalStatus_Status = "EMPTY"
	GoalStatus_Status_OFFTRACK  GoalStatus_Status = "OFFTRACK"
	GoalStatus_Status_ONTRACK   GoalStatus_Status = "ONTRACK"
	GoalStatus_Status_COMPLETED GoalStatus_Status = "COMPLETED"
	GoalStatus_Status_CLOSED    GoalStatus_Status = "CLOSED"
)

type LOCAccountStatus_Status string

const (
	LOCAccountStatus_Status_ACTIVE          LOCAccountStatus_Status = "ACTIVE"
	LOCAccountStatus_Status_CLOSED          LOCAccountStatus_Status = "CLOSED"
	LOCAccountStatus_Status_PENDING_ACTIVE  LOCAccountStatus_Status = "PENDING_ACTIVE"
	LOCAccountStatus_Status_CANCELLED       LOCAccountStatus_Status = "CANCELLED"
	LOCAccountStatus_Status_PENDING_CLOSURE LOCAccountStatus_Status = "PENDING_CLOSURE"
)

type LOCAccountSubStatus_SubStatus string

const (
	LOCAccountSubStatus_SubStatus_NORMAL       LOCAccountSubStatus_SubStatus = "NORMAL"
	LOCAccountSubStatus_SubStatus_LIMITED      LOCAccountSubStatus_SubStatus = "LIMITED"
	LOCAccountSubStatus_SubStatus_NON_TERMINAL LOCAccountSubStatus_SubStatus = "NON_TERMINAL"
	LOCAccountSubStatus_SubStatus_TERMINAL     LOCAccountSubStatus_SubStatus = "TERMINAL"
)

type TermLoanAccountStatus_Status string

const (
	TermLoanAccountStatus_Status_ACTIVE             TermLoanAccountStatus_Status = "ACTIVE"
	TermLoanAccountStatus_Status_WRITTEN_OFF        TermLoanAccountStatus_Status = "WRITTEN_OFF"
	TermLoanAccountStatus_Status_CLOSED             TermLoanAccountStatus_Status = "CLOSED"
	TermLoanAccountStatus_Status_SETTLED_FULL       TermLoanAccountStatus_Status = "SETTLED_FULL"
	TermLoanAccountStatus_Status_SETTLED_NEGOTIATED TermLoanAccountStatus_Status = "SETTLED_NEGOTIATED"
	TermLoanAccountStatus_Status_SOLD_OFF           TermLoanAccountStatus_Status = "SOLD_OFF"
)

type TermLoanAccountSubStatus_SubStatus string

const (
	TermLoanAccountSubStatus_SubStatus_NORMAL    TermLoanAccountSubStatus_SubStatus = "NORMAL"
	TermLoanAccountSubStatus_SubStatus_DEFAULT   TermLoanAccountSubStatus_SubStatus = "DEFAULT"
	TermLoanAccountSubStatus_SubStatus_BAD_DEBT  TermLoanAccountSubStatus_SubStatus = "BAD_DEBT"
	TermLoanAccountSubStatus_SubStatus_OPS_LOSS  TermLoanAccountSubStatus_SubStatus = "OPS_LOSS"
	TermLoanAccountSubStatus_SubStatus_WAIVED    TermLoanAccountSubStatus_SubStatus = "WAIVED"
	TermLoanAccountSubStatus_SubStatus_RECOVERED TermLoanAccountSubStatus_SubStatus = "RECOVERED"
)

type RequestStatus_Status string

const (
	RequestStatus_Status_PROCESSING RequestStatus_Status = "PROCESSING"
	RequestStatus_Status_SUCCESS    RequestStatus_Status = "SUCCESS"
	RequestStatus_Status_FAILED     RequestStatus_Status = "FAILED"
)

type LookUpAccountParentRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type LookUpAccountParentResponse struct {
	AccountID       string `json:"accountID,omitempty"`
	ParentAccountID string `json:"parentAccountID,omitempty"`
}

type GetAccountRequest struct {
	AccountID    string `json:"accountID,omitempty"`
	FetchBalance bool   `json:"fetchBalance,omitempty"`
}

type GetAccountResponse struct {
	Account *Account `json:"account,omitempty"`
}

type CheckPermissionsForAccountRequest struct {
	AccountID string `json:"accountID,omitempty"`
	CifNumber string `json:"cifNumber,omitempty"`
}

type CheckPermissionsForAccountResponse struct {
	Status AccountPermission `json:"status,omitempty"`
}

type CreateCASAAccountRequest struct {
	ProductVariantCode   string                `json:"productVariantCode,omitempty"`
	CifNumber            string                `json:"cifNumber,omitempty"`
	CreatedBy            string                `json:"createdBy,omitempty"`
	InstanceParams       *CASAAccountParams    `json:"instanceParams,omitempty"`
	ParentAccountID      string                `json:"parentAccountID,omitempty"`
	Name                 string                `json:"name,omitempty"`
	IdempotencyKey       string                `json:"idempotencyKey,omitempty"`
	PocketTemplateDetail *PocketTemplateDetail `json:"pocketTemplateDetail,omitempty"`
}

type CreateCASAAccountResponse struct {
	Account       *CASAAccount   `json:"account,omitempty"`
	SavingsPocket *SavingsPocket `json:"savingsPocket,omitempty"`
	CreatedBy     string         `json:"createdBy,omitempty"`
}

type BoostPocketParameters struct {
	PrincipalBalance        int64                   `json:"principalBalance,omitempty"`
	FundSourceAccount       string                  `json:"fundSourceAccount,omitempty"`
	MaturityInstructionType MaturityInstructionType `json:"maturityInstructionType,omitempty"`
	CalendarDate            *CalendarDate           `json:"calendarDate,omitempty"`
	PocketTemplateDetail    *PocketTemplateDetail   `json:"pocketTemplateDetail,omitempty"`
}

type CASACreationParameters struct {
	BoostPocket *BoostPocketParameters `json:"boostPocket,omitempty"`
}

type CreateCASAAccountV3Request struct {
	ProductVariantCode string                  `json:"productVariantCode,omitempty"`
	CifNumber          string                  `json:"cifNumber,omitempty"`
	CreatedBy          string                  `json:"createdBy,omitempty"`
	InstanceParams     *CASAAccountParams      `json:"instanceParams,omitempty"`
	ParentAccountID    string                  `json:"parentAccountID,omitempty"`
	Name               string                  `json:"name,omitempty"`
	IdempotencyKey     string                  `json:"idempotencyKey,omitempty"`
	ProductParameters  *CASACreationParameters `json:"productParameters,omitempty"`
}

type CreateCASAAccountV3Response struct {
	RequestIdempotencyKey string        `json:"requestIdempotencyKey,omitempty"`
	Operation             string        `json:"operation,omitempty"`
	RequestStatus         AccountStatus `json:"requestStatus,omitempty"`
	CreatedBy             string        `json:"createdBy,omitempty"`
}

type GetCASAAccountRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetCASAAccountResponse struct {
	Account *CASAAccount `json:"account,omitempty"`
}

type GetAccountBalanceRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetAccountBalanceResponse struct {
	AccountID        string           `json:"accountID,omitempty"`
	AvailableBalance *Money           `json:"availableBalance"`
	Balances         []AccountBalance `json:"balances,omitempty"`
}

type GetCASAAccountSummaryRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type GetCASAAccountSummaryResponse struct {
	TotalBalance         *Money                `json:"totalBalance"`
	DailyPocketSummary   *DailyPocketSummary   `json:"dailyPocketSummary,omitempty"`
	SavingsPocketSummary *SavingsPocketSummary `json:"savingsPocketSummary,omitempty"`
}

type ListCASAAccountsForCustomerResponse struct {
	Accounts []CASAAccountWithBalance `json:"accounts,omitempty"`
}

type ListCASAAccountsForCustomerDetailRequest struct {
	CifNumber         string   `json:"cifNumber,omitempty"`
	FetchBalance      bool     `json:"fetchBalance,omitempty"`
	ProductVariantIDs []string `json:"productVariantIDs,omitempty"`
	Status            []string `json:"status,omitempty"`
	Limit             int64    `json:"limit,omitempty"`
	Page              int64    `json:"page,omitempty"`
}

type ListCASAAccountsForCustomerDetailResponse struct {
	Accounts             []CASAAccountDetail `json:"accounts,omitempty"`
	MaxChildAccountLimit int64               `json:"maxChildAccountLimit,omitempty"`
}

type UpdateCASAAccountStatusRequest struct {
	AccountID        string        `json:"accountID,omitempty"`
	Status           AccountStatus `json:"status,omitempty"`
	Reasons          []string      `json:"reasons,omitempty"`
	ClosingTimestamp time.Time     `json:"closingTimestamp,omitempty"`
	UpdatedBy        string        `json:"updatedBy,omitempty"`
}

type UpdateCASAAccountStatusResponse struct {
	AccountID string        `json:"accountID,omitempty"`
	Status    AccountStatus `json:"status,omitempty"`
	UpdatedBy string        `json:"updatedBy,omitempty"`
}

type UpdateCASAAccountStatusV2Request struct {
	IdempotencyKey string        `json:"idempotencyKey,omitempty"`
	AccountID      string        `json:"accountID,omitempty"`
	Status         AccountStatus `json:"status,omitempty"`
	Reasons        []string      `json:"reasons,omitempty"`
	UpdatedBy      string        `json:"updatedBy,omitempty"`
}

type UpdateCASAAccountStatusV2Response struct {
	RequestIdempotencyKey string        `json:"requestIdempotencyKey,omitempty"`
	AccountID             string        `json:"accountID,omitempty"`
	Operation             string        `json:"operation,omitempty"`
	RequestStatus         AccountStatus `json:"requestStatus,omitempty"`
}

type UpdateCASAAccountParametersRequest struct {
	AccountID                 string             `json:"accountID,omitempty"`
	ProductSpecificParameters *CASAAccountParams `json:"productSpecificParameters,omitempty"`
	UpdatedBy                 string             `json:"updatedBy,omitempty"`
}

type UpdateCASAAccountParametersResponse struct {
	AccountID     string `json:"accountID,omitempty"`
	Status        string `json:"status,omitempty"`
	FailureReason string `json:"failureReason,omitempty"`
}

type GetRequestStatusRequest struct {
	RequestID string `json:"requestID,omitempty"`
	Operation string `json:"operation,omitempty"`
}

type GetRequestStatusResponse struct {
	RequestID     string               `json:"requestID,omitempty"`
	AccountID     string               `json:"accountID,omitempty"`
	Operation     string               `json:"operation,omitempty"`
	RequestStatus RequestStatus_Status `json:"requestStatus,omitempty"`
	ErrorMessage  string               `json:"errorMessage,omitempty"`
	Metadata      map[string]string    `json:"metadata,omitempty"`
}

type CreateSavingsPocketRequest struct {
	IdempotencyKey       string                `json:"idempotencyKey,omitempty"`
	AccountID            string                `json:"accountID,omitempty"`
	PocketTemplateDetail *PocketTemplateDetail `json:"pocketTemplateDetail,omitempty"`
	InstanceParams       *SavingsPocketParams  `json:"instanceParams,omitempty"`
}

type CreateSavingsPocketResponse struct {
	SavingsPocket *SavingsPocket `json:"savingsPocket,omitempty"`
}

type GetSavingsPocketDetailRequest struct {
	PocketID    string `json:"pocketID,omitempty"`
	GoalID      string `json:"goalID,omitempty"`
	SchedulerID string `json:"schedulerID,omitempty"`
}

type GetSavingsPocketDetailResponse struct {
	SavingsPocketDetail *SavingsPocketDetail `json:"savingsPocketDetail,omitempty"`
}

type GetSavingsPocketRequest struct {
	PocketID string `json:"pocketID,omitempty"`
}

type GetSavingsPocketResponse struct {
	SavingsPocket *SavingsPocket `json:"savingsPocket,omitempty"`
}

type ListSavingsPocketDetailRequest struct {
	AccountID string `json:"accountID,omitempty"`
}

type ListSavingsPocketDetailResponse struct {
	TotalBalance        *Money                `json:"totalBalance"`
	SavingsPocketDetail []SavingsPocketDetail `json:"savingsPocketDetail,omitempty"`
}

type UpdateSavingsPocketNameRequest struct {
	IdempotencyKey string `json:"idempotencyKey,omitempty"`
	PocketID       string `json:"pocketID,omitempty"`
	Name           string `json:"name,omitempty"`
}

type UpdateSavingsPocketNameResponse struct {
	PocketID string `json:"pocketID,omitempty"`
	Name     string `json:"name,omitempty"`
}

type UpdateSavingsPocketImageRequest struct {
	IdempotencyKey string `json:"idempotencyKey,omitempty"`
	PocketID       string `json:"pocketID,omitempty"`
	ImageID        string `json:"imageID,omitempty"`
}

type UpdateSavingsPocketImageResponse struct {
	PocketID string `json:"pocketID,omitempty"`
	ImageID  string `json:"imageID,omitempty"`
}

type UpdateSavingsPocketStatusRequest struct {
	IdempotencyKey string        `json:"idempotencyKey,omitempty"`
	PocketID       string        `json:"pocketID,omitempty"`
	Status         AccountStatus `json:"status,omitempty"`
}

type UpdateSavingsPocketStatusResponse struct {
	PocketID string        `json:"pocketID,omitempty"`
	Status   AccountStatus `json:"status,omitempty"`
}

type CloseSavingsPocketRequest struct {
	IdempotencyKey string `json:"idempotencyKey,omitempty"`
	PocketID       string `json:"pocketID,omitempty"`
}

type CloseSavingsPocketResponse struct {
	PocketID string        `json:"pocketID,omitempty"`
	Status   AccountStatus `json:"status,omitempty"`
}

type CreateGoalRequest struct {
	IdempotencyKey string    `json:"idempotencyKey,omitempty"`
	PocketID       string    `json:"pocketID,omitempty"`
	TargetAmount   int64     `json:"targetAmount,omitempty"`
	TargetDate     time.Time `json:"targetDate,omitempty"`
}

type CreateGoalResponse struct {
	Goal *Goal `json:"goal,omitempty"`
}

type GetGoalRequest struct {
	GoalID   string `json:"goalID,omitempty"`
	PocketID string `json:"pocketID,omitempty"`
}

type GetGoalResponse struct {
	Goal *Goal `json:"goal,omitempty"`
}

type UpdateGoalRequest struct {
	IdempotencyKey string    `json:"idempotencyKey,omitempty"`
	GoalID         string    `json:"goalID,omitempty"`
	PocketID       string    `json:"pocketID,omitempty"`
	TargetAmount   int64     `json:"targetAmount,omitempty"`
	TargetDate     time.Time `json:"targetDate,omitempty"`
}

type UpdateGoalResponse struct {
	Goal *Goal `json:"goal,omitempty"`
}

type CloseGoalRequest struct {
	IdempotencyKey string `json:"idempotencyKey,omitempty"`
	GoalID         string `json:"goalID,omitempty"`
	PocketID       string `json:"pocketID,omitempty"`
}

type CloseGoalResponse struct {
	Goal *Goal `json:"goal,omitempty"`
}

type Account struct {
	Id                        string                 `json:"id,omitempty"`
	Name                      string                 `json:"name,omitempty"`
	ParentAccountID           string                 `json:"parentAccountID,omitempty"`
	CifNumber                 string                 `json:"cifNumber,omitempty"`
	AccountType               string                 `json:"accountType,omitempty"`
	PermittedCurrencies       []string               `json:"permittedCurrencies,omitempty"`
	AvailableBalance          *Money                 `json:"availableBalance"`
	Status                    AccountStatus          `json:"status,omitempty"`
	ProductID                 string                 `json:"productID,omitempty"`
	ProductVariantID          string                 `json:"productVariantID,omitempty"`
	ProductSpecificParameters map[string]string      `json:"productSpecificParameters,omitempty"`
	OpeningTimestamp          time.Time              `json:"openingTimestamp,omitempty"`
	PendingActions            []AccountPendingAction `json:"pendingActions"`
}

type CASAAccountParams struct {
	ApplicableHoldcodes []ApplicableHoldcode `json:"applicableHoldcodes"`
}

type CASAAccount struct {
	Id                        string                 `json:"id,omitempty"`
	Name                      string                 `json:"name,omitempty"`
	ParentAccountID           string                 `json:"parentAccountID,omitempty"`
	CifNumber                 string                 `json:"cifNumber,omitempty"`
	PermittedCurrencies       []string               `json:"permittedCurrencies,omitempty"`
	Status                    AccountStatus          `json:"status,omitempty"`
	ProductID                 string                 `json:"productID,omitempty"`
	ProductVariantID          string                 `json:"productVariantID,omitempty"`
	ProductVersionID          string                 `json:"productVersionID,omitempty"`
	ProductSpecificParameters map[string]string      `json:"productSpecificParameters,omitempty"`
	DerivedParams             map[string]string      `json:"derivedParams,omitempty"`
	OpeningTimestamp          time.Time              `json:"openingTimestamp,omitempty"`
	ClosingTimestamp          time.Time              `json:"closingTimestamp,omitempty"`
	PendingActions            []AccountPendingAction `json:"pendingActions"`
}

type AccountBalance struct {
	AccountAddress string `json:"accountAddress,omitempty"`
	Phase          string `json:"phase,omitempty"`
	Amount         *Money `json:"amount,omitempty"`
}

// Represents an amount of money with its currency type.
type Money struct {
	CurrencyCode string `json:"currencyCode"`
	Val          int64  `json:"val"`
}

type CASAAccountWithBalance struct {
	Id               string           `json:"id,omitempty"`
	ProductVariantID string           `json:"productVariantID,omitempty"`
	Status           AccountStatus    `json:"status,omitempty"`
	AvailableBalance *Money           `json:"availableBalance"`
	Balances         []AccountBalance `json:"balances,omitempty"`
}

type CASAAccountDetail struct {
	Id                        string            `json:"id,omitempty"`
	ParentAccountID           string            `json:"parentAccountID,omitempty"`
	ProductVariantID          string            `json:"productVariantID,omitempty"`
	AccountType               string            `json:"accountType,omitempty"`
	AccountName               string            `json:"accountName,omitempty"`
	Status                    AccountStatus     `json:"status,omitempty"`
	AvailableBalance          *Money            `json:"availableBalance,omitempty"`
	ProductSpecificParameters map[string]string `json:"productSpecificParameters"`
	Features                  *Feature          `json:"features,omitempty"`
	OpeningTimestamp          time.Time         `json:"openingTimestamp,omitempty"`
	OnHoldAmount              *Money            `json:"onHoldAmount,omitempty"`
	Balance                   *Balance          `json:"balance,omitempty"`
}

type Balance struct {
	AvailableBalance *Money `json:"availableBalance,omitempty"`
	LedgerBalance    *Money `json:"ledgerBalance,omitempty"`
	EarmarkedBalance *Money `json:"earmarkedBalance,omitempty"`
}

type Feature struct {
	Credit bool `json:"credit,omitempty"`
	Debit  bool `json:"debit,omitempty"`
}

type DailyPocketSummary struct {
	Balance *Money `json:"balance"`
}

type SavingsPocketSummary struct {
	Balance            *Money              `json:"balance,omitempty"`
	Count              int32               `json:"count,omitempty"`
	Images             []string            `json:"images,omitempty"`
	PocketImageDetails []PocketImageDetail `json:"pocketImageDetails,omitempty"`
}

type PocketImageDetail struct {
	PocketID          string      `json:"pocketID,omitempty"`
	PocketImageURL    string      `json:"pocketImageURL,omitempty"`
	PocketImageID     string      `json:"pocketImageID,omitempty"`
	PocketImageStatus ImageStatus `json:"pocketImageStatus,omitempty"`
}

type SavingsPocket struct {
	Id        string        `json:"id,omitempty"`
	Name      string        `json:"name,omitempty"`
	AccountID string        `json:"accountID,omitempty"`
	Status    AccountStatus `json:"status,omitempty"`
}

type PocketTemplateDetail struct {
	TemplateID          string                         `json:"templateID,omitempty"`
	QuestionAnswerPairs []PocketTemplateQuestionAnswer `json:"questionAnswerPairs,omitempty"`
	CustomName          string                         `json:"customName,omitempty"`
	ImageID             string                         `json:"imageID,omitempty"`
}

type PocketTemplateQuestionAnswer struct {
	TemplateQuestionID string `json:"templateQuestionID,omitempty"`
	AnswerText         string `json:"answerText,omitempty"`
}

type SavingsPocketParams struct {
}

type SavingsPocketDetail struct {
	ID                 string           `json:"ID,omitempty"`
	AccountID          string           `json:"accountID,omitempty"`
	Name               string           `json:"name,omitempty"`
	Image              *Image           `json:"image,omitempty"`
	ImageSuggestions   []Image          `json:"imageSuggestions,omitempty"`
	Balance            *Money           `json:"balance,omitempty"`
	Status             AccountStatus    `json:"status,omitempty"`
	GoalDetail         *GoalDetail      `json:"goalDetail,omitempty"`
	SchedulerDetail    *SchedulerDetail `json:"schedulerDetail,omitempty"`
	ProductVariantCode string           `json:"ProductVariantCode,omitempty"`
}

type Image struct {
	ID     string      `json:"ID,omitempty"`
	URL    string      `json:"URL,omitempty"`
	Status ImageStatus `json:"status,omitempty"`
}

type Goal struct {
	ID           string            `json:"ID,omitempty"`
	PocketID     string            `json:"pocketID,omitempty"`
	TargetAmount int64             `json:"targetAmount,omitempty"`
	Currency     Currency          `json:"currency,omitempty"`
	TargetDate   time.Time         `json:"targetDate,omitempty"`
	Status       GoalStatus_Status `json:"status,omitempty"`
}

type GoalDetail struct {
	ID                 string            `json:"ID,omitempty"`
	TargetAmount       *Money            `json:"targetAmount"`
	TargetDate         time.Time         `json:"targetDate,omitempty"`
	Status             GoalStatus_Status `json:"status,omitempty"`
	OfftrackPercentage uint32            `json:"offtrackPercentage"`
	OntrackPercentage  uint32            `json:"ontrackPercentage"`
	OutstandingBalance *Money            `json:"outstandingBalance"`
}

type SchedulerDetail struct {
	SchedulerID    string `json:"schedulerID,omitempty"`
	SchedulerName  string `json:"schedulerName,omitempty"`
	Status         string `json:"status,omitempty"`
	Amount         *Money `json:"amount,omitempty"`
	StartDate      string `json:"startDate,omitempty"`
	EndDate        string `json:"endDate,omitempty"`
	FrequencyType  string `json:"frequencyType,omitempty"`
	FrequencyLabel string `json:"frequencyLabel,omitempty"`
	RepeatCount    int64  `json:"repeatCount,omitempty"`
}

type GetGoalEstimateRequest struct {
	PocketID     string    `json:"pocketID,omitempty"`
	TargetAmount int64     `json:"targetAmount,omitempty"`
	TargetDate   time.Time `json:"targetDate,omitempty"`
}

type GetGoalEstimateResponse struct {
	PocketID                     string `json:"pocketID,omitempty"`
	TargetAmount                 *Money `json:"targetAmount"`
	EstimatedMonthlyContribution *Money `json:"estimatedMonthlyContribution"`
	CustomerTotalContribution    *Money `json:"customerTotalContribution"`
	EstimatedInterestEarned      *Money `json:"estimatedInterestEarned"`
}

type GetAccountDetailsRequest struct {
	AccountType string   `json:"accountType,omitempty"`
	AccountIDs  []string `json:"accountIDs,omitempty"`
}

type GetAccountDetailsResponse struct {
	AccountImageDetails map[string]Image  `json:"accountImageDetails,omitempty"`
	AccountNameDetails  map[string]string `json:"accountNameDetails,omitempty"`
}

type CreateTermLoanAccountRequest struct {
	IdempotencyKey        string    `json:"idempotencyKey,omitempty"`
	LocAccountID          string    `json:"locAccountID,omitempty"`
	TermLoanAccountID     string    `json:"termLoanAccountID,omitempty"`
	AccountName           string    `json:"accountName,omitempty"`
	ProductCode           string    `json:"productCode,omitempty"`
	ProductVariantCode    string    `json:"productVariantCode,omitempty"`
	ProductVariantVersion string    `json:"productVariantVersion,omitempty"`
	CifNumber             string    `json:"cifNumber,omitempty"`
	CurrentStatus         string    `json:"currentStatus,omitempty"`
	CreatedBy             string    `json:"createdBy,omitempty"`
	OpeningTimestamp      time.Time `json:"openingTimestamp,omitempty"`
	CountryCode           string    `json:"countryCode,omitempty"`
	CurrencyCode          string    `json:"currencyCode,omitempty"`
	SubStatus             string    `json:"subStatus,omitempty"`
}

type CreateTermLoanAccountResponse struct {
	TermLoanAccountID string `json:"termLoanAccountID,omitempty"`
	CreatedBy         string `json:"createdBy,omitempty"`
}

type CreateLOCAccountRequest struct {
	IdempotencyKey     string                 `json:"idempotencyKey,omitempty"`
	ProductVariantCode string                 `json:"productVariantCode,omitempty"`
	CifNumber          string                 `json:"cifNumber,omitempty"`
	InstanceParameters *LOCInstanceParameters `json:"instanceParameters,omitempty"`
	WebhookURL         string                 `json:"webhookURL,omitempty"`
	CreatedBy          string                 `json:"createdBy,omitempty"`
	ApplicationID      string                 `json:"applicationID,omitempty"`
}

type LOCInstanceParameters struct {
	OfferedLOC          *CreateLOCMoney `json:"offeredLOC,omitempty"`
	OfferedInterestRate float32         `json:"offeredInterestRate,omitempty"`
	OfferedMaxTenor     int64           `json:"offeredMaxTenor,omitempty"`
}

// Represents an amount of money with its currency type.
type CreateLOCMoney struct {
	CurrencyCode string  `json:"currencyCode"`
	Val          float64 `json:"val"`
}

type CreateLOCAccountResponse struct {
	LOC *LOCAccountDetail `json:"LOC,omitempty"`
}

type LOCAccountDetail struct {
	ReferenceID             string                  `json:"referenceID,omitempty"`
	AccountID               string                  `json:"accountID,omitempty"`
	ProductID               string                  `json:"productID,omitempty"`
	ProductVariantCode      string                  `json:"productVariantCode,omitempty"`
	ProductVersionID        string                  `json:"productVersionID,omitempty"`
	PermittedCurrencies     []string                `json:"permittedCurrencies,omitempty"`
	CifNumber               string                  `json:"cifNumber,omitempty"`
	Status                  LOCAccountStatus_Status `json:"status,omitempty"`
	StatusReason            string                  `json:"statusReason,omitempty"`
	StatusReasonDescription string                  `json:"statusReasonDescription,omitempty"`
	OpeningTimestamp        time.Time               `json:"openingTimestamp,omitempty"`
	ClosingTimestamp        time.Time               `json:"closingTimestamp,omitempty"`
	InstanceParameters      map[string]string       `json:"instanceParameters,omitempty"`
	DerivedParameters       map[string]string       `json:"derivedParameters,omitempty"`
	CreatedBy               string                  `json:"createdBy,omitempty"`
	WebhookURL              string                  `json:"webhookURL,omitempty"`
}

type GetLendingAccountBankDetailsRequest struct {
	ProductVariantCode string `json:"productVariantCode,omitempty"`
}

type LendingAccountBankDetail struct {
	AccountID          string `json:"accountID,omitempty"`
	BankCode           string `json:"bankCode,omitempty"`
	BankName           string `json:"bankName,omitempty"`
	BranchCode         string `json:"branchCode,omitempty"`
	BeneficiaryName    string `json:"beneficiaryName,omitempty"`
	Status             string `json:"status,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
}

type GetLendingAccountBankDetailsResponse struct {
	LendingAccountBankDetails []LendingAccountBankDetail `json:"lendingAccountBankDetails,omitempty"`
}

type AccountDetailForCustomer struct {
	AccountID          string `json:"accountID,omitempty"`
	Status             string `json:"status,omitempty"`
	AccountType        string `json:"accountType,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
	SubStatus          string `json:"subStatus,omitempty"`
}

// UpdateTermLoanAccountStatusRequest : request format to update term loan account status
type UpdateTermLoanAccountStatusRequest struct {
	AccountID        string                             `json:"accountID,omitempty"`
	Status           TermLoanAccountStatus_Status       `json:"status,omitempty"`
	SubStatus        TermLoanAccountSubStatus_SubStatus `json:"subStatus,omitempty"`
	ClosingTimestamp time.Time                          `json:"closingTimestamp,omitempty"`
	UpdatedBy        string                             `json:"updatedBy,omitempty"`
}

// UpdateTermLoanAccountStatusResponse : response format for term loan account status
type UpdateTermLoanAccountStatusResponse struct {
	AccountID string                             `json:"accountID,omitempty"`
	Status    TermLoanAccountStatus_Status       `json:"status,omitempty"`
	SubStatus TermLoanAccountSubStatus_SubStatus `json:"subStatus,omitempty"`
	UpdatedBy string                             `json:"updatedBy,omitempty"`
}

type LOCAccountClosureRequest struct {
	AccountID      string   `json:"accountID,omitempty"`
	IdempotencyKey string   `json:"idempotencyKey,omitempty"`
	Comment        string   `json:"comment,omitempty"`
	Reasons        []string `json:"reasons,omitempty"`
}

type LOCAccountClosureResponse struct {
	Status                  string    `json:"status,omitempty"`
	StatusReason            string    `json:"statusReason,omitempty"`
	StatusReasonDescription string    `json:"statusReasonDescription,omitempty"`
	AccountID               string    `json:"accountID,omitempty"`
	IdempotencyKey          string    `json:"idempotencyKey,omitempty"`
	CreatedAt               time.Time `json:"createdAt,omitempty"`
}

// LOCAccountDeactivationRequest...
type LOCAccountDeactivationRequest struct {
	IdempotencyKey     string            `json:"idempotencyKey,omitempty"`
	ProductVariantCode string            `json:"productVariantCode,omitempty"`
	SafeID             string            `json:"safeID,omitempty"`
	ParentAccountID    string            `json:"parentAccountID,omitempty"`
	Metadata           map[string]string `json:"metadata,omitempty"`
	ReasonCode         string            `json:"reasonCode,omitempty"`
	CreatedBy          string            `json:"createdBy,omitempty"`
}

// LOCAccountDeactivationResponse...
type LOCAccountDeactivationResponse struct {
	IdempotencyKey     string `json:"idempotencyKey,omitempty"`
	ProductVariantCode string `json:"productVariantCode,omitempty"`
	SafeID             string `json:"safeID,omitempty"`
	ParentAccountID    string `json:"parentAccountID,omitempty"`
	Status             string `json:"status,omitempty"`
	StatusReason       string `json:"statusReason,omitempty"`
	StatusDescription  string `json:"statusDescription,omitempty"`
	ReasonCode         string `json:"reasonCode,omitempty"`
	CreatedBy          string `json:"createdBy,omitempty"`
}

// UpdateLOCAccountStatusRequest : request format to update loc account status
type UpdateLOCAccountStatusRequest struct {
	AccountID        string                        `json:"accountID,omitempty"`
	Status           LOCAccountStatus_Status       `json:"status,omitempty"`
	SubStatus        LOCAccountSubStatus_SubStatus `json:"subStatus,omitempty"`
	ClosingTimestamp time.Time                     `json:"closingTimestamp,omitempty"`
	UpdatedBy        string                        `json:"updatedBy,omitempty"`
}

// UpdateLOCAccountStatusResponse : response format for loc account status
type UpdateLOCAccountStatusResponse struct {
	AccountID string                        `json:"accountID,omitempty"`
	Status    LOCAccountStatus_Status       `json:"status,omitempty"`
	SubStatus LOCAccountSubStatus_SubStatus `json:"subStatus,omitempty"`
	UpdatedBy string                        `json:"updatedBy,omitempty"`
}

type GoalStatus struct {
}

type RequestStatus struct {
}

type AccountPendingAction struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"`
}

type LOCAccountStatus struct {
}

type LOCAccountSubStatus struct {
}

type TermLoanAccountStatus struct {
}

type TermLoanAccountSubStatus struct {
}

type MicroSaverInfo struct {
	NominalAmount        string `json:"nominalAmount,omitempty"`
	IsBoosterFlagEnabled bool   `json:"isBoosterFlagEnabled,omitempty"`
}

type ReactivateDormantCasaAccountRequest struct {
	IdempotencyKey string `json:"idempotencyKey,omitempty"`
	AccountID      string `json:"accountID,omitempty"`
	UpdatedBy      string `json:"updatedBy,omitempty"`
}

type ReactivateDormantCasaAccountResponse struct {
	RequestIdempotencyKey string `json:"requestIdempotencyKey,omitempty"`
	AccountID             string `json:"accountID,omitempty"`
	Operation             string `json:"operation,omitempty"`
	Status                string `json:"status,omitempty"`
}

type CalendarDate struct {
	Day   int32 `json:"day,omitempty"`
	Month int32 `json:"month,omitempty"`
	Year  int32 `json:"year,omitempty"`
}

// AccountService
type AccountService interface {
	// LookUpAccountParent look up parent value for the mentioned accountID
	LookUpAccountParent(ctx context.Context, req *LookUpAccountParentRequest) (*LookUpAccountParentResponse, error)
	// GetAccount fetches account details
	GetAccount(ctx context.Context, req *GetAccountRequest) (*GetAccountResponse, error)
	// CheckPermissionsForAccount checks permission between account and customer
	CheckPermissionsForAccount(ctx context.Context, req *CheckPermissionsForAccountRequest) (*CheckPermissionsForAccountResponse, error)
	// CreateCASAAccount creates a new casa account
	CreateCASAAccount(ctx context.Context, req *CreateCASAAccountRequest) (*CreateCASAAccountResponse, error)
	// CreateCASAAccountV3 creates a new casa account
	CreateCASAAccountV3(ctx context.Context, req *CreateCASAAccountV3Request) (*CreateCASAAccountV3Response, error)
	// GetCASAAccount fetches details of a casa account
	GetCASAAccount(ctx context.Context, req *GetCASAAccountRequest) (*GetCASAAccountResponse, error)
	// GetAccountBalance fetches balance of a casa account
	GetAccountBalance(ctx context.Context, req *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error)
	// GetCASAAccountSummary fetches summary of a casa account
	GetCASAAccountSummary(ctx context.Context, req *GetCASAAccountSummaryRequest) (*GetCASAAccountSummaryResponse, error)
	// ListCASAAccountsForCustomer list all the details of all casa accounts
	ListCASAAccountsForCustomer(ctx context.Context) (*ListCASAAccountsForCustomerResponse, error)
	// ListCASAAccountsForCustomerDetail list all the details of all casa accounts for a cif number
	ListCASAAccountsForCustomerDetail(ctx context.Context, req *ListCASAAccountsForCustomerDetailRequest) (*ListCASAAccountsForCustomerDetailResponse, error)
	// UpdateCASAAccountStatus updates the status of a casa account
	UpdateCASAAccountStatus(ctx context.Context, req *UpdateCASAAccountStatusRequest) (*UpdateCASAAccountStatusResponse, error)
	// V2UpdateCASAAccountStatus updates the status of a casa account in async (deprecated, use UpdateCASAAccountStatusV2)
	V2UpdateCASAAccountStatus(ctx context.Context, req *UpdateCASAAccountStatusRequest) (*UpdateCASAAccountStatusResponse, error)
	// UpdateCASAAccountStatusV2 updates the status of a casa account in async
	UpdateCASAAccountStatusV2(ctx context.Context, req *UpdateCASAAccountStatusV2Request) (*UpdateCASAAccountStatusV2Response, error)
	// UpdateCASAAccountParameters updates the instance parameters of a casa account
	UpdateCASAAccountParameters(ctx context.Context, req *UpdateCASAAccountParametersRequest) (*UpdateCASAAccountParametersResponse, error)
	// GetRequestStatus fetches the Status of current request
	GetRequestStatus(ctx context.Context, req *GetRequestStatusRequest) (*GetRequestStatusResponse, error)
	// CreateSavingsPocket creates a new savings pocket
	CreateSavingsPocket(ctx context.Context, req *CreateSavingsPocketRequest) (*CreateSavingsPocketResponse, error)
	// GetSavingsPocketDetail fetches all the details of a savings pocket
	GetSavingsPocketDetail(ctx context.Context, req *GetSavingsPocketDetailRequest) (*GetSavingsPocketDetailResponse, error)
	// V2GetSavingsPocketDetail fetches all the details of a savings pocket
	V2GetSavingsPocketDetail(ctx context.Context, req *GetSavingsPocketDetailRequest) (*GetSavingsPocketDetailResponse, error)
	// GetSavingsPocket fetches savings pocket data
	GetSavingsPocket(ctx context.Context, req *GetSavingsPocketRequest) (*GetSavingsPocketResponse, error)
	// ListSavingsPocketDetail fetches details of all savings pockets
	ListSavingsPocketDetail(ctx context.Context, req *ListSavingsPocketDetailRequest) (*ListSavingsPocketDetailResponse, error)
	// ListSavingsPocketDetail fetches details of all savings pockets
	V2ListSavingsPocketDetail(ctx context.Context, req *ListSavingsPocketDetailRequest) (*ListSavingsPocketDetailResponse, error)
	// UpdateSavingsPocketName updates the name of a pocket
	UpdateSavingsPocketName(ctx context.Context, req *UpdateSavingsPocketNameRequest) (*UpdateSavingsPocketNameResponse, error)
	// UpdateSavingsPocketImage updates the image of a pocket
	UpdateSavingsPocketImage(ctx context.Context, req *UpdateSavingsPocketImageRequest) (*UpdateSavingsPocketImageResponse, error)
	// UpdateSavingsPocketStatus updates the status of a pocket
	UpdateSavingsPocketStatus(ctx context.Context, req *UpdateSavingsPocketStatusRequest) (*UpdateSavingsPocketStatusResponse, error)
	// CloseSavingsPocket closes a savings pocket
	CloseSavingsPocket(ctx context.Context, req *CloseSavingsPocketRequest) (*CloseSavingsPocketResponse, error)
	// CreateGoal creates a new goal
	CreateGoal(ctx context.Context, req *CreateGoalRequest) (*CreateGoalResponse, error)
	// GetGoal fetches all the details of a goal
	GetGoal(ctx context.Context, req *GetGoalRequest) (*GetGoalResponse, error)
	// UpdateGoal updates the targetDate/targetAmount of goal
	UpdateGoal(ctx context.Context, req *UpdateGoalRequest) (*UpdateGoalResponse, error)
	// CloseGoal updates the status of goal to CLOSED
	CloseGoal(ctx context.Context, req *CloseGoalRequest) (*CloseGoalResponse, error)
	// GetGoalEstimate returns contributions and interest earned
	GetGoalEstimate(ctx context.Context, req *GetGoalEstimateRequest) (*GetGoalEstimateResponse, error)
	// GetAccountDetails returns mapping between accounts to the imageURLS and name.
	GetAccountDetails(ctx context.Context, req *GetAccountDetailsRequest) (*GetAccountDetailsResponse, error)
	// GetAccountDetailsByAccountID returns Account Details
	GetAccountDetailsByAccountID(ctx context.Context, req *GetAccountRequest) (*GetAccountResponse, error)
	// CreateTermLoanAccount : Internal API to create term loan account entry in customer_account table, post creation in TM
	CreateTermLoanAccount(ctx context.Context, req *CreateTermLoanAccountRequest) (*CreateTermLoanAccountResponse, error)
	// CreateLOCAccount creates line of credit account for customer
	CreateLOCAccount(ctx context.Context, req *CreateLOCAccountRequest) (*CreateLOCAccountResponse, error)
	// GetLendingAccountDetails returns account details of credit account for customer
	GetLendingAccountBankDetails(ctx context.Context, req *GetLendingAccountBankDetailsRequest) (*GetLendingAccountBankDetailsResponse, error)
	// UpdateTermLoanAccountStatus : Internal API to update term loan account entry in customer_account table, currently used for closure
	UpdateTermLoanAccountStatus(ctx context.Context, req *UpdateTermLoanAccountStatusRequest) (*UpdateTermLoanAccountStatusResponse, error)
	// CloseLOCAccount API closes a line of credit account for a user
	CloseLOCAccount(ctx context.Context, req *LOCAccountClosureRequest) (*LOCAccountClosureResponse, error)
	// FlexiLOCDeactivation : API to deactivate line of credit account
	FlexiLOCDeactivation(ctx context.Context, req *LOCAccountDeactivationRequest) (*LOCAccountDeactivationResponse, error)
	// UpdateLOCAccountStatus API to update line of credit account status for a user
	UpdateLOCAccountStatus(ctx context.Context, req *UpdateLOCAccountStatusRequest) (*UpdateLOCAccountStatusResponse, error)
	// ReactivateDormantCasaAccount updates the status of a casa account from dormant to active in async
	ReactivateDormantCasaAccount(ctx context.Context, req *ReactivateDormantCasaAccountRequest) (*ReactivateDormantCasaAccountResponse, error)
}

type ProductVariantID string

const (
	// MYMainAccountProductVariantID malaysia main acc product variant id
	MYMainAccountProductVariantID ProductVariantID = "DEPOSITS_ACCOUNT"
	// MYSavingsPocketProductVariantID malaysia savings pocket product variant id
	MYSavingsPocketProductVariantID ProductVariantID = "SAVINGS_POCKET"
	// MYDefaultFlexiLoanAccountProductVarientID malaysia DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT product variant id
	MYDefaultFlexiLoanAccountProductVarientID ProductVariantID = "DEFAULT_FLEXI_LOAN_LINE_OF_CREDIT"
	// MYDefaultFlexiLoanTermLoanID malaysia DEFAULT_FLEXI_LOAN_TERM_LOAN product variant id
	MYDefaultFlexiLoanTermLoanID ProductVariantID = "DEFAULT_FLEXI_LOAN_TERM_LOAN"
	// DefaultBizFlexiCreditLineOfCreditID DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT product variant id
	DefaultBizFlexiCreditLineOfCreditID ProductVariantID = "DEFAULT_BIZ_FLEXI_CREDIT_LINE_OF_CREDIT"
	// BizOAProductVariantID SG and MY operating acc product variant id
	BizOAProductVariantID ProductVariantID = "BIZ_DEPOSIT_ACCOUNT"
)
