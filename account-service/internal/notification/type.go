// Package notification ...
package notification

// Type ...
type Type string

// NotificationType ...
const (
	HoldcodeAccountBlockNotification                    Type = "holdcodeAccountBlockNotification"
	HoldcodeAccountLockNotification                     Type = "holdcodeAccountLockNotification"
	HoldcodeAccountUnblockNotification                  Type = "holdcodeAccountUnblockNotification"
	HoldcodeAccountUnlockNotification                   Type = "holdcodeAccountUnlockNotification"
	BizOAHoldcodeAccountBlockNotification               Type = "bizOAHoldcodeAccountBlockNotification"
	BizOAHoldcodeAccountUnblockNotification             Type = "bizOAHoldcodeAccountUnblockNotification"
	SavingsPocketClosureNotification                    Type = "savingsPocketClosureNotification"
	SavingsPocketCreationNotification                   Type = "savingsPocketCreationNotification"
	BoostPocketCreationNotification                     Type = "boostPocketCreationNotification"
	BoostPocketCreationFailedGenericNotification        Type = "boostPocketCreationFailedGenericNotification"
	BoostPocketCreationFailedAvailabilityNotification   Type = "boostPocketCreationFailedHighDemandNotification"
	BoostPocketCreationFailedBalanceNotification        Type = "boostPocketCreationFailedInsufficientBalanceNotification"
	PendingActivationAutoCloseReminderNotification      Type = "pendingActivationAutoCloseReminderNotification"
	PendingActionAutoCloseReminderNotification          Type = "pendingActionAutoCloseReminderNotification"
	HoldCodeBankRestrictedBlockAccountNotification      Type = "holdCodeBankRestrictedBlockAccountNotification"
	HoldCodeBankRestrictedUnblockAccountNotification    Type = "holdCodeBankRestrictedUnblockAccountNotification"
	HoldCodeMissingDeviceBlockAccountNotification       Type = "holdCodeMissingDeviceBlockAccountNotification"
	HoldCodeMissingDeviceUnblockAccountNotification     Type = "holdCodeMissingDeviceUnblockAccountNotification"
	LOCAccountPendingClosureNotification                Type = "locAccountPendingClosureNotification"
	LOCAccountFailedClosureNotification                 Type = "locAccountFailedClosureNotification"
	LOCAccountClosedNotification                        Type = "locAccountClosedNotification"
	ClosedPendingActivationNotification                 Type = "closedAccountPendingActivationNotification"
	ReactivatedDormantAccountNotification               Type = "reactivatedDormantAccountNotification"
	DormantAccountNotification                          Type = "dormantAccountNotification"
	AutoClosedBizOAPendingActivationNotification        Type = "autoClosedBizOAPendingActivationNotification"
	BizOAPendingActivationAutoCloseReminderNotification Type = "bizOAPendingActivationAutoCloseReminderNotification"
	BizHoldCodeBankRestrictedBlockAccountNotification   Type = "bizHoldCodeBankRestrictedBlockAccountNotification"
	BizHoldCodeBankRestrictedUnblockAccountNotification Type = "bizHoldCodeBankRestrictedUnblockAccountNotification"
)
