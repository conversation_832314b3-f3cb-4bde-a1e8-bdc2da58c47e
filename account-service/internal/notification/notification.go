// Package notification ...
package notification

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	klientError "gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	pigeon "gitlab.myteksi.net/dbmy/pigeon/api"
)

// Notifier ...
type Notifier interface {
	SendNotification(context.Context, *NotifDTO) error
}

// NotifierImpl ...
type NotifierImpl struct {
	PigeonClient       pigeon.Pigeon              `inject:"client.pigeon"`
	NotificationConfig *config.NotificationConfig `inject:"notificationConfig"`
}

// TemplateParams ...
type TemplateParams struct {
	PushParams  map[string]string
	EmailParams map[string]string
}

// NotifDTO ...
type NotifDTO struct {
	// RecipientID can either be safeID for Retail or BIF for Biz
	RecipientID string
	// Grab userID or safeID
	CustomerID       string
	NotificationType Type
	Params           TemplateParams
}

const recipientType = "DIGIBANK"

// SendNotification ...
func (n *NotifierImpl) SendNotification(ctx context.Context, dto *NotifDTO) error {
	var pushErr, emailErr error

	pushTemplate, emailTemplate := n.mapTypeToTemplates(dto.NotificationType)

	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, dto.CustomerID)
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, recipientType)
	// we will use recipientID if available as it has higher specificity, otherwise we will use customerID
	recipientID := lo.Ternary(lo.IsNotEmpty(dto.RecipientID), dto.RecipientID, dto.CustomerID)

	if pushTemplate != "" {
		_, pushErr = n.PigeonClient.V2Push(ctx, &pigeon.PushRequest{
			RecipientID: recipientID,
			// to avoid compatible issue for other types of notification when recipientID is not available
			RecipientType: recipientType,
			Template: &pigeon.Template{
				ID:     pushTemplate,
				Params: dto.Params.PushParams,
			},
		})
	}
	if emailTemplate != "" {
		_, emailErr = n.PigeonClient.V2Email(ctx, &pigeon.EmailRequest{
			RecipientID: recipientID,
			Template: &pigeon.Template{
				ID:     emailTemplate,
				Params: dto.Params.EmailParams,
			},
		})
	}
	err := parseNotificationErr(ctx, pushErr, emailErr)
	return err
}

func parseNotificationErr(ctx context.Context, pushErr, emailErr error) error {
	errStrings := make([]string, 0, 2)
	if pushErr != nil {
		clientErr, ok := pushErr.(*klientError.Error)
		if ok && clientErr.HTTPCode == 404 {
			slog.FromContext(ctx).Warn("pigeon", "customer push notification token not found", slog.Error(pushErr))
		}
		errStrings = append(errStrings, fmt.Sprintf("pushErr: %s", pushErr.Error()))
	}
	if emailErr != nil {
		clientErr, ok := emailErr.(*klientError.Error)
		if ok && clientErr.HTTPCode == 404 {
			slog.FromContext(ctx).Warn("pigeon", "customer email not found", slog.Error(emailErr))
		}
		errStrings = append(errStrings, fmt.Sprintf("emailErr: %s", emailErr.Error()))
	}
	if len(errStrings) > 0 {
		return errors.New(strings.Join(errStrings, ", "))
	}

	return nil
}

// nolint:funlen
func (n *NotifierImpl) mapTypeToTemplates(notifType Type) (pushTemplate, emailTemplate string) {
	conf := n.NotificationConfig
	switch notifType {
	case HoldcodeAccountBlockNotification:
		pushTemplate, emailTemplate = conf.HoldcodeAccountBlockNotification.PushTemplate, conf.HoldcodeAccountBlockNotification.EmailTemplate
	case HoldcodeAccountLockNotification:
		pushTemplate, emailTemplate = conf.HoldcodeAccountLockNotification.PushTemplate,
			conf.HoldcodeAccountLockNotification.EmailTemplate
	case HoldcodeAccountUnblockNotification:
		pushTemplate, emailTemplate = conf.HoldcodeAccountUnblockNotification.PushTemplate,
			conf.HoldcodeAccountUnblockNotification.EmailTemplate
	case HoldcodeAccountUnlockNotification:
		pushTemplate, emailTemplate = conf.HoldcodeAccountUnlockNotification.PushTemplate,
			conf.HoldcodeAccountUnlockNotification.EmailTemplate
	case BizOAHoldcodeAccountBlockNotification:
		pushTemplate, emailTemplate = conf.BizOAHoldcodeAccountBlockNotification.PushTemplate,
			conf.BizOAHoldcodeAccountBlockNotification.EmailTemplate
	case BizOAHoldcodeAccountUnblockNotification:
		pushTemplate, emailTemplate = conf.BizOAHoldcodeAccountUnblockNotification.PushTemplate,
			conf.BizOAHoldcodeAccountUnblockNotification.EmailTemplate
	case SavingsPocketClosureNotification:
		pushTemplate, emailTemplate = conf.SavingsPocketClosureNotification.PushTemplate,
			conf.SavingsPocketClosureNotification.EmailTemplate
	case SavingsPocketCreationNotification:
		pushTemplate, emailTemplate = conf.SavingsPocketCreationNotification.PushTemplate,
			conf.SavingsPocketCreationNotification.EmailTemplate
	case BoostPocketCreationNotification:
		pushTemplate, emailTemplate = conf.BoostPocketCreationNotification.PushTemplate,
			conf.BoostPocketCreationNotification.EmailTemplate
	case BoostPocketCreationFailedGenericNotification:
		pushTemplate, emailTemplate = conf.BoostPocketCreationFailedGenericNotification.PushTemplate,
			conf.BoostPocketCreationFailedGenericNotification.EmailTemplate
	case BoostPocketCreationFailedAvailabilityNotification:
		pushTemplate, emailTemplate = conf.BoostPocketCreationFailedAvailabilityNotification.PushTemplate,
			conf.BoostPocketCreationFailedAvailabilityNotification.EmailTemplate
	case BoostPocketCreationFailedBalanceNotification:
		pushTemplate, emailTemplate = conf.BoostPocketCreationFailedBalanceNotification.PushTemplate,
			conf.BoostPocketCreationFailedBalanceNotification.EmailTemplate
	case PendingActivationAutoCloseReminderNotification:
		pushTemplate, emailTemplate = conf.PendingActivationAutoCloseReminderNotification.PushTemplate,
			conf.PendingActivationAutoCloseReminderNotification.EmailTemplate
	case PendingActionAutoCloseReminderNotification:
		pushTemplate, emailTemplate = conf.PendingFirstFundAutoCloseReminderNotification.PushTemplate,
			conf.PendingFirstFundAutoCloseReminderNotification.EmailTemplate
	case HoldCodeBankRestrictedBlockAccountNotification:
		pushTemplate, emailTemplate = conf.HoldCodeBankRestrictedBlockAccountNotification.PushTemplate,
			conf.HoldCodeBankRestrictedBlockAccountNotification.EmailTemplate
	case HoldCodeBankRestrictedUnblockAccountNotification:
		pushTemplate, emailTemplate = conf.HoldCodeBankRestrictedUnblockAccountNotification.PushTemplate,
			conf.HoldCodeBankRestrictedUnblockAccountNotification.EmailTemplate
	case HoldCodeMissingDeviceBlockAccountNotification:
		pushTemplate, emailTemplate = conf.HoldCodeMissingDeviceBlockAccountNotification.PushTemplate,
			conf.HoldCodeMissingDeviceBlockAccountNotification.EmailTemplate
	case HoldCodeMissingDeviceUnblockAccountNotification:
		pushTemplate, emailTemplate = conf.HoldCodeMissingDeviceUnblockAccountNotification.PushTemplate,
			conf.HoldCodeMissingDeviceUnblockAccountNotification.EmailTemplate
	case LOCAccountPendingClosureNotification:
		pushTemplate, emailTemplate = conf.LOCAccountPendingClosureNotification.PushTemplate,
			conf.LOCAccountPendingClosureNotification.EmailTemplate
	case LOCAccountFailedClosureNotification:
		pushTemplate, emailTemplate = conf.LOCAccountFailedClosureNotification.PushTemplate,
			conf.LOCAccountFailedClosureNotification.EmailTemplate
	case LOCAccountClosedNotification:
		pushTemplate, emailTemplate = conf.LOCAccountClosedNotification.PushTemplate,
			conf.LOCAccountClosedNotification.EmailTemplate
	case ClosedPendingActivationNotification:
		pushTemplate, emailTemplate = conf.ClosedPendingActivationNotification.PushTemplate,
			conf.ClosedPendingActivationNotification.EmailTemplate
	case ReactivatedDormantAccountNotification:
		pushTemplate, emailTemplate = conf.ReactivatedDormantAccountNotification.PushTemplate,
			conf.ReactivatedDormantAccountNotification.EmailTemplate
	case DormantAccountNotification:
		pushTemplate, emailTemplate = conf.DormantAccountNotification.PushTemplate,
			conf.DormantAccountNotification.EmailTemplate
	case AutoClosedBizOAPendingActivationNotification:
		pushTemplate, emailTemplate = conf.AutoClosedBizOAPendingActivationNotification.PushTemplate,
			conf.AutoClosedBizOAPendingActivationNotification.EmailTemplate
	case BizOAPendingActivationAutoCloseReminderNotification:
		pushTemplate, emailTemplate = conf.BizOAPendingActivationAutoCloseReminderNotification.PushTemplate,
			conf.BizOAPendingActivationAutoCloseReminderNotification.EmailTemplate
	case BizHoldCodeBankRestrictedBlockAccountNotification:
		pushTemplate, emailTemplate = conf.BizHoldCodeBankRestrictedBlockAccountNotification.PushTemplate,
			conf.BizHoldCodeBankRestrictedBlockAccountNotification.EmailTemplate
	case BizHoldCodeBankRestrictedUnblockAccountNotification:
		pushTemplate, emailTemplate = conf.BizHoldCodeBankRestrictedUnblockAccountNotification.PushTemplate,
			conf.BizHoldCodeBankRestrictedUnblockAccountNotification.EmailTemplate
	}
	return pushTemplate, emailTemplate
}
