package handlers

import (
	"context"
	"fmt"
	"net/http"
	"reflect"
	"strconv"

	"gitlab.myteksi.net/bersama/core-banking/account-service/internal/featureflag"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils/validations"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
	"gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"
)

// GetAccountDetailsByAccountID  returns Account details for the accountID
func (a *AccountService) GetAccountDetailsByAccountID(ctx context.Context,
	req *api.GetAccountRequest) (*api.GetAccountResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("GetAccountDetails By AccountID"))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, &a.AppConfig.FeatureFlags)
	slog.FromContext(ctx).Info(constants.GetAccountLogTag, fmt.Sprintf("Received Request: %+v", req), utils.GetTraceID(ctx))
	if err := a.validateGetAccountByAccountIDRequest(ctx, req); err != nil {
		return nil, err
	}

	requestParams := depositsCore.GetCASAAccountRequest{
		AccountID: req.AccountID,
	}

	dbCustomerAccount, err := a.Store.GetCustomerAccount(ctx, requestParams.AccountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountLogTag, fmt.Sprintf("unable to fetch account-details from database: %s", err.Error()), utils.GetTraceID(ctx))
		if reflect.DeepEqual(err, data.ErrNoData) {
			return nil, apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrAccountNotFound.Code), 10), apiErr.ErrAccountNotFound.Message)
		}
		return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message)
	}

	casaAccount, err := a.DepositsCoreClient.GetCASAAccount(ctx, &requestParams)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetAccountLogTag, fmt.Sprintf("unable to fetch account-details via deposits-core:: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}

	account := utils.MapAccount(casaAccount.Account, dbCustomerAccount)

	if req.FetchBalance {
		accBalances, err := a.DepositsCoreClient.GetAccountBalance(ctx, &depositsCore.GetAccountBalanceRequest{
			AccountID: req.AccountID,
		})
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAccountLogTag, fmt.Sprintf("unable to fetch account-balance via deposits-core: %s", err.Error()), utils.GetTraceID(ctx))
			return nil, err
		}
		account.AvailableBalance, err = utils.GetAvailableBalance(accBalances.Balances)
		if err != nil {
			slog.FromContext(ctx).Warn(constants.GetAccountLogTag, fmt.Sprintf("empty balances fetched from deposits-core: %s", err.Error()), utils.GetTraceID(ctx))
			return nil, err
		}
	}

	if storage.IsParentAccount(dbCustomerAccount) {
		pendingActions, err := getPendingActions(ctx, a.Store, dbCustomerAccount.AccountID)

		if err != nil {
			return nil, err
		}
		account.PendingActions = pendingActions
	}

	response := &api.GetAccountResponse{
		Account: &account,
	}

	return response, nil
}

func (a *AccountService) validateGetAccountByAccountIDRequest(ctx context.Context, req *api.GetAccountRequest) error {
	if err := validations.ValidateAccountID(ctx, req.AccountID); err != nil {
		return err
	}
	client := utils.GetClientIdentityFromHeader(ctx)
	if client == servicename.SentryT6.ToString() {
		var err error
		if a.AppConfig.FeatureFlags.EnableBIZAuthorisation {
			err = utils.CheckIfCustomerAuthorized(ctx, req.AccountID, a.Store, a.ExternalCustomerClient)
		} else {
			err = utils.CheckIfAuthorized(ctx, req.AccountID, a.Store, a.ExternalCustomerClient)
		}
		if err != nil {
			return err
		}
	}
	return nil
}

func getPendingActions(ctx context.Context, store storage.DatabaseStore, accountID string) ([]api.AccountPendingAction, error) {
	pendingActions, err := store.GetAccountPendingActionsByAccountID(ctx, accountID)

	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetAccountPendingActionByName, fmt.Sprintf("unable to fetch account-pending-actions: %s", err.Error()), utils.GetTraceID(ctx))
		return nil, err
	}

	return utils.MapPendingActionsToAPI(pendingActions), nil
}
