package handlers

import (
	"context"
	"errors"
	"net/http"
	"net/url"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/test/responses"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	context2 "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	testutils "gitlab.myteksi.net/bersama/core-banking/account-service/test/utils"
	goalCore "gitlab.myteksi.net/dakota/core-banking/goal-core/api"
	mocks "gitlab.myteksi.net/dakota/core-banking/goal-core/api/mock"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
	depositsCoreMock "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api/mock"
	depositsExp "gitlab.myteksi.net/dbmy/core-banking/deposits-exp/api"
	depositsExpMock "gitlab.myteksi.net/dbmy/core-banking/deposits-exp/api/mock"
	hermes "gitlab.myteksi.net/dbmy/hermes/api"
	hermesMock "gitlab.myteksi.net/dbmy/hermes/api/mock"
)

func Test_V2ListSavingsPocketDetail(t *testing.T) {
	now := time.Now().UTC()
	locale := testutils.GetLocale()

	constTargetDate := utils.DateNowLocal().AddDate(1, 0, 0)
	goalDetail := responses.GoalDetail(constTargetDate, api.GoalStatus_Status_OFFTRACK, 50, 50, 20)
	goalDetail.TargetAmount = &api.Money{
		CurrencyCode: locale.Currency,
		Val:          40,
	}

	newDepCoreAccountDTO := func(f func(account depositsCore.CASAAccountWithBalance) depositsCore.CASAAccountWithBalance) depositsCore.CASAAccountWithBalance {
		account := depositsCore.CASAAccountWithBalance{}

		if f == nil {
			return account
		}
		return f(account)
	}
	depCoreAccount1 := newDepCoreAccountDTO(func(account depositsCore.CASAAccountWithBalance) depositsCore.CASAAccountWithBalance {
		(&account).Id = "test-pocket-id-001"
		(&account).Balances = []depositsCore.AccountBalance{
			{
				AccountAddress: constants.AccountAddressDefault,
				Amount: &depositsCore.Money{
					CurrencyCode: locale.Currency,
					Val:          20,
				},
				Phase: constants.BalancePhaseCommitted,
			},
		}
		(&account).AvailableBalance = &depositsCore.Money{
			CurrencyCode: locale.Currency,
			Val:          20,
		}
		return account
	})

	depCoreAccount2 := newDepCoreAccountDTO(func(account depositsCore.CASAAccountWithBalance) depositsCore.CASAAccountWithBalance {
		(&account).Id = "test-pocket-id-002"
		(&account).Balances = []depositsCore.AccountBalance{
			{
				AccountAddress: constants.AccountAddressDefault,
				Amount: &depositsCore.Money{
					CurrencyCode: locale.Currency,
					Val:          0,
				},
				Phase: constants.BalancePhaseCommitted,
			},
		}
		(&account).AvailableBalance = &depositsCore.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		}
		return account
	})

	depCoreBoostPocketAccount1 := newDepCoreAccountDTO(func(account depositsCore.CASAAccountWithBalance) depositsCore.CASAAccountWithBalance {
		(&account).Id = "test-pocket-id-003"
		(&account).Balances = []depositsCore.AccountBalance{
			{
				AccountAddress: constants.AccountAddressDefault,
				Amount: &depositsCore.Money{
					CurrencyCode: locale.Currency,
					Val:          20,
				},
				Phase: constants.BalancePhaseCommitted,
			},
		}
		(&account).AvailableBalance = &depositsCore.Money{
			CurrencyCode: locale.Currency,
			Val:          20,
		}
		return account
	})
	depCoreBoostPocketAccount2 := newDepCoreAccountDTO(func(account depositsCore.CASAAccountWithBalance) depositsCore.CASAAccountWithBalance {
		(&account).Id = "test-pocket-id-004"
		(&account).Balances = []depositsCore.AccountBalance{
			{
				AccountAddress: constants.AccountAddressDefault,
				Amount: &depositsCore.Money{
					CurrencyCode: locale.Currency,
					Val:          0,
				},
				Phase: constants.BalancePhaseCommitted,
			},
		}
		(&account).AvailableBalance = &depositsCore.Money{
			CurrencyCode: locale.Currency,
			Val:          0,
		}
		return account
	})

	scenarios := []struct {
		testDesc                          string
		userID                            string
		serviceID                         string
		callerService                     string
		request                           *api.ListSavingsPocketDetailRequest
		response                          *api.ListSavingsPocketDetailResponse
		goalCoreGetGoalResponse           *goalCore.GetGoalsResponse
		depositsExpListSchedulersResponse *depositsExp.ListSchedulersResponse
		returnCif                         *customerMaster.LookupCIFNumberResponse
		customerAccount                   *storage.CustomerAccount
		storeCustomerSubAccount           []*storage.CustomerAccount
		storePockets                      []*storage.Pocket
		depositsCoreAccounts              []depositsCore.CASAAccountWithBalance
		listImageDetailFromHermes         *hermes.GetDocumentsResponse
		customerAccountError              error
		depositsCoreBalanceError          error
		storeCustomerSubAccountErr        error
		storePocketsError                 error
		cifErr                            error
		goalCoreGetGoalError              error
		depositsExpListSchedulersError    error
		listImageDetailFromHermesError    error
		isErrorExpected                   bool
		returnError                       error
		expectedError                     error
	}{
		{
			testDesc:  "happy get path - no error",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			response:                          responses.ListSavingsPocketDetailWithSchedulerResponse(constTargetDate, []string{"test-pocket-id-001", "test-pocket-id-002"}, nil, goalDetail),
			goalCoreGetGoalResponse:           responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate),
			depositsExpListSchedulersResponse: responses.DepositsExpListSchedulersResponse(),
			customerAccount:                   responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount:           responses.GetSubAccountsNonEmptyResponse(now),
			storePockets:                      responses.GetPocketResponse(now),
			depositsCoreAccounts:              []depositsCore.CASAAccountWithBalance{depCoreAccount1, depCoreAccount2},
			listImageDetailFromHermes:         responses.ListImageDetailsResponseFromHermes(),
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			isErrorExpected: false,
			returnError:     nil,
		},
		{
			testDesc:  "happy get path with boost pocket - no error",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			response: responses.ListSavingsPocketDetailWithSchedulerResponse(constTargetDate, []string{"test-pocket-id-001", "test-pocket-id-002"}, []string{"test-pocket-id-003",
				"test-pocket-id-004"}, goalDetail),
			goalCoreGetGoalResponse:           responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate),
			depositsExpListSchedulersResponse: responses.DepositsExpListSchedulersResponse(),
			customerAccount:                   responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount:           responses.GetSubAccountsNonEmptyResponse(now, responses.WithBoostPocket(true)),
			storePockets:                      responses.GetPocketResponse(now, responses.WithBoostPocket(true)),
			depositsCoreAccounts:              []depositsCore.CASAAccountWithBalance{depCoreAccount1, depCoreAccount2, depCoreBoostPocketAccount1, depCoreBoostPocketAccount2},
			listImageDetailFromHermes:         responses.ListImageDetailsResponseFromHermes(responses.WithBoostPocket(true)),
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			isErrorExpected: false,
			returnError:     nil,
		},
		{
			testDesc:  "happy get path - empty list",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			response:                  responses.ListSavingsPocketDetailEmptyResponse(),
			goalCoreGetGoalResponse:   nil,
			customerAccount:           responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount:   nil,
			storePockets:              nil,
			depositsCoreAccounts:      []depositsCore.CASAAccountWithBalance{},
			listImageDetailFromHermes: nil,
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			isErrorExpected: false,
			returnError:     nil,
		},
		{
			testDesc:        "Error path - Missing AccountID",
			userID:          "test-user-id",
			serviceID:       "test-service-id",
			request:         &api.ListSavingsPocketDetailRequest{},
			response:        nil,
			isErrorExpected: true,
			returnError:     apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingAccountID.Code), 10), apiErr.ErrMissingAccountID.Message),
		},
		{
			testDesc:  "Error path - Forbidden Access - Main account does not belong to the user",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif-1",
			},
			customerAccount:            responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccountErr: data.ErrNoData,
			response:                   nil,
			isErrorExpected:            true,
			callerService:              servicename.SentryT6.ToString(),
			returnError:                apiErr.BuildErrorResponse(http.StatusForbidden, strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10), apiErr.ErrAccountPermissionForbidden.Message),
		},
		{
			testDesc:  "Error path - no active pockets found",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			customerAccount:         responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount: responses.GetSubAccountsNonEmptyResponse(now),
			storePocketsError:       data.ErrNoData,
			response:                nil,
			isErrorExpected:         true,
			returnError:             apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrRecordNotFound.Code), 10), apiErr.ErrRecordNotFound.Message),
		},
		{
			testDesc:  "Error path - database error - Unable to load customerAccount",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			customerAccountError: errors.New("some DB failure"),
			response:             nil,
			isErrorExpected:      true,
			returnError:          apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message),
			callerService:        servicename.SentryT6.ToString(),
		},
		{
			testDesc:  "Error path - database error - Unable to load pockets",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			customerAccount:         responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount: responses.GetSubAccountsNonEmptyResponse(now),
			storePocketsError:       data.ErrInvalidObject,
			response:                nil,
			isErrorExpected:         true,
			returnError:             apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrLoadDataFromDB.Code), 10), apiErr.ErrLoadDataFromDB.Message),
		},
		{
			testDesc:  "Error path - unable to get balance via deposits-core",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			customerAccount:          responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount:  responses.GetSubAccountsNonEmptyResponse(now),
			storePockets:             responses.GetPocketResponse(now),
			depositsCoreBalanceError: errors.New("unable to retrieve balance via-deposits-core"),
			response:                 nil,
			isErrorExpected:          true,
			returnError:              errors.New("unable to retrieve balance via-deposits-core"),
		},
		{
			testDesc:  "Error path - unable to get goals via goal-core",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			customerAccount:         responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount: responses.GetSubAccountsNonEmptyResponse(now),
			storePockets:            responses.GetPocketResponse(now),
			depositsCoreAccounts:    []depositsCore.CASAAccountWithBalance{depCoreAccount1, depCoreAccount2},
			goalCoreGetGoalError: &url.Error{
				Op:  "Get",
				URL: "http://goal-core/api/goals",
				Err: errors.New("goal-core error"),
			},
			response:        nil,
			isErrorExpected: true,
			returnError:     servus.ServiceError{HTTPCode: 500, Code: "1227", Message: "Internal error", Errors: []servus.ErrorDetail(nil)},
		},
		{
			testDesc:  "Error path - unable to get schedulers via deposits-exp",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			response:                nil,
			goalCoreGetGoalResponse: responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate),
			depositsExpListSchedulersError: &errorhandling.Error{
				HTTPCode: 500,
				Code:     strconv.FormatInt(5999, 10),
				Message:  "deposits-exp error",
				Errors:   nil,
			},
			customerAccount:           responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount:   responses.GetSubAccountsNonEmptyResponse(now),
			storePockets:              responses.GetPocketResponse(now),
			depositsCoreAccounts:      []depositsCore.CASAAccountWithBalance{depCoreAccount1, depCoreAccount2},
			listImageDetailFromHermes: responses.ListImageDetailsResponseFromHermes(),
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			isErrorExpected: true,
			returnError: &errorhandling.Error{
				HTTPCode: 500,
				Code:     strconv.FormatInt(5999, 10),
				Message:  "deposits-exp error",
				Errors:   nil,
			},
		},
		{
			testDesc:  "error path - unable to get image details from hermes",
			userID:    "test-user-id",
			serviceID: "test-service-id",
			request: &api.ListSavingsPocketDetailRequest{
				AccountID: "test-account-id",
			},
			response:                responses.ListSavingsPocketDetailResponse(constTargetDate, "test-pocket-id-001", "test-pocket-id-002", goalDetail),
			goalCoreGetGoalResponse: responses.GoalCoreGetGoalsWithCreationDateResponse(constTargetDate),
			customerAccount:         responses.GetCustomerAccountResponse(now),
			storeCustomerSubAccount: responses.GetSubAccountsNonEmptyResponse(now),
			storePockets:            responses.GetPocketResponse(now),
			depositsCoreAccounts:    []depositsCore.CASAAccountWithBalance{depCoreAccount1, depCoreAccount2},
			returnCif: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			isErrorExpected:                true,
			listImageDetailFromHermesError: errors.New("error from hermes"),
			returnError:                    errors.New("error from hermes"),
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockGoalCore := &mocks.GoalCore{}
			mockStore := &storage.MockDatabaseStore{}
			mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
			mockDepositsCore := &depositsCoreMock.DepositsCore{}
			mockDepositsExp := &depositsExpMock.DepositsExp{}
			mockHermes := &hermesMock.Hermes{}

			mockStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(s.customerAccount, s.customerAccountError)
			mockGoalCore.On("GetGoals", mock.Anything, mock.Anything).Return(s.goalCoreGetGoalResponse, s.goalCoreGetGoalError)
			mockStore.On("GetPockets", mock.Anything, mock.Anything, mock.Anything).Return(s.storePockets, s.storePocketsError)
			mockStore.On("GetCustomerSubAccounts", mock.Anything, mock.Anything, mock.Anything).Return(s.storeCustomerSubAccount, s.storeCustomerSubAccountErr)
			mockDepositsCore.On("ListCASAAccountsForCustomer", mock.Anything, mock.Anything).Return(
				&depositsCore.ListCASAAccountsForCustomerResponse{Accounts: s.depositsCoreAccounts}, s.depositsCoreBalanceError,
			)
			mockDepositsExp.On("ListSchedulers", mock.Anything, mock.Anything).Return(s.depositsExpListSchedulersResponse, s.depositsExpListSchedulersError)
			mockHermes.On("GetDocuments", mock.Anything, mock.Anything).Return(s.listImageDetailFromHermes, s.listImageDetailFromHermesError)

			ctx := utils.AddUserIDToHeader(context.Background(), s.userID)
			ctx = utils.AddServiceIDToHeader(ctx, s.serviceID)
			ctx = context2.WithClientIdentity(ctx, s.callerService)
			if s.returnCif != nil {
				ctx = testutils.InjectActiveProfileToContext(ctx, s.returnCif.CifNumber)
			}

			appConfig := &config.AppConfig{
				DefaultAppConfig: servus.DefaultAppConfig{
					Data: &servus.DataConfig{
						MySQL: &data.MysqlConfig{},
					},
				},
				Locale: locale,
			}
			service := &AccountService{
				AppConfig:            appConfig,
				GoalCoreClient:       mockGoalCore,
				CustomerMasterClient: mockCustomerMasterClient,
				DepositsCoreClient:   mockDepositsCore,
				DepositsExpClient:    mockDepositsExp,
				HermesClient:         mockHermes,
				Store:                mockStore,
			}
			response, err := service.V2ListSavingsPocketDetail(ctx, s.request)
			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
				assert.Equal(t, err, s.returnError)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.response, response)
			}
		})
	}
}
