package handlers

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"sort"

	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils/validations"
	goalCore "gitlab.myteksi.net/dakota/core-banking/goal-core/api"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	sharedutil "gitlab.myteksi.net/dbmy/core-banking/common/utils"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
)

// ListSavingsPocketDetail ...
func (a *AccountService) ListSavingsPocketDetail(ctx context.Context, req *api.ListSavingsPocketDetailRequest) (*api.ListSavingsPocketDetailResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListSavingsPocketDetail"))

	if err := validateListSavingsPocketDetailRequest(ctx, req); err != nil {
		return nil, err
	}
	if err := utils.CheckIfAuthorized(ctx, req.AccountID, a.Store, a.ExternalCustomerClient); err != nil {
		return nil, err
	}
	return a.listSavingsPocketsDetail(ctx, req)
}

// nolint:dupl,gocognit
func (a *AccountService) listSavingsPocketsDetail(ctx context.Context, req *api.ListSavingsPocketDetailRequest) (*api.ListSavingsPocketDetailResponse, error) {
	var (
		savingPocketDetailsMap = make(map[string]api.SavingsPocketDetail)
		currency               = a.AppConfig.Locale.Currency
		pocketBalance          = make(map[string]int64)
		totalPocketBalance     = int64(0)
		pocketInfo             []dto.PocketInfo
	)

	pockets, pocketIDs, pocketStatus, err := a.getPocketsForAccount(ctx, req.AccountID)
	if err != nil {
		return nil, err
	}
	accBalance, getBalanceErr := a.DepositsCoreClient.GetAccountBalance(ctx, &depositsCore.GetAccountBalanceRequest{AccountID: req.AccountID})
	if getBalanceErr != nil {
		slog.FromContext(ctx).Warn(constants.ListSavingsPocketDetailLogTag, fmt.Sprintf("unable to get balance via deposits-core: %s", getBalanceErr.Error()), utils.GetTraceID(ctx))
		return nil, getBalanceErr
	}
	// If no results from deposits-core means balance is zero
	for _, balance := range accBalance.Balances {
		if (string(pocketStatus[balance.AccountAddress]) != "") &&
			(balance.Phase == "POSTING_PHASE_PENDING_OUTGOING" || balance.Phase == "POSTING_PHASE_COMMITTED") {
			totalPocketBalance += balance.Amount.Val
			pocketBalance[balance.AccountAddress] += balance.Amount.Val
		}
	}
	if len(pocketIDs) == 0 || len(pockets) == 0 {
		return emptyListSavingsPocketDetailResponse(totalPocketBalance, currency), nil
	}

	pocketGoalDetail, getGoalsErr := a.getGoals(ctx, pocketBalance, pocketIDs)
	if getGoalsErr != nil {
		return nil, getGoalsErr
	}

	pocketImageMap, imageMap, imageErr := a.getImageDetails(ctx, pockets)
	if imageErr != nil {
		return nil, imageErr
	}

	for _, pocket := range pockets {
		imageStatus := api.ImageStatus_PROCESSING
		if _, ok := imageMap[pocketImageMap[pocket.PocketID]]; ok {
			imageStatus = api.ImageStatus_SUCCESS
		}
		savingPocketDetail := api.SavingsPocketDetail{
			ID:        pocket.PocketID,
			AccountID: req.AccountID,
			Name:      pocket.Name,
			Image: &api.Image{
				ID:     pocketImageMap[pocket.PocketID],
				URL:    imageMap[pocketImageMap[pocket.PocketID]],
				Status: imageStatus,
			},
			Balance: &api.Money{
				Val:          pocketBalance[pocket.PocketID],
				CurrencyCode: currency,
			},
			Status:     pocketStatus[pocket.PocketID],
			GoalDetail: pocketGoalDetail[pocket.PocketID],
		}
		savingPocketDetailsMap[pocket.PocketID] = savingPocketDetail
		pocketInfo = append(pocketInfo, dto.PocketInfo{CreationTime: pocket.CreatedAt, PocketID: pocket.PocketID})
	}
	return &api.ListSavingsPocketDetailResponse{
		TotalBalance: &api.Money{
			Val:          totalPocketBalance,
			CurrencyCode: currency,
		},
		SavingsPocketDetail: sortPocket(pocketInfo, savingPocketDetailsMap),
	}, nil
}

func emptyListSavingsPocketDetailResponse(balance int64, currency string) *api.ListSavingsPocketDetailResponse {
	return &api.ListSavingsPocketDetailResponse{
		TotalBalance: &api.Money{
			Val:          balance,
			CurrencyCode: currency,
		},
		SavingsPocketDetail: []api.SavingsPocketDetail{},
	}
}

// sortPocket sorts the pocket based on goal status (ongoing first) and creation time (recent created first)
func sortPocket(pocketInfo []dto.PocketInfo, pocketDetailsMap map[string]api.SavingsPocketDetail) []api.SavingsPocketDetail {
	var orderedPocketDetails []api.SavingsPocketDetail
	orderingMap := make(map[string][]dto.PocketInfo)
	sort.Slice(pocketInfo, func(i, j int) bool {
		return pocketInfo[i].CreationTime.After(pocketInfo[j].CreationTime)
	})
	for _, pocket := range pocketInfo {
		if pocketDetailsMap[pocket.PocketID].GoalDetail != nil {
			if pocketDetailsMap[pocket.PocketID].GoalDetail.Status != api.GoalStatus_Status_COMPLETED {
				orderingMap[constants.Ongoing] = append(orderingMap[constants.Ongoing], pocket)
			} else {
				orderingMap[constants.Complete] = append(orderingMap[constants.Complete], pocket)
			}
		} else {
			orderingMap[constants.WithoutGoals] = append(orderingMap[constants.WithoutGoals], pocket)
		}
	}
	for _, val := range orderingMap[constants.Ongoing] {
		orderedPocketDetails = append(orderedPocketDetails, pocketDetailsMap[val.PocketID])
	}
	for _, val := range orderingMap[constants.WithoutGoals] {
		orderedPocketDetails = append(orderedPocketDetails, pocketDetailsMap[val.PocketID])
	}
	for _, val := range orderingMap[constants.Complete] {
		orderedPocketDetails = append(orderedPocketDetails, pocketDetailsMap[val.PocketID])
	}
	return orderedPocketDetails
}

func (a *AccountService) getPocketsForAccount(ctx context.Context, accountID string) ([]*storage.Pocket, []string, map[string]api.AccountStatus, error) {
	var pocketIDs []string
	pocketStatus := make(map[string]api.AccountStatus)
	subAccounts, err := a.Store.GetCustomerSubAccounts(ctx, accountID)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		return nil, nil, nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.ListSavingsPocketDetailLogTag, "Unable to load sub-accounts from DB", err)
	}
	if errors.Is(err, data.ErrNoData) {
		return []*storage.Pocket{}, []string{}, map[string]api.AccountStatus{}, nil
	}
	for _, subAccount := range subAccounts {
		pocketIDs = append(pocketIDs, subAccount.AccountID)
		pocketStatus[subAccount.AccountID] = subAccount.CurrentStatus
	}
	// GetPockets returns not closed pockets Only
	pockets, err := a.Store.GetPockets(ctx, pocketIDs)
	if err != nil {
		return nil, nil, nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.ListSavingsPocketDetailLogTag, "Unable to load pockets from DB", err)
	}
	return pockets, pocketIDs, pocketStatus, nil
}

func (a *AccountService) getGoals(ctx context.Context, pocketBalance map[string]int64, pocketIDs []string) (map[string]*api.GoalDetail, error) {
	goalDetailMap := make(map[string]*api.GoalDetail)
	if len(pocketIDs) == 0 {
		return goalDetailMap, nil
	}
	var goals []goalCore.Goal
	goalsResponse, err := a.GoalCoreClient.GetGoals(ctx, &goalCore.GetGoalsRequest{AccountID: pocketIDs})
	if err != nil {
		switch e := err.(type) {
		case *errorhandling.Error:
			if e.Code != sharedutil.SafeIntToString(goalCore.ErrRecordNotFound.Code) {
				slog.FromContext(ctx).Warn(constants.ListSavingsPocketDetailLogTag, fmt.Sprintf("unable to get all goals via goal-core: %s", err.Error()), utils.GetTraceID(ctx))
				return nil, err
			}
		case *url.Error:
			slog.FromContext(ctx).Warn(constants.ListSavingsPocketDetailLogTag, fmt.Sprintf("URL error : %s", err.Error()), utils.GetTraceID(ctx))
			return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, sharedutil.SafeIntToString(apiErr.ErrGenericInternalError.Code), apiErr.ErrGenericInternalError.Message)
		default:
			slog.FromContext(ctx).Warn(constants.ListSavingsPocketDetailLogTag, fmt.Sprintf("unknown error: %s", err.Error()), utils.GetTraceID(ctx))
			return nil, apiErr.BuildErrorResponse(http.StatusInternalServerError, sharedutil.SafeIntToString(apiErr.ErrUnknown.Code), apiErr.ErrUnknown.Message)
		}
	} else {
		goals = goalsResponse.Goals
	}

	for _, goal := range goals {
		trackingDetail := utils.CalculateTrackingDetail(pocketBalance[goal.AccountID[0]], goal.TargetAmount, goal.TargetDate, goal.CreationDate)
		goalDetailMap[goal.AccountID[0]] = &api.GoalDetail{
			ID: goal.ID,
			TargetAmount: &api.Money{
				CurrencyCode: string(goal.Currency),
				Val:          goal.TargetAmount,
			},
			TargetDate:         goal.TargetDate,
			Status:             trackingDetail.GoalStatus,
			OntrackPercentage:  trackingDetail.OnTrackPercent,
			OfftrackPercentage: trackingDetail.OffTrackPercent,
			OutstandingBalance: &api.Money{
				CurrencyCode: string(goal.Currency),
				Val:          trackingDetail.OutstandingAmount,
			},
		}
	}
	return goalDetailMap, nil
}

func (a *AccountService) getImageDetails(ctx context.Context, pockets []*storage.Pocket) (map[string]string, map[string]string, error) {
	var imageIDs []string
	pocketImageMap := make(map[string]string)
	for _, pocket := range pockets {
		imageIDs = append(imageIDs, pocket.ImageID)
		pocketImageMap[pocket.PocketID] = pocket.ImageID
	}
	hermesResp, hermesErr := utils.GetImageDetailsFromHermes(ctx, constants.ListSavingsPocketDetailLogTag, imageIDs, a.HermesClient)
	if hermesErr != nil {
		return nil, nil, hermesErr
	}
	imageMap := make(map[string]string)
	for _, image := range hermesResp.Documents {
		imageMap[image.Id] = image.PresignedURL
	}
	return pocketImageMap, imageMap, nil
}

func validateListSavingsPocketDetailRequest(ctx context.Context, req *api.ListSavingsPocketDetailRequest) error {
	if err := validations.ValidateFieldNotEmpty(ctx, req.AccountID, "accountID", apiErr.ErrMissingAccountID); err != nil {
		return err
	}
	return nil
}
