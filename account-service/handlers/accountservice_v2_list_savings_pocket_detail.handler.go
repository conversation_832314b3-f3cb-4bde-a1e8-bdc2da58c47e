package handlers

import (
	"context"
	"errors"

	"github.com/samber/lo"

	"gitlab.myteksi.net/dakota/common/servicename"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags/namespaces/common"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/dto"
	"gitlab.myteksi.net/bersama/core-banking/account-service/internal/featureflag"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/utils"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositsExp "gitlab.myteksi.net/dbmy/core-banking/deposits-exp/api"
)

// V2ListSavingsPocketDetail fetches details of all savings pockets
// TODO: This endpoint design should be reviewed again as it is more on experience layer logic
func (a *AccountService) V2ListSavingsPocketDetail(ctx context.Context, req *api.ListSavingsPocketDetailRequest) (*api.ListSavingsPocketDetailResponse, error) {
	ctx = slog.AddTagsToContext(ctx, common.Endpoint("ListSavingsPocketDetail"))
	ctx = featureflag.NewContextWithFeatureFlags(ctx, &a.AppConfig.FeatureFlags)

	if err := validateListSavingsPocketDetailRequest(ctx, req); err != nil {
		return nil, err
	}
	client := utils.GetClientIdentityFromHeader(ctx)
	// resolve cif/bif on best effort basis
	if client == servicename.SentryT6.ToString() || req == nil {
		if err := utils.CheckIfAuthorized(ctx, req.AccountID, a.Store, a.ExternalCustomerClient); err != nil {
			return nil, err
		}
	}
	return a.v2ListSavingsPocketsDetail(ctx, req)
}

// nolint:dupl,funlen
func (a *AccountService) v2ListSavingsPocketsDetail(ctx context.Context, req *api.ListSavingsPocketDetailRequest) (*api.ListSavingsPocketDetailResponse, error) {
	var savingPocketDetailsMap = make(map[string]api.SavingsPocketDetail)

	currency := a.AppConfig.Locale.Currency
	pocketBalance := make(map[string]int64)
	totalPocketBalance := int64(0)
	var pocketInfo []dto.PocketInfo
	pocketAccountPairs, err := a.getPocketAccountPairs(ctx, req.AccountID)
	if err != nil {
		return nil, err
	}

	if len(pocketAccountPairs) == 0 {
		return emptyListSavingsPocketDetailResponse(totalPocketBalance, currency), nil
	}

	allPocketIDs := lo.FilterMap(pocketAccountPairs, func(pocketAccountPair lo.Tuple2[*storage.Pocket, *storage.CustomerAccount], _ int) (string, bool) {
		pocket := pocketAccountPair.A
		if lo.Contains(
			[]constants.CustomerPocketType{constants.PocketTypeSavings, constants.PocketTypeBoostPocket},
			pocket.Type) {
			return pocket.PocketID, true
		}
		return "", false
	})

	savingsPocketIDs := lo.FilterMap(pocketAccountPairs, func(pocketAccountPair lo.Tuple2[*storage.Pocket, *storage.CustomerAccount], _ int) (string, bool) {
		pocket := pocketAccountPair.A
		if constants.PocketTypeSavings == pocket.Type {
			return pocket.PocketID, true
		}
		return "", false
	})

	account, err := a.Store.GetCustomerAccount(ctx, req.AccountID)
	if err != nil {
		return nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.ListSavingsPocketDetailLogTag, "fail to load account from db", err)
	}

	casaAccountMap, err := a.getCasaAccountsFromDepositsCore(ctx, account.CifNumber, true, allPocketIDs)
	if err != nil {
		return nil, err
	}

	for accountID, account := range casaAccountMap {
		amtObj, err := utils.GetAvailableBalance(account.Balances)
		if err != nil {
			return nil, err
		}
		pocketBalance[accountID] = amtObj.Val
		totalPocketBalance = totalPocketBalance + amtObj.Val
	}

	// only savings pocket supports goal for now
	pocketGoalDetail, getGoalsErr := a.getGoals(ctx, pocketBalance, savingsPocketIDs)
	if getGoalsErr != nil {
		return nil, getGoalsErr
	}

	pockets := lo.Map(pocketAccountPairs, func(p lo.Tuple2[*storage.Pocket, *storage.CustomerAccount], _ int) *storage.Pocket { return p.A })
	pocketImageMap, imageMap, imageErr := a.getImageDetails(ctx, pockets)
	if imageErr != nil {
		return nil, imageErr
	}

	pocketSchedulersDetail, getSchedulersErr := a.getSchedulers(ctx, savingsPocketIDs)
	if getSchedulersErr != nil {
		return nil, getSchedulersErr
	}

	for _, pocketAccountPair := range pocketAccountPairs {
		pocket, customerAccount := pocketAccountPair.Unpack()
		imageStatus := api.ImageStatus_PROCESSING
		if _, ok := imageMap[pocketImageMap[pocket.PocketID]]; ok {
			imageStatus = api.ImageStatus_SUCCESS
		}
		savingPocketDetail := api.SavingsPocketDetail{
			ID:        pocket.PocketID,
			AccountID: req.AccountID,
			Name:      pocket.Name,
			Image: &api.Image{
				ID:     pocketImageMap[pocket.PocketID],
				URL:    imageMap[pocketImageMap[pocket.PocketID]],
				Status: imageStatus,
			},
			Balance: &api.Money{
				Val:          pocketBalance[pocket.PocketID],
				CurrencyCode: currency,
			},
			Status:             customerAccount.CurrentStatus,
			GoalDetail:         pocketGoalDetail[pocket.PocketID],
			SchedulerDetail:    pocketSchedulersDetail[pocket.PocketID],
			ProductVariantCode: customerAccount.ProductVariantID,
		}
		savingPocketDetailsMap[pocket.PocketID] = savingPocketDetail
		pocketInfo = append(pocketInfo, dto.PocketInfo{CreationTime: pocket.CreatedAt, PocketID: pocket.PocketID})
	}
	return &api.ListSavingsPocketDetailResponse{
		TotalBalance: &api.Money{
			Val:          totalPocketBalance,
			CurrencyCode: currency,
		},
		SavingsPocketDetail: sortPocket(pocketInfo, savingPocketDetailsMap),
	}, nil
}

func (a *AccountService) getSchedulers(ctx context.Context, pocketIDs []string) (map[string]*api.SchedulerDetail, error) {
	schedulersDetailMap := make(map[string]*api.SchedulerDetail)
	if len(pocketIDs) == 0 {
		return schedulersDetailMap, nil
	}
	schedulersDetail, err := a.DepositsExpClient.ListSchedulers(ctx, &depositsExp.ListSchedulersRequest{AccountIDs: pocketIDs})
	if err != nil {
		return nil, err
	}
	for _, scheduler := range schedulersDetail.Schedulers {
		schedulersDetailMap[scheduler.DestinationAccountID] = &api.SchedulerDetail{
			SchedulerID:   scheduler.SchedulerID,
			SchedulerName: scheduler.SchedulerName,
			Status:        scheduler.Status,
			Amount: &api.Money{
				CurrencyCode: scheduler.Amount.CurrencyCode,
				Val:          scheduler.Amount.Val,
			},
			StartDate:     scheduler.StartDate,
			EndDate:       scheduler.EndDate,
			FrequencyType: scheduler.FrequencyType,
			RepeatCount:   scheduler.RepeatCount,
		}
	}
	return schedulersDetailMap, nil
}

// getPocketsAndAccounts returns list of pair of non-closed Pocket and corresponding CustomerAccount entity.
func (a *AccountService) getPocketAccountPairs(ctx context.Context, accountID string) ([]lo.Tuple2[*storage.Pocket, *storage.CustomerAccount], error) {
	subAccounts, err := a.Store.GetCustomerSubAccounts(ctx, accountID)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		return nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.ListSavingsPocketDetailLogTag, "Unable to load sub-accounts from DB", err)
	}
	if errors.Is(err, data.ErrNoData) {
		return []lo.Tuple2[*storage.Pocket, *storage.CustomerAccount]{}, nil
	}

	subAccountsMap := lo.SliceToMap(subAccounts, func(acc *storage.CustomerAccount) (string, *storage.CustomerAccount) {
		return acc.AccountID, acc
	})

	pocketIDs := lo.Keys(subAccountsMap)
	pockets, err := a.Store.GetPockets(ctx, pocketIDs)
	if err != nil {
		return nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.ListSavingsPocketDetailLogTag, "Unable to load pockets from DB", err)
	}

	result := lo.Map(pockets, func(pocket *storage.Pocket, _ int) lo.Tuple2[*storage.Pocket, *storage.CustomerAccount] {
		return lo.Tuple2[*storage.Pocket, *storage.CustomerAccount]{
			A: pocket,
			B: subAccountsMap[pocket.PocketID],
		}
	})
	return result, nil
}
