package handlers

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/google/uuid"

	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	"gitlab.myteksi.net/bersama/core-banking/account-service/server/config"
	"gitlab.myteksi.net/bersama/core-banking/account-service/storage"
	"gitlab.myteksi.net/bersama/core-banking/account-service/test/responses"
	"gitlab.myteksi.net/bersama/core-banking/account-service/test/utils"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/common/servicename"
	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
	customerMasterMock "gitlab.myteksi.net/dakota/customer-master/api/v2/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	depositsCore "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api"
	mocks "gitlab.myteksi.net/dbmy/core-banking/deposits-core/api/mock"
	"gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/customermaster/dto"
	externalCustomerMock "gitlab.myteksi.net/dbmy/core-banking/external-lib/v2/accessmanagement/customermaster/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestAccountService_GetAccountDetailsByAccountID(t *testing.T) {
	now := time.Now()
	locale := utils.GetLocale()
	currency := utils.GetLocale().Currency
	testIDOne := "testID-1"
	testIDTwo := "testID-2"
	testIDThree := "testID-3"
	testAvailableBalance := &api.Money{
		CurrencyCode: currency,
		Val:          15,
	}
	depositsCoreErr := errors.New("error from deposits-core")
	serviceID := "DIGIBANK"
	userID := "user1"
	pendingActionEntities := []*storage.PendingAction{{
		Name:     storage.PendingActionFirstDeposit,
		Status:   storage.PendingActionStatusPending,
		PublicID: uuid.New().String(),
	}}
	pendingActionDTO := api.AccountPendingAction{
		ID:     pendingActionEntities[0].PublicID,
		Name:   storage.PendingActionFirstDeposit,
		Status: string(storage.PendingActionStatusPending),
	}

	scenarios := []struct {
		testDesc              string
		accountID             string
		fetchBalance          bool
		getCASAAccountRequest *depositsCore.GetCASAAccountRequest

		customerAccount    *storage.CustomerAccount
		customerAccountErr error

		getCASAAccountResponse *depositsCore.GetCASAAccountResponse
		getCASAAAccountErr     error

		getAccountBalanceRequest  *depositsCore.GetAccountBalanceRequest
		getAccountBalanceResponse *depositsCore.GetAccountBalanceResponse
		getAccountBalanceErr      error

		lookupCIFNumberRequest  *customerMaster.LookupCIFNumberRequest
		lookupCIFNumberResponse *customerMaster.LookupCIFNumberResponse

		pendingActionEntities []*storage.PendingAction

		clientIdentity string
		serviceID      string
		userID         string
		lookupCifErr   error

		isErrorExpected bool
		expectedAccount *api.GetAccountResponse
		expectedError   error

		isBizAuthorisationEnabled bool
		getBusinessInfoResponse   *dto.GetBusinessInfoResponse
	}{
		{
			testDesc:     "Should return account details when valid account id in request body for Active account",
			accountID:    testIDOne,
			fetchBalance: false,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
				Type:                    storage.AccountTypeCasa,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest:  &depositsCore.GetAccountBalanceRequest{},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{},
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:              testIDOne,
					ParentAccountID: "test-parent-account",
					CifNumber:       "test-cif",
					Status:          api.AccountStatus_ACTIVE,
					ProductSpecificParameters: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					Name:                "",
					PermittedCurrencies: []string{locale.Currency},
					ProductID:           "test-id",
					ProductVariantID:    "vr-1234",
					OpeningTimestamp:    now,
					AccountType:         string(storage.AccountTypeCasa),
				},
			},
		},
		{
			testDesc: "Should return parent account details with pending actions when valid account id" +
				" in request body for Active account",
			accountID:    testIDOne,
			fetchBalance: false,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "",
					Valid:  false,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest:  &depositsCore.GetAccountBalanceRequest{},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{},
			pendingActionEntities:     pendingActionEntities,
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:              testIDOne,
					ParentAccountID: "",
					CifNumber:       "test-cif",
					Status:          api.AccountStatus_ACTIVE,
					ProductSpecificParameters: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					Name:                "",
					PermittedCurrencies: []string{locale.Currency},
					ProductID:           "test-id",
					ProductVariantID:    "vr-1234",
					OpeningTimestamp:    now,
					PendingActions:      []api.AccountPendingAction{pendingActionDTO},
				},
			},
		},
		{
			testDesc:       "Should return account details when valid account id in request body for Active account via T6",
			clientIdentity: servicename.SentryT6.ToString(),
			serviceID:      serviceID,
			userID:         userID,
			accountID:      testIDOne,
			fetchBalance:   false,
			lookupCifErr:   nil,
			lookupCIFNumberResponse: &customerMaster.LookupCIFNumberResponse{
				CifNumber: "test-cif",
			},
			lookupCIFNumberRequest: &customerMaster.LookupCIFNumberRequest{
				ID: userID,
				Target: &customerMaster.TargetGroup{
					ServiceID: serviceID,
				},
			},
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						"interest_application_day": "1",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest:  &depositsCore.GetAccountBalanceRequest{},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{},
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:                        testIDOne,
					ParentAccountID:           "test-parent-account",
					CifNumber:                 "test-cif",
					Status:                    api.AccountStatus_ACTIVE,
					ProductSpecificParameters: map[string]string{"applicableHoldcodes": "[]"},
					Name:                      "",
					PermittedCurrencies:       []string{locale.Currency},
					ProductID:                 "test-id",
					ProductVariantID:          "vr-1234",
					OpeningTimestamp:          now,
				},
			},
		},
		{
			testDesc:        "Should return error when serviceID is missing via T6",
			accountID:       testIDOne,
			clientIdentity:  servicename.SentryT6.ToString(),
			serviceID:       "",
			userID:          "",
			lookupCifErr:    apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrJWTNotFound.Code), 10), apiErr.ErrJWTNotFound.Message),
			expectedError:   apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrJWTNotFound.Code), 10), apiErr.ErrJWTNotFound.Message),
			isErrorExpected: true,
		},
		{
			testDesc:  "Should return account details when valid account id in request body for Dormant account",
			accountID: testIDTwo,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDTwo,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID:     testIDTwo,
				CifNumber:     "test-cif",
				CurrentStatus: api.AccountStatus_DORMANT,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDTwo,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "",
					},
					DerivedParams: map[string]string{},
				},
			},
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:              testIDTwo,
					ParentAccountID: "test-parent-account",
					CifNumber:       "test-cif",
					Status:          api.AccountStatus_DORMANT,
					ProductSpecificParameters: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					Name:                "",
					PermittedCurrencies: []string{locale.Currency},
					ProductID:           "test-id",
					ProductVariantID:    "vr-1234",
					OpeningTimestamp:    now,
				},
			},
			getAccountBalanceRequest:  &depositsCore.GetAccountBalanceRequest{},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{},
		},
		{
			testDesc:     "Should return account details when valid account id in request body for Closed account",
			accountID:    testIDThree,
			fetchBalance: false,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDThree,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID:     testIDThree,
				CifNumber:     "test-cif",
				CurrentStatus: api.AccountStatus_CLOSED,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDThree,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_CLOSED,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "",
					},
					DerivedParams: map[string]string{},
				},
			},
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:              testIDThree,
					ParentAccountID: "test-parent-account",
					CifNumber:       "test-cif",
					Status:          api.AccountStatus_CLOSED,
					ProductSpecificParameters: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					Name:                "",
					PermittedCurrencies: []string{locale.Currency},
					ProductID:           "test-id",
					ProductVariantID:    "vr-1234",
					OpeningTimestamp:    now,
				},
			},
			getAccountBalanceRequest:  &depositsCore.GetAccountBalanceRequest{},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{},
		},
		{
			testDesc: "Should return account details with balance when valid account id" +
				" in request body for Active account",
			accountID:    testIDOne,
			fetchBalance: true,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest: &depositsCore.GetAccountBalanceRequest{
				AccountID: testIDOne,
			},
			getAccountBalanceResponse: responses.DepositsCoreBalanceWithoutPocketsResponse(),
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:              testIDOne,
					ParentAccountID: "test-parent-account",
					CifNumber:       "test-cif",
					Status:          api.AccountStatus_ACTIVE,
					ProductSpecificParameters: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					Name:                "",
					PermittedCurrencies: []string{locale.Currency},
					ProductID:           "test-id",
					ProductVariantID:    "vr-1234",
					OpeningTimestamp:    now,
					AvailableBalance:    testAvailableBalance,
				},
			},
		},
		{
			testDesc:        "Should return error when accountID is missing",
			accountID:       "",
			customerAccount: &storage.CustomerAccount{},
			isErrorExpected: true,
			expectedError:   apiErr.BuildErrorResponse(http.StatusBadRequest, strconv.FormatInt(int64(apiErr.ErrMissingAccountID.Code), 10), apiErr.ErrMissingAccountID.Message),
		},
		{
			testDesc:  "Should return error when account not found in database",
			accountID: "testID",
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: "testID",
			},
			customerAccountErr:     data.ErrNoData,
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{},
			expectedAccount:        &api.GetAccountResponse{},
			isErrorExpected:        true,
			expectedError:          apiErr.BuildErrorResponse(http.StatusNotFound, strconv.FormatInt(int64(apiErr.ErrAccountNotFound.Code), 10), apiErr.ErrAccountNotFound.Message),
		},
		{
			testDesc:  "Should return error when database failure",
			accountID: "testID",
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: "testID",
			},
			customerAccountErr:     errors.New("failed to load customerAccount"),
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{},
			expectedAccount:        &api.GetAccountResponse{},
			isErrorExpected:        true,
			expectedError:          apiErr.BuildErrorResponse(http.StatusInternalServerError, strconv.FormatInt(int64(apiErr.ErrDatabaseFailure.Code), 10), apiErr.ErrDatabaseFailure.Message),
		},
		{
			testDesc:  "Should return error when deposits-core GetCustomerAccount returns error",
			accountID: testIDThree,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDThree,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID:     testIDThree,
				CifNumber:     "test-cif",
				CurrentStatus: api.AccountStatus_CLOSED,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{},
			getCASAAAccountErr:     depositsCoreErr,
			isErrorExpected:        true,
			expectedError:          depositsCoreErr,
		},
		{
			testDesc:     "Should return error when deposits-core GetAccountBalance returns error",
			accountID:    testIDOne,
			fetchBalance: true,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest: &depositsCore.GetAccountBalanceRequest{
				AccountID: testIDOne,
			},
			getAccountBalanceErr: errors.New("error from deposits core getAccountBalance"),
			isErrorExpected:      true,
			expectedError:        errors.New("error from deposits core getAccountBalance"),
		},
		{
			testDesc:     "Should return error when empty balances received from deposits-core GetAccountBalance",
			accountID:    testIDOne,
			fetchBalance: true,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						constants.ApplicableHoldcodes: "[]",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest: &depositsCore.GetAccountBalanceRequest{
				AccountID: testIDOne,
			},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{
				AccountID:        testIDOne,
				AvailableBalance: nil,
				Balances:         []depositsCore.AccountBalance{},
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusInternalServerError,
				strconv.FormatInt(int64(apiErr.ErrEmptyBalancesForAccount.Code), 10), apiErr.ErrEmptyBalancesForAccount.Message),
		},
		{
			testDesc:       "Should return account details when valid bif and biz authorisation is enabled",
			clientIdentity: servicename.SentryT6.ToString(),
			serviceID:      serviceID,
			userID:         userID,
			accountID:      testIDOne,
			fetchBalance:   false,
			getCASAAccountRequest: &depositsCore.GetCASAAccountRequest{
				AccountID: testIDOne,
			},
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			getCASAAccountResponse: &depositsCore.GetCASAAccountResponse{
				Account: &depositsCore.CASAAccount{
					Id:                  testIDOne,
					ProductVariantID:    "vr-1234",
					InternalCustomerID:  "test-owner-1",
					PermittedCurrencies: []string{locale.Currency},
					Status:              depositsCore.AccountStatus_ACCOUNT_STATUS_OPEN,
					OpeningTimestamp:    now,
					ClosingTimestamp:    time.Time{},
					InstanceParams: map[string]string{
						"interest_application_day": "1",
					},
					DerivedParams: map[string]string{},
				},
			},
			getAccountBalanceRequest:  &depositsCore.GetAccountBalanceRequest{},
			getAccountBalanceResponse: &depositsCore.GetAccountBalanceResponse{},
			expectedAccount: &api.GetAccountResponse{
				Account: &api.Account{
					Id:                        testIDOne,
					ParentAccountID:           "test-parent-account",
					CifNumber:                 "test-cif",
					Status:                    api.AccountStatus_ACTIVE,
					ProductSpecificParameters: map[string]string{"applicableHoldcodes": "[]"},
					Name:                      "",
					PermittedCurrencies:       []string{locale.Currency},
					ProductID:                 "test-id",
					ProductVariantID:          "vr-1234",
					OpeningTimestamp:          now,
				},
			},
			isBizAuthorisationEnabled: true,
			getBusinessInfoResponse: &dto.GetBusinessInfoResponse{
				BusinessRelationships: []dto.BusinessRelationship{{BIF: "test-cif"}},
			},
		},
		{
			testDesc:       "Should return error when invalid bif and biz authorisation is enabled",
			clientIdentity: servicename.SentryT6.ToString(),
			serviceID:      serviceID,
			userID:         userID,
			accountID:      testIDOne,
			fetchBalance:   false,
			customerAccount: &storage.CustomerAccount{
				AccountID: testIDOne,
				ParentAccountID: sql.NullString{
					String: "test-parent-account",
					Valid:  true,
				},
				ProductID:               "test-id",
				ProductVariantID:        "vr-1234",
				CifNumber:               "test-cif",
				CurrentStatus:           api.AccountStatus_ACTIVE,
				AccountOpeningTimestamp: now,
			},
			isErrorExpected: true,
			expectedError: apiErr.BuildErrorResponse(http.StatusForbidden,
				strconv.FormatInt(int64(apiErr.ErrAccountPermissionForbidden.Code), 10), apiErr.ErrAccountPermissionForbidden.Message),
			isBizAuthorisationEnabled: true,
			getBusinessInfoResponse:   &dto.GetBusinessInfoResponse{},
		},
	}

	for _, scenario := range scenarios {
		s := scenario
		t.Run(s.testDesc, func(t *testing.T) {
			mockClient := &mocks.DepositsCore{}
			mockStore := &storage.MockDatabaseStore{}
			mockCustomerMasterClient := &customerMasterMock.CustomerMaster{}
			mockExternalCustomerClient := &externalCustomerMock.ICustomerMasterClient{}
			mockExternalCustomerClient.On("GetBusinessInfo", mock.Anything, mock.Anything).Return(s.getBusinessInfoResponse, nil)
			mockClient.On("GetCASAAccount", mock.Anything, s.getCASAAccountRequest).Return(s.getCASAAccountResponse, s.getCASAAAccountErr)
			mockClient.On("GetAccountBalance", mock.Anything, s.getAccountBalanceRequest).Return(s.getAccountBalanceResponse, s.getAccountBalanceErr)
			mockStore.On("GetCustomerAccountByCif", mock.Anything, mock.Anything).Return(s.customerAccount, nil)
			mockStore.On("GetCustomerAccount", mock.Anything, mock.Anything).Return(s.customerAccount, s.customerAccountErr)
			mockStore.On("GetAccountPendingActionsByAccountID", mock.Anything, mock.Anything).Return(s.pendingActionEntities, nil)

			ctx := commonCtx.WithClientIdentity(context.Background(), s.clientIdentity)
			ctx = commonCtx.WithServiceID(ctx, s.serviceID)
			ctx = commonCtx.WithUserID(ctx, s.userID)
			if s.lookupCIFNumberResponse != nil {
				ctx = utils.InjectActiveProfileToContext(ctx, s.lookupCIFNumberResponse.CifNumber)
			}

			service := &AccountService{
				AppConfig: &config.AppConfig{
					Locale: locale,
					FeatureFlags: config.FeatureFlags{
						EnableBIZAuthorisation: s.isBizAuthorisationEnabled,
					},
				},
				DepositsCoreClient:     mockClient,
				Store:                  mockStore,
				CustomerMasterClient:   mockCustomerMasterClient,
				ExternalCustomerClient: mockExternalCustomerClient,
			}
			account, err := service.GetAccountDetailsByAccountID(ctx, &api.GetAccountRequest{AccountID: s.accountID, FetchBalance: s.fetchBalance})

			if s.isErrorExpected {
				assert.Error(t, err, s.testDesc)
				assert.Equal(t, s.expectedError, err)
			} else {
				assert.NoError(t, err, s.testDesc)
				assert.Equal(t, s.expectedAccount.Account, account.Account)
			}
		})
	}
}
