// Code generated by mockery v2.53.3. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"

	sql "database/sql"
)

// MockDatabaseStore is an autogenerated mock type for the DatabaseStore type
type MockDatabaseStore struct {
	mock.Mock
}

// GetCustomerAccountsCountByFilters provides a mock function with given fields: ctx, filters
func (_m *MockDatabaseStore) GetCustomerAccountsCountByFilters(ctx context.Context, filters []data.Condition) (int64, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for CountCustomerAccountRowsByFilters")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []data.Condition) (int64, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []data.Condition) int64); ok {
		r0 = rf(ctx, filters)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, []data.Condition) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateCustomerAccount provides a mock function with given fields: ctx, customerAccount
func (_m *MockDatabaseStore) CreateCustomerAccount(ctx context.Context, customerAccount *CustomerAccount) error {
	ret := _m.Called(ctx, customerAccount)

	if len(ret) == 0 {
		panic("no return value specified for CreateCustomerAccount")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *CustomerAccount) error); ok {
		r0 = rf(ctx, customerAccount)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateCustomerAccountStatusHistory provides a mock function with given fields: ctx, customerAccountStatusHistory
func (_m *MockDatabaseStore) CreateCustomerAccountStatusHistory(ctx context.Context, customerAccountStatusHistory *CustomerAccountStatusHistory) error {
	ret := _m.Called(ctx, customerAccountStatusHistory)

	if len(ret) == 0 {
		panic("no return value specified for CreateCustomerAccountStatusHistory")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *CustomerAccountStatusHistory) error); ok {
		r0 = rf(ctx, customerAccountStatusHistory)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreatePocket provides a mock function with given fields: ctx, pocket
func (_m *MockDatabaseStore) CreatePocket(ctx context.Context, pocket *Pocket) error {
	ret := _m.Called(ctx, pocket)

	if len(ret) == 0 {
		panic("no return value specified for CreatePocket")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *Pocket) error); ok {
		r0 = rf(ctx, pocket)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreatePocketTemplateAnswers provides a mock function with given fields: ctx, pocketTemplateAnswers
func (_m *MockDatabaseStore) CreatePocketTemplateAnswers(ctx context.Context, pocketTemplateAnswers []*PocketTemplateAnswer) error {
	ret := _m.Called(ctx, pocketTemplateAnswers)

	if len(ret) == 0 {
		panic("no return value specified for CreatePocketTemplateAnswers")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*PocketTemplateAnswer) error); ok {
		r0 = rf(ctx, pocketTemplateAnswers)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateRequestLog provides a mock function with given fields: ctx, _a1
func (_m *MockDatabaseStore) CreateRequestLog(ctx context.Context, _a1 *RequestLog) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for CreateRequestLog")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *RequestLog) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ExecuteTransaction provides a mock function with given fields: ctx, config, stmts
func (_m *MockDatabaseStore) ExecuteTransaction(ctx context.Context, config *data.MysqlConfig, stmts []*TransactionStmt) error {
	ret := _m.Called(ctx, config, stmts)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteTransaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *data.MysqlConfig, []*TransactionStmt) error); ok {
		r0 = rf(ctx, config, stmts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAccountPendingActionByName provides a mock function with given fields: ctx, accountID, name
func (_m *MockDatabaseStore) GetAccountPendingActionByName(ctx context.Context, accountID string, name string) (*PendingAction, error) {
	ret := _m.Called(ctx, accountID, name)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountPendingActionByName")
	}

	var r0 *PendingAction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*PendingAction, error)); ok {
		return rf(ctx, accountID, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *PendingAction); ok {
		r0 = rf(ctx, accountID, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*PendingAction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, accountID, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAccountPendingActionsByAccountID provides a mock function with given fields: ctx, accountID
func (_m *MockDatabaseStore) GetAccountPendingActionsByAccountID(ctx context.Context, accountID string) ([]*PendingAction, error) {
	ret := _m.Called(ctx, accountID)

	if len(ret) == 0 {
		panic("no return value specified for GetAccountPendingActionsByAccountID")
	}

	var r0 []*PendingAction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*PendingAction, error)); ok {
		return rf(ctx, accountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*PendingAction); ok {
		r0 = rf(ctx, accountID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*PendingAction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccount provides a mock function with given fields: ctx, accountID
func (_m *MockDatabaseStore) GetCustomerAccount(ctx context.Context, accountID string) (*CustomerAccount, error) {
	ret := _m.Called(ctx, accountID)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccount")
	}

	var r0 *CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*CustomerAccount, error)); ok {
		return rf(ctx, accountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *CustomerAccount); ok {
		r0 = rf(ctx, accountID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccountByCif provides a mock function with given fields: ctx, cif
func (_m *MockDatabaseStore) GetCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	ret := _m.Called(ctx, cif)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccountByCif")
	}

	var r0 []*CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*CustomerAccount, error)); ok {
		return rf(ctx, cif)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*CustomerAccount); ok {
		r0 = rf(ctx, cif)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, cif)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccountByCifStatusProductVariant provides a mock function with given fields: ctx, cif, statuses, productVariantCodes, pagination
func (_m *MockDatabaseStore) GetCustomerAccountByCifStatusProductVariant(ctx context.Context, cif string, statuses []string, productVariantCodes []string, pagination Pagination) ([]*CustomerAccount, error) {
	ret := _m.Called(ctx, cif, statuses, productVariantCodes, pagination)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccountByCifStatusProductVariant")
	}

	var r0 []*CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []string, []string, Pagination) ([]*CustomerAccount, error)); ok {
		return rf(ctx, cif, statuses, productVariantCodes, pagination)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []string, []string, Pagination) []*CustomerAccount); ok {
		r0 = rf(ctx, cif, statuses, productVariantCodes, pagination)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []string, []string, Pagination) error); ok {
		r1 = rf(ctx, cif, statuses, productVariantCodes, pagination)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccountByFilters provides a mock function with given fields: ctx, filters
func (_m *MockDatabaseStore) GetCustomerAccountByFilters(ctx context.Context, filters []data.Condition) ([]*CustomerAccount, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccountByFilters")
	}

	var r0 []*CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []data.Condition) ([]*CustomerAccount, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []data.Condition) []*CustomerAccount); ok {
		r0 = rf(ctx, filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []data.Condition) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccountPending provides a mock function with given fields: ctx, cif, productVariantID
func (_m *MockDatabaseStore) GetCustomerAccountPending(ctx context.Context, cif string, productVariantID string) (*CustomerAccount, error) {
	ret := _m.Called(ctx, cif, productVariantID)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccountPending")
	}

	var r0 *CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*CustomerAccount, error)); ok {
		return rf(ctx, cif, productVariantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *CustomerAccount); ok {
		r0 = rf(ctx, cif, productVariantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, cif, productVariantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccountStatusHistory provides a mock function with given fields: ctx, filters
func (_m *MockDatabaseStore) GetCustomerAccountStatusHistory(ctx context.Context, filters []data.Condition) ([]*CustomerAccountStatusHistory, error) {
	ret := _m.Called(ctx, filters)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccountStatusHistory")
	}

	var r0 []*CustomerAccountStatusHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []data.Condition) ([]*CustomerAccountStatusHistory, error)); ok {
		return rf(ctx, filters)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []data.Condition) []*CustomerAccountStatusHistory); ok {
		r0 = rf(ctx, filters)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccountStatusHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []data.Condition) error); ok {
		r1 = rf(ctx, filters)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerAccountsByVariantID provides a mock function with given fields: ctx, parentAccountID, variantID
func (_m *MockDatabaseStore) GetCustomerAccountsByVariantID(ctx context.Context, parentAccountID string, variantID string) ([]*CustomerAccount, error) {
	ret := _m.Called(ctx, parentAccountID, variantID)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerAccountsByVariantID")
	}

	var r0 []*CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]*CustomerAccount, error)); ok {
		return rf(ctx, parentAccountID, variantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []*CustomerAccount); ok {
		r0 = rf(ctx, parentAccountID, variantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, parentAccountID, variantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerSubAccountByName provides a mock function with given fields: ctx, parentAccountID, name
func (_m *MockDatabaseStore) GetCustomerSubAccountByName(ctx context.Context, parentAccountID string, name string) (int64, error) {
	ret := _m.Called(ctx, parentAccountID, name)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerSubAccountByName")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (int64, error)); ok {
		return rf(ctx, parentAccountID, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) int64); ok {
		r0 = rf(ctx, parentAccountID, name)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, parentAccountID, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerSubAccountByNameAndStatus provides a mock function with given fields: ctx, parentAccountID, name, status
func (_m *MockDatabaseStore) GetCustomerSubAccountByNameAndStatus(ctx context.Context, parentAccountID string, name string, status string) (int64, error) {
	ret := _m.Called(ctx, parentAccountID, name, status)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerSubAccountByNameAndStatus")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (int64, error)); ok {
		return rf(ctx, parentAccountID, name, status)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) int64); ok {
		r0 = rf(ctx, parentAccountID, name, status)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, parentAccountID, name, status)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCustomerSubAccounts provides a mock function with given fields: ctx, accountID
func (_m *MockDatabaseStore) GetCustomerSubAccounts(ctx context.Context, accountID string) ([]*CustomerAccount, error) {
	ret := _m.Called(ctx, accountID)

	if len(ret) == 0 {
		panic("no return value specified for GetCustomerSubAccounts")
	}

	var r0 []*CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*CustomerAccount, error)); ok {
		return rf(ctx, accountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*CustomerAccount); ok {
		r0 = rf(ctx, accountID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDatabaseHandle provides a mock function with given fields: ctx, config
func (_m *MockDatabaseStore) GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error) {
	ret := _m.Called(ctx, config)

	if len(ret) == 0 {
		panic("no return value specified for GetDatabaseHandle")
	}

	var r0 *sql.DB
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *data.MysqlConfig) (*sql.DB, error)); ok {
		return rf(ctx, config)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *data.MysqlConfig) *sql.DB); ok {
		r0 = rf(ctx, config)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*sql.DB)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *data.MysqlConfig) error); ok {
		r1 = rf(ctx, config)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetNonClosedCustomerAccountByCif provides a mock function with given fields: ctx, cif
func (_m *MockDatabaseStore) GetNonClosedCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	ret := _m.Called(ctx, cif)

	if len(ret) == 0 {
		panic("no return value specified for GetNonClosedCustomerAccountByCif")
	}

	var r0 []*CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*CustomerAccount, error)); ok {
		return rf(ctx, cif)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*CustomerAccount); ok {
		r0 = rf(ctx, cif)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*CustomerAccount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, cif)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPockets provides a mock function with given fields: ctx, pocketIDs
func (_m *MockDatabaseStore) GetPockets(ctx context.Context, pocketIDs []string) ([]*Pocket, error) {
	ret := _m.Called(ctx, pocketIDs)

	if len(ret) == 0 {
		panic("no return value specified for GetPockets")
	}

	var r0 []*Pocket
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*Pocket, error)); ok {
		return rf(ctx, pocketIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*Pocket); ok {
		r0 = rf(ctx, pocketIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*Pocket)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, pocketIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRequestLog provides a mock function with given fields: ctx, requestID
func (_m *MockDatabaseStore) GetRequestLog(ctx context.Context, requestID string) (*RequestLog, error) {
	ret := _m.Called(ctx, requestID)

	if len(ret) == 0 {
		panic("no return value specified for GetRequestLog")
	}

	var r0 *RequestLog
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*RequestLog, error)); ok {
		return rf(ctx, requestID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *RequestLog); ok {
		r0 = rf(ctx, requestID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*RequestLog)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, requestID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSubAccountsCount provides a mock function with given fields: ctx, accountID
func (_m *MockDatabaseStore) GetSubAccountsCount(ctx context.Context, accountID string) (int64, error) {
	ret := _m.Called(ctx, accountID)

	if len(ret) == 0 {
		panic("no return value specified for GetSubAccountsCount")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return rf(ctx, accountID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = rf(ctx, accountID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accountID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateCustomerAccount provides a mock function with given fields: ctx, preData, newData
func (_m *MockDatabaseStore) UpdateCustomerAccount(ctx context.Context, preData CustomerAccount, newData CustomerAccount) (CustomerAccount, error) {
	ret := _m.Called(ctx, preData, newData)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCustomerAccount")
	}

	var r0 CustomerAccount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CustomerAccount, CustomerAccount) (CustomerAccount, error)); ok {
		return rf(ctx, preData, newData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CustomerAccount, CustomerAccount) CustomerAccount); ok {
		r0 = rf(ctx, preData, newData)
	} else {
		r0 = ret.Get(0).(CustomerAccount)
	}

	if rf, ok := ret.Get(1).(func(context.Context, CustomerAccount, CustomerAccount) error); ok {
		r1 = rf(ctx, preData, newData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdatePendingAction provides a mock function with given fields: ctx, action
func (_m *MockDatabaseStore) UpdatePendingAction(ctx context.Context, action *PendingAction) error {
	ret := _m.Called(ctx, action)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePendingAction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *PendingAction) error); ok {
		r0 = rf(ctx, action)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateRequestLog provides a mock function with given fields: ctx, _a1
func (_m *MockDatabaseStore) UpdateRequestLog(ctx context.Context, _a1 *RequestLog) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for UpdateRequestLog")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *RequestLog) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewMockDatabaseStore creates a new instance of MockDatabaseStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDatabaseStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDatabaseStore {
	mock := &MockDatabaseStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
