package storage

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func Test_convertToInterface(t *testing.T) {
	type args struct {
		arr []string
	}
	tests := []struct {
		name string
		args args
		want []interface{}
	}{
		{
			"Test convert to interface",
			args{
				arr: []string{"grab"},
			},
			[]interface{}{"grab"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, convertToInterface(tt.args.arr), "convertToInterface(%v)", tt.args.arr)
		})
	}
}

func TestNew(t *testing.T) {
	type args struct {
		customerAccountDB              ICustomerAccountDAO
		customerAccountStatusHistoryDB ICustomerAccountStatusHistoryDAO
		requestLogDB                   IRequestLogDAO
		pocketDB                       IPocketDAO
		pocketTemplateAnswerDB         IPocketTemplateAnswerDAO
		pendingActionDB                IPendingActionDAO
		schedulerLockDB                ISchedulerLockDAO
		stats                          statsd.Client
	}
	tests := []struct {
		name string
		args args
		want *Store
	}{
		{"Testing contructor",
			args{},
			&Store{
				db: &dbStore{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, New(
				tt.args.customerAccountDB,
				tt.args.customerAccountStatusHistoryDB,
				tt.args.requestLogDB,
				tt.args.pocketDB,
				tt.args.pocketTemplateAnswerDB,
				tt.args.pendingActionDB,
				tt.args.schedulerLockDB,
				tt.args.stats,
			),
				"New(%v, %v, %v, %v, %v, %v, %v)",
				tt.args.customerAccountDB,
				tt.args.customerAccountStatusHistoryDB,
				tt.args.requestLogDB,
				tt.args.pocketDB,
				tt.args.pocketTemplateAnswerDB,
				tt.args.pendingActionDB,
				tt.args.stats)
		})
	}
}

func TestStore_CreateCustomerAccount(t *testing.T) {
	scenarios := []struct {
		name      string
		input     *CustomerAccount
		mockError error
		wantErr   bool
	}{
		{
			name: "Happy Case",
			input: &CustomerAccount{
				AccountID: "testID",
			},
		},
		{
			name: "Error Case",
			input: &CustomerAccount{
				AccountID: "testID",
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Save", mock.Anything, mock.Anything).Return(tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			err := store.CreateCustomerAccount(context.Background(), tt.input)
			assert.Equalf(t, tt.wantErr, err != nil, "CreateCustomerAccount(%v)", tt.input)
		})
	}
}

func TestStore_UpdateCustomerAccount(t *testing.T) {
	scenarios := []struct {
		name      string
		preData   CustomerAccount
		postData  CustomerAccount
		mockError error
		wantErr   bool
	}{
		{
			name: "Happy Case",
			preData: CustomerAccount{
				AccountID: "testID",
			},
			postData: CustomerAccount{
				AccountID: "testID1",
			},
		},
		{
			name: "Error Case",
			preData: CustomerAccount{
				AccountID: "testID",
			},
			postData: CustomerAccount{
				AccountID: "testID1",
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.UpdateCustomerAccount(context.Background(), tt.preData, tt.postData)
			assert.Equalf(t, tt.wantErr, err != nil, "UpdateCustomerAccount(%v)", tt.name)
		})
	}
}

func TestStore_GetCustomerAccount(t *testing.T) {
	scenarios := []struct {
		name      string
		accountID string
		mockError error
		wantErr   bool
	}{
		{
			name:      "Happy Case",
			accountID: "testID",
		},
		{
			name:      "Error Case",
			accountID: "testID",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:      "empty accountID Case",
			accountID: "",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Find", mock.Anything, mock.Anything).Return([]*CustomerAccount{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerAccount(context.Background(), tt.accountID)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerAccount(%v)", tt.name)
		})
	}
}

func TestStore_GetCustomerAccountByCif(t *testing.T) {
	scenarios := []struct {
		name      string
		cifNumber string
		mockError error
		wantErr   bool
	}{
		{
			name:      "Happy Case",
			cifNumber: "testID",
		},
		{
			name:      "Error Case",
			cifNumber: "testID",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:      "empty cifNumber Case",
			cifNumber: "",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Find", mock.Anything, mock.Anything).Return([]*CustomerAccount{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerAccountByCif(context.Background(), tt.cifNumber)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerAccountByCif(%v)", tt.name)
		})
	}
}

func TestStore_GetCustomerAccountsByVariantID(t *testing.T) {
	scenarios := []struct {
		name      string
		accountID string
		variantID string
		mockError error
		wantErr   bool
	}{
		{
			name:      "Happy Case",
			accountID: "testID",
			variantID: "testVID",
		},
		{
			name:      "Error Case",
			accountID: "testID",
			variantID: "testVID",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:      "empty accountID Case",
			accountID: "",
			variantID: "testVID",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*CustomerAccount{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerAccountsByVariantID(context.Background(), tt.accountID, tt.variantID)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerAccountsByVariantID(%v)", tt.name)
		})
	}
}

func TestStore_GetCustomerSubAccounts(t *testing.T) {
	scenarios := []struct {
		name      string
		accountID string
		mockError error
		wantErr   bool
	}{
		{
			name:      "Happy Case",
			accountID: "testID",
		},
		{
			name:      "Error Case",
			accountID: "testID",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:      "empty accountID Case",
			accountID: "",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*CustomerAccount{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerSubAccounts(context.Background(), tt.accountID)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerSubAccounts(%v)", tt.name)
		})
	}
}

func TestStore_GetCustomerAccountByFilters(t *testing.T) {
	scenarios := []struct {
		name      string
		filters   []data.Condition
		mockError error
		wantErr   bool
	}{
		{
			name: "Happy Case",
			filters: []data.Condition{
				data.EqualTo("test", "test"),
			},
		},
		{
			name: "Error Case",
			filters: []data.Condition{
				data.EqualTo("test", "test"),
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:    "empty filters Case",
			filters: []data.Condition{},
			wantErr: true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Find", mock.Anything, mock.Anything).Return([]*CustomerAccount{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerAccountByFilters(context.Background(), tt.filters)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerAccountByFilters(%v)", tt.name)
		})
	}
}

func TestStore_GetPockets(t *testing.T) {
	scenarios := []struct {
		name      string
		pocketIDs []string
		mockError error
		wantErr   bool
	}{
		{
			name: "Happy Case",
			pocketIDs: []string{
				"test",
			},
		},
		{
			name: "Error Case",
			pocketIDs: []string{
				"test",
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:      "empty pocketIDs Case",
			pocketIDs: nil,
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockPocketDAO := &MockIPocketDAO{}
			mockPocketDAO.On("Find", mock.Anything, mock.Anything).Return([]*Pocket{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					PocketDao: mockPocketDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetPockets(context.Background(), tt.pocketIDs)
			assert.Equalf(t, tt.wantErr, err != nil, "GetPockets(%v)", tt.name)
		})
	}
}

func TestStore_GetSubAccountsCount(t *testing.T) {
	scenarios := []struct {
		name      string
		accountID string
		mockError error
		wantErr   bool
	}{
		{
			name:      "Happy Case",
			accountID: "test",
		},
		{
			name:      "Error Case",
			accountID: "test",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			name:      "empty accountID Case",
			accountID: "",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.name, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Count", mock.Anything, mock.Anything).Return(int64(1), tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetSubAccountsCount(context.Background(), tt.accountID)
			assert.Equalf(t, tt.wantErr, err != nil, "GetPocketByAccountID(%v)", tt.name)
		})
	}
}

func TestStore_GetCustomerSubAccountByName(t *testing.T) {
	scenarios := []struct {
		desc      string
		accountID string
		name      string
		mockError error
		wantErr   bool
	}{
		{
			desc:      "Happy Case",
			accountID: "test",
			name:      "test",
		},
		{
			desc:      "Error Case",
			accountID: "test",
			name:      "test",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:      "empty accountID Case",
			accountID: "",
			name:      "test",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Count", mock.Anything, mock.Anything, mock.Anything).Return(int64(1), tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerSubAccountByName(context.Background(), tt.accountID, tt.name)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerSubAccountByName(%v)", tt.desc)
		})
	}
}

func TestStore_GetCustomerSubAccountByNameAndStatusD(t *testing.T) {
	scenarios := []struct {
		desc      string
		accountID string
		name      string
		status    string
		mockError error
		wantErr   bool
	}{
		{
			desc:      "Happy Case",
			accountID: "test",
			name:      "test",
			status:    "test",
		},
		{
			desc:      "Error Case",
			accountID: "test",
			name:      "test",
			status:    "test",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:      "empty accountID Case",
			accountID: "",
			name:      "test",
			status:    "test",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockCustomerAccountDAO := &MockICustomerAccountDAO{}
			mockCustomerAccountDAO.On("Count", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(int64(1), tt.mockError)
			store := &Store{
				db: &dbStore{
					CustomerAccountDao: mockCustomerAccountDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetCustomerSubAccountByNameAndStatus(context.Background(), tt.accountID, tt.name, tt.status)
			assert.Equalf(t, tt.wantErr, err != nil, "GetCustomerSubAccountByNameAndStatus(%v)", tt.desc)
		})
	}
}

func TestStore_CreatePocket(t *testing.T) {
	scenarios := []struct {
		desc      string
		input     *Pocket
		mockError error
		wantErr   bool
	}{
		{
			desc: "Happy Case",
			input: &Pocket{
				PocketID: "test",
			},
		},
		{
			desc: "Error Case",
			input: &Pocket{
				PocketID: "test",
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:    "empty input Case",
			input:   nil,
			wantErr: true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockPocketDAO := &MockIPocketDAO{}
			mockPocketDAO.On("Save", mock.Anything, mock.Anything).Return(tt.mockError)
			store := &Store{
				db: &dbStore{
					PocketDao: mockPocketDAO,
				},
				stats: statsd.NewNoop(),
			}
			err := store.CreatePocket(context.Background(), tt.input)
			assert.Equalf(t, tt.wantErr, err != nil, "CreatePocket(%v)", tt.desc)
		})
	}
}

func TestStore_CreatePocketTemplateAnswer(t *testing.T) {
	scenarios := []struct {
		desc      string
		input     []*PocketTemplateAnswer
		mockError error
		wantErr   bool
	}{
		{
			desc: "Happy Case",
			input: []*PocketTemplateAnswer{{
				PocketID: "test",
			}},
		},
		{
			desc: "Error Case",
			input: []*PocketTemplateAnswer{{
				PocketID: "test",
			}},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:    "empty input Case",
			input:   nil,
			wantErr: true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockPocketTemplateAnswerDAO := &MockIPocketTemplateAnswerDAO{}
			mockPocketTemplateAnswerDAO.On("SaveBatch", mock.Anything, mock.Anything).Return(tt.mockError)
			store := &Store{
				db: &dbStore{
					PocketTemplateAnswerDao: mockPocketTemplateAnswerDAO,
				},
				stats: statsd.NewNoop(),
			}
			err := store.CreatePocketTemplateAnswers(context.Background(), tt.input)
			assert.Equalf(t, tt.wantErr, err != nil, "CreatePocketTemplateAnswers(%v)", tt.desc)
		})
	}
}

func TestStore_GetRequestLog(t *testing.T) {
	scenarios := []struct {
		desc      string
		requestID string
		mockError error
		wantErr   bool
	}{
		{
			desc:      "Happy Case",
			requestID: "test",
		},
		{
			desc:      "Error Case",
			requestID: "test",
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:      "empty requestID Case",
			requestID: "",
			wantErr:   true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockRequestLogDAO := &MockIRequestLogDAO{}
			mockRequestLogDAO.On("Find", mock.Anything, mock.Anything).Return([]*RequestLog{
				{},
			}, tt.mockError)
			store := &Store{
				db: &dbStore{
					RequestLogDao: mockRequestLogDAO,
				},
				stats: statsd.NewNoop(),
			}
			_, err := store.GetRequestLog(context.Background(), tt.requestID)
			assert.Equalf(t, tt.wantErr, err != nil, "GetRequestLog(%v)", tt.desc)
		})
	}
}

func TestStore_CreateRequestLog(t *testing.T) {
	scenarios := []struct {
		desc      string
		input     *RequestLog
		mockError error
		wantErr   bool
	}{
		{
			desc: "Happy Case",
			input: &RequestLog{
				RequestID: "test",
			},
		},
		{
			desc: "Error Case",
			input: &RequestLog{
				RequestID: "test",
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:    "empty input Case",
			input:   nil,
			wantErr: true,
		},
		{
			desc: "empty requestID Case",
			input: &RequestLog{
				RequestID: "",
			},
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockRequestLogDAO := &MockIRequestLogDAO{}
			mockRequestLogDAO.On("Save", mock.Anything, mock.Anything).Return(tt.mockError)
			store := &Store{
				db: &dbStore{
					RequestLogDao: mockRequestLogDAO,
				},
				stats: statsd.NewNoop(),
			}
			err := store.CreateRequestLog(context.Background(), tt.input)
			assert.Equalf(t, tt.wantErr, err != nil, "CreateRequestLog(%v)", tt.desc)
		})
	}
}

func TestStore_UpdateRequestLog(t *testing.T) {
	scenarios := []struct {
		desc      string
		data      *RequestLog
		mockError error
		wantErr   bool
	}{
		{
			desc: "Happy Case",
			data: &RequestLog{
				RequestID: "test",
			},
		},
		{
			desc: "Error Case",
			data: &RequestLog{
				RequestID: "test",
			},
			mockError: fmt.Errorf("error"),
			wantErr:   true,
		},
		{
			desc:    "empty input Case",
			data:    nil,
			wantErr: true,
		},
	}
	for _, scenario := range scenarios {
		tt := scenario
		t.Run(tt.desc, func(t *testing.T) {
			mockRequestLogDAO := &MockIRequestLogDAO{}
			mockRequestLogDAO.On("Update", mock.Anything, mock.Anything).Return(tt.mockError)
			store := &Store{
				db: &dbStore{
					RequestLogDao: mockRequestLogDAO,
				},
				stats: statsd.NewNoop(),
			}
			err := store.UpdateRequestLog(context.Background(), tt.data)
			assert.Equalf(t, tt.wantErr, err != nil, "UpdateRequestLog(%v)", tt.desc)
		})
	}
}

// func TestStore_CreateCustomerAccountStatusHistory(t *testing.T) {
//	scenarios := []struct {
//		name    string
//		input   *CustomerAccountStatusHistory
//		wantErr bool
//	}{
//		{
//			name: "Test CreateCustomerAccountStatusHistory",
//			input: &CustomerAccountStatusHistory{
//				CustomerAccountID: "testID",
//			},
//			wantErr: false,
//		},
//	}
//	for _, scenario := range scenarios {
//		tt := scenario
//		t.run(tt.name, func(t *testing.T) {
//			mockCustomerAccountStatusHistoryDAO := &MockICustomerAccountStatusHistoryDAO{}
//			mockCustomerAccountStatusHistoryDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
//			store := &Store{
//				db: &dbStore{
//					CustomerAccountStatusHistoryDao: mockCustomerAccountStatusHistoryDAO,
//				},
//				stats: statsd.NewNoop(),
//			}
//			err := store.CreateCustomerAccountStatusHistory(context.Background(), tt.input)
//			assert.Equalf(t, tt.wantErr, err != nil, "CreateCustomerAccountStatusHistory(%v)", tt.input)
//		})
//	}
// }
