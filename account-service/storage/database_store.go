package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"reflect"
	"sync/atomic"

	"gitlab.myteksi.net/gophers/go/commons/data"

	"github.com/google/uuid"
	"gitlab.myteksi.net/bersama/core-banking/account-service/constants"
	apiErr "gitlab.myteksi.net/bersama/core-banking/account-service/utils/error"
	commonctx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// DatabaseStore ...
//
//go:generate mockery --name=DatabaseStore --case=underscore --inpackage
type DatabaseStore interface {
	GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error)
	CreateCustomerAccount(ctx context.Context, customerAccount *CustomerAccount) error
	UpdateCustomerAccount(ctx context.Context, preData CustomerAccount, newData CustomerAccount) (CustomerAccount, error)
	GetCustomerAccount(ctx context.Context, accountID string) (*CustomerAccount, error)
	GetCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error)
	GetCustomerAccountByCifStatusProductVariant(ctx context.Context, cif string, statuses, productVariantCodes []string, pagination Pagination) ([]*CustomerAccount, error)
	GetCustomerAccountByFilters(ctx context.Context, filters []data.Condition) ([]*CustomerAccount, error)
	CreateCustomerAccountStatusHistory(ctx context.Context, customerAccountStatusHistory *CustomerAccountStatusHistory) error
	GetCustomerAccountStatusHistory(ctx context.Context, filters []data.Condition) ([]*CustomerAccountStatusHistory, error)
	GetCustomerSubAccounts(ctx context.Context, accountID string) ([]*CustomerAccount, error)
	GetSubAccountsCount(ctx context.Context, accountID string) (int64, error)
	GetPockets(ctx context.Context, pocketIDs []string) ([]*Pocket, error)
	CreatePocket(ctx context.Context, pocket *Pocket) error
	CreatePocketTemplateAnswers(ctx context.Context, pocketTemplateAnswers []*PocketTemplateAnswer) error
	GetRequestLog(ctx context.Context, requestID string) (*RequestLog, error)
	CreateRequestLog(ctx context.Context, data *RequestLog) error
	UpdateRequestLog(ctx context.Context, data *RequestLog) error
	ExecuteTransaction(ctx context.Context, config *data.MysqlConfig, stmts []*TransactionStmt) error
	GetCustomerSubAccountByName(ctx context.Context, parentAccountID string, name string) (int64, error)
	GetCustomerAccountPending(ctx context.Context, cif string, productVariantID string) (*CustomerAccount, error)
	GetCustomerSubAccountByNameAndStatus(ctx context.Context, parentAccountID string, name, status string) (int64, error)
	GetCustomerAccountsByVariantID(ctx context.Context, parentAccountID string, variantID string) ([]*CustomerAccount, error)
	GetNonClosedCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error)
	GetAccountPendingActionsByAccountID(ctx context.Context, accountID string) ([]*PendingAction, error)
	GetAccountPendingActionByName(ctx context.Context, accountID, name string) (*PendingAction, error)
	UpdatePendingAction(ctx context.Context, action *PendingAction) error
	GetCustomerAccountsCountByFilters(ctx context.Context, filters []data.Condition) (int64, error)
}
type (
	// dbStore ...
	dbStore struct {
		CustomerAccountDao              ICustomerAccountDAO
		CustomerAccountStatusHistoryDao ICustomerAccountStatusHistoryDAO
		RequestLogDao                   IRequestLogDAO
		PocketDao                       IPocketDAO
		PocketTemplateAnswerDao         IPocketTemplateAnswerDAO
		PendingActionDao                IPendingActionDAO
		SchedulerLock                   ISchedulerLockDAO
	}
	// Store ...
	Store struct {
		db    *dbStore
		stats statsd.Client
	}

	// Pagination ...
	Pagination struct {
		Limit  int
		Offset int
	}
)

// New ...
func New(customerAccountDB ICustomerAccountDAO, customerAccountStatusHistoryDB ICustomerAccountStatusHistoryDAO, requestLogDB IRequestLogDAO, pocketDB IPocketDAO, pocketTemplateAnswerDB IPocketTemplateAnswerDAO, pendingActionDB IPendingActionDAO, schedulerLockDB ISchedulerLockDAO, stats statsd.Client) *Store {
	return &Store{
		db: &dbStore{
			CustomerAccountDao:              customerAccountDB,
			CustomerAccountStatusHistoryDao: customerAccountStatusHistoryDB,
			RequestLogDao:                   requestLogDB,
			PocketDao:                       pocketDB,
			PocketTemplateAnswerDao:         pocketTemplateAnswerDB,
			PendingActionDao:                pendingActionDB,
			SchedulerLock:                   schedulerLockDB,
		},
		stats: stats,
	}
}

// GetCustomerAccountsCountByFilters ...
func (s *Store) GetCustomerAccountsCountByFilters(ctx context.Context, filters []data.Condition) (int64, error) {
	if len(filters) == 0 {
		return 0, fmt.Errorf("filters cannot be nil")
	}
	count, err := s.db.CustomerAccountDao.Count(ctx, filters...)
	if err != nil {
		msg := fmt.Sprintf("Error while fetching count of customer_accounts: %s", err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagCountCustomerAccountByFilters, msg)
		return 0, err
	}
	return count, nil
}

// GetDatabaseHandle ...
func (s *Store) GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error) {
	atomic.AddInt64(&config.PendingCalls, 1)
	defer atomic.AddInt64(&config.PendingCalls, -1)
	dbs, err := getDatabase(config)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.GetDatabaseHandleLogTag, fmt.Sprintf("GetDatabaseHandle error: %s", err.Error()))
		return nil, err
	}
	return dbs, nil
}

// CreateCustomerAccount ...
func (s *Store) CreateCustomerAccount(ctx context.Context, customerAccount *CustomerAccount) error {
	return s.createCustomerAccount(ctx, customerAccount)
}

func (s *Store) createCustomerAccount(ctx context.Context, customerAccount *CustomerAccount) error {
	if err := s.db.CustomerAccountDao.Save(ctx, customerAccount); err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagCreateCustomerAccount, fmt.Sprintf("error saving customer account entry to DB: %s", err.Error()))
		return err
	}
	return nil
}

// UpdateCustomerAccount ...
func (s *Store) UpdateCustomerAccount(ctx context.Context, preData CustomerAccount, newData CustomerAccount) (CustomerAccount, error) {
	return s.updateCustomerAccount(ctx, preData, newData)
}

func (s *Store) updateCustomerAccount(ctx context.Context, preData CustomerAccount, newData CustomerAccount) (CustomerAccount, error) {
	if err := s.db.CustomerAccountDao.UpdateEntity(ctx, &preData, &newData); err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagUpdateCustomerAccount, fmt.Sprintf("error updating customer account entry to DB: %s", err.Error()))
		return CustomerAccount{}, err
	}
	return newData, nil
}

// GetCustomerAccount ...
func (s *Store) GetCustomerAccount(ctx context.Context, accountID string) (*CustomerAccount, error) {
	if accountID == "" {
		return nil, errors.New("accountID cannot be blank")
	}
	customerAccount, err := s.getCustomerAccount(ctx, accountID)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccount, fmt.Sprintf("Failed to get customer account by accountID: %s", err.Error()))
	}
	return customerAccount, err
}

// GetCustomerSubAccounts ...
func (s *Store) GetCustomerSubAccounts(ctx context.Context, accountID string) ([]*CustomerAccount, error) {
	if accountID == "" {
		return nil, errors.New("accountID cannot be blank")
	}
	return s.getCustomerSubAccounts(ctx, accountID)
}

// GetCustomerAccountByFilters ...
func (s *Store) GetCustomerAccountByFilters(ctx context.Context, filters []data.Condition) ([]*CustomerAccount, error) {
	if len(filters) == 0 {
		return nil, fmt.Errorf("filters cannot be nil")
	}
	customerAccounts, err := s.getCustomerAccountByFilters(ctx, filters)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByFilters, fmt.Sprintf("Failed to get customer account by filters : %s", err))
		return []*CustomerAccount{}, err
	}
	return customerAccounts, err
}

// CreateCustomerAccountStatusHistory ...
func (s *Store) CreateCustomerAccountStatusHistory(ctx context.Context, customerAccountStatusHistory *CustomerAccountStatusHistory) error {
	return s.createCustomerAccountStatusHistory(ctx, customerAccountStatusHistory)
}

func (s *Store) createCustomerAccountStatusHistory(ctx context.Context, customerAccountStatusHistory *CustomerAccountStatusHistory) error {
	if err := s.db.CustomerAccountStatusHistoryDao.Save(ctx, customerAccountStatusHistory); err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagCreateCustomerAccountStatusHistory, fmt.Sprintf("error saving customer account status history to DB: %s", err.Error()))
		return err
	}
	return nil
}

// GetCustomerAccountStatusHistory ...
func (s *Store) GetCustomerAccountStatusHistory(ctx context.Context, filters []data.Condition) ([]*CustomerAccountStatusHistory, error) {
	if len(filters) == 0 {
		return nil, fmt.Errorf("filters cannot be nil")
	}
	customerAccountStatusHistory, err := s.getCustomerAccountStatusHistory(ctx, filters)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountStatusHistoryByFilters, fmt.Sprintf("Failed to get customer account status history : %s", err))
		return []*CustomerAccountStatusHistory{}, err
	}
	return customerAccountStatusHistory, err
}

func (s *Store) getCustomerAccountStatusHistory(ctx context.Context, filters []data.Condition) ([]*CustomerAccountStatusHistory, error) {
	customerAccountStatusHistory, err := s.db.CustomerAccountStatusHistoryDao.FindOnSlave(ctx, filters...)
	if err != nil {
		return nil, err
	}
	return customerAccountStatusHistory, nil
}

func (s *Store) getCustomerAccountByFilters(ctx context.Context, filters []data.Condition) ([]*CustomerAccount, error) {
	return s.db.CustomerAccountDao.Find(ctx, filters...)
}

// GetPockets ...
func (s *Store) GetPockets(ctx context.Context, pocketIDs []string) ([]*Pocket, error) {
	if pocketIDs == nil {
		return nil, errors.New("PocketIDs array cannot be nil")
	}
	return s.getSavingsPockets(ctx, pocketIDs)
}

// GetSubAccountsCount ...
func (s *Store) GetSubAccountsCount(ctx context.Context, accountID string) (int64, error) {
	if accountID == "" {
		return 0, errors.New("accountID cannot be nil")
	}
	return s.getSubAccountsCount(ctx, accountID)
}

// GetCustomerSubAccountByName ...
func (s *Store) GetCustomerSubAccountByName(ctx context.Context, parentAccountID string, name string) (int64, error) {
	if parentAccountID == "" {
		return 0, errors.New("accountID cannot be nil")
	}
	return s.getCustomerSubAccountByName(ctx, parentAccountID, name)
}

// GetCustomerSubAccountByNameAndStatus ...
func (s *Store) GetCustomerSubAccountByNameAndStatus(ctx context.Context, parentAccountID string, name, status string) (int64, error) {
	if parentAccountID == "" {
		return 0, errors.New("accountID cannot be nil")
	}
	return s.getCustomerSubAccountByNameAndStatus(ctx, parentAccountID, name, status)
}

// GetCustomerAccountByCif ...
func (s *Store) GetCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	if cif == "" {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByCif, "cif cannot be blank")
		return []*CustomerAccount{}, fmt.Errorf("cif cannot be blank")
	}
	customerAccount, err := s.getCustomerAccountByCif(ctx, cif)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByCif, fmt.Sprintf("Failed to get customer account by CIF: %s", err.Error()))
		return []*CustomerAccount{}, err
	}
	return customerAccount, err
}

// GetCustomerAccountByCifStatusProductVariant ...
func (s *Store) GetCustomerAccountByCifStatusProductVariant(ctx context.Context, cif string, statuses, productVariantCodes []string, pagination Pagination) ([]*CustomerAccount, error) {
	if cif == "" {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByCifStatusProductVariant, "cif cannot be blank")
		return []*CustomerAccount{}, fmt.Errorf("cif cannot be blank")
	}
	var conditions []data.Condition
	conditions = append(conditions, data.EqualTo("CifNumber", cif))
	conditions = append(conditions, data.AscendingOrder("ID"))
	if len(statuses) > 0 {
		conditions = append(conditions, data.ContainedIn("CurrentStatus", convertToInterface(statuses)...))
	}
	if len(productVariantCodes) > 0 {
		conditions = append(conditions, data.ContainedIn("ProductVariantID", convertToInterface(productVariantCodes)...))
	}
	if pagination.Limit > 0 {
		conditions = append(conditions, data.Limit(pagination.Limit))
	}
	if pagination.Offset > 0 {
		conditions = append(conditions, data.Skip(pagination.Offset))
	}
	anyRes, err := FindReplicaConditionally(
		ctx, s.db.CustomerAccountDao, conditions, ReplicaByCallerIdentity(commonctx.GetClientIdentity(ctx)),
	)
	if err != nil {
		return nil, err
	}
	customerAccounts, _ := anyRes.([]*CustomerAccount)
	return customerAccounts, nil
}

// GetNonClosedCustomerAccountByCif ...
func (s *Store) GetNonClosedCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	if cif == "" {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByCif, "cif cannot be blank")
		return []*CustomerAccount{}, fmt.Errorf("cif cannot be blank")
	}
	customerAccount, err := s.getNonClosedCustomerAccountByCif(ctx, cif)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByCif, fmt.Sprintf("Failed to get customer account by CIF: %s", err.Error()))
		return []*CustomerAccount{}, err
	}
	return customerAccount, err
}

// GetCustomerAccountsByVariantID ...
func (s *Store) GetCustomerAccountsByVariantID(ctx context.Context, accountID string, variantID string) ([]*CustomerAccount, error) {
	if accountID == "" {
		return nil, errors.New("accountID cannot be blank")
	}
	return s.getCustomerAccountsByVariantID(ctx, accountID, variantID)
}

// GetRequestLog ...
func (s *Store) GetRequestLog(ctx context.Context, requestID string) (*RequestLog, error) {
	if requestID == "" {
		slog.FromContext(ctx).Warn(constants.LogTagGetRequestLog, "requestID cannot be blank")
		return nil, errors.New("requestID cannot be blank")
	}
	requestLog, err := s.getRequestLog(ctx, requestID)
	if err != nil && !reflect.DeepEqual(data.ErrNoData, err) {
		slog.FromContext(ctx).Warn(constants.LogTagGetRequestLog, fmt.Sprintf("Failed to get idempotency key: %s", err.Error()))
	}
	return requestLog, err
}

// CreateRequestLog ...
func (s *Store) CreateRequestLog(ctx context.Context, data *RequestLog) error {
	if data == nil {
		return errors.New("nil input")
	}
	if data.RequestID == "" {
		data.RequestID = uuid.New().String()
	}
	err := s.createRequestLog(ctx, data)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagCreateRequestLog, fmt.Sprintf("Failed to create idempotency key: %s", err.Error()))
	}
	return err
}

// UpdateRequestLog ...
func (s *Store) UpdateRequestLog(ctx context.Context, data *RequestLog) error {
	if data == nil {
		return errors.New("nil input")
	}
	err := s.updateRequestLog(ctx, data)
	if err != nil {
		return apiErr.BuildDataUpdateInDBErrResponse(ctx, constants.LogTagUpdateRequestLog, fmt.Sprintf("Failed to update idempotency key: %s", err.Error()))
	}
	return err
}

// GetAccountPendingActionsByAccountID returns collection of pending actions by account ID
func (s *Store) GetAccountPendingActionsByAccountID(ctx context.Context, accountID string) ([]*PendingAction, error) {
	pendingActions, err := s.db.PendingActionDao.Find(ctx, data.EqualTo("AccountID", accountID))
	if err != nil {
		if err == data.ErrNoData {
			return []*PendingAction{}, nil
		}
		msg := fmt.Sprintf("Error while fetching account pending action with account ID %s from DB: %s", accountID, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetAccountPendingActions, msg)
		return nil, err
	}
	return pendingActions, nil
}

// GetAccountPendingActionByName returns a pending action based on action name
func (s *Store) GetAccountPendingActionByName(ctx context.Context, accountID, name string) (*PendingAction, error) {
	pendingActions, err := s.db.PendingActionDao.Find(ctx, data.EqualTo("AccountID", accountID), data.EqualTo("Name", name))
	if err != nil {
		if err == data.ErrNoData {
			return nil, nil
		}
		msg := fmt.Sprintf("Error while fetching account pending action with account ID %s and name %s from DB: %s", accountID, name, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetAccountPendingActionByName, msg)
		return nil, err
	}
	return pendingActions[0], nil
}

// UpdatePendingAction ...
func (s *Store) UpdatePendingAction(ctx context.Context, data *PendingAction) error {
	if data == nil {
		return errors.New("nil input")
	}
	err := s.db.PendingActionDao.Update(ctx, data)
	if err != nil {
		return apiErr.BuildDataUpdateInDBErrResponse(ctx, constants.LogTagUpdateRequestLog, fmt.Sprintf("Failed to update idempotency key: %s", err.Error()))
	}
	return err
}

// updateRequestLog ...
func (s *Store) updateRequestLog(ctx context.Context, data *RequestLog) error {
	return s.updateRequestLogInDB(ctx, data)
}

// updateRequestLogInDB ...
func (s *Store) updateRequestLogInDB(ctx context.Context, requestLog *RequestLog) error {
	return s.db.RequestLogDao.Update(ctx, requestLog)
}

// ExecuteTransaction ...
func (s *Store) ExecuteTransaction(ctx context.Context, config *data.MysqlConfig, stmts []*TransactionStmt) error {
	if stmts == nil {
		return nil
	}
	var err error
	db, err := GetDatabaseHandle(config)
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagExecuteTransaction, fmt.Sprintf("Failed to get a database connection: %s", err.Error()))
		return err
	}

	err = WithTransaction(ctx, db, func(tx Transaction) error {
		_, err = RunTransactionPipeline(ctx, tx, stmts...)
		return err
	})

	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagExecuteTransaction, fmt.Sprintf("Failed to execute database transaction: %s", err.Error()))
	}
	return err
}

func (s *Store) getCustomerAccount(ctx context.Context, accountID string) (*CustomerAccount, error) {
	return s.getCustomerAccountFromDB(ctx, accountID)
}

// GetCustomerAccountPending ...
func (s *Store) GetCustomerAccountPending(ctx context.Context, cif string, productVariantID string) (*CustomerAccount, error) {
	return s.getCustomerAccountPendingFromDB(ctx, cif, productVariantID)
}

func (s *Store) getCustomerSubAccounts(ctx context.Context, accountID string) ([]*CustomerAccount, error) {
	return s.getCustomerSubAccountsFromDB(ctx, accountID)
}

func (s *Store) getSavingsPockets(ctx context.Context, pocketIDs []string) ([]*Pocket, error) {
	return s.getSavingsPocketsFromDB(ctx, pocketIDs)
}

func (s *Store) getSubAccountsCount(ctx context.Context, accountID string) (int64, error) {
	return s.getSubAccountsCountFromDB(ctx, accountID)
}

func (s *Store) getCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	return s.getCustomerAccountByCifFromDB(ctx, cif)
}

func (s *Store) getCustomerAccountsByVariantID(ctx context.Context, accountID string, variantID string) ([]*CustomerAccount, error) {
	return s.getCustomerAccountsByVariantIDFromDB(ctx, accountID, variantID)
}

func (s *Store) getNonClosedCustomerAccountByCif(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	return s.getNonClosedCustomerAccountByCifFromDB(ctx, cif)
}

func (s *Store) getNonClosedCustomerAccountByCifFromDB(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	customerAccounts, err := s.db.CustomerAccountDao.Find(ctx, data.EqualTo("CifNumber", cif), data.NotEqualTo("CurrentStatus", api.AccountStatus_CLOSED))
	if err != nil {
		msg := fmt.Sprintf("Error while fetching customer account with cif %s: %s", cif, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetNonClosedCustomerAccountByCif, msg)
		return nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.LogTagGetNonClosedCustomerAccountByCif, msg, err)
	}
	return customerAccounts, nil
}

func (s *Store) getCustomerAccountFromDB(ctx context.Context, accountID string) (*CustomerAccount, error) {
	customerAccounts, err := s.db.CustomerAccountDao.Find(ctx, data.EqualTo("AccountID", accountID))
	if err != nil {
		msg := fmt.Sprintf("Error while fetching customer account with ID %s from DB: %s", accountID, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccount, msg)
		return nil, err
	}
	return customerAccounts[0], nil
}

// ExecuteTransaction getCustomerAccountPendingFromDB ...
func (s *Store) getCustomerAccountPendingFromDB(ctx context.Context, cif string, productVariantID string) (*CustomerAccount, error) {
	customerAccounts, err := s.db.CustomerAccountDao.Find(ctx, []data.Condition{data.EqualTo("CifNumber", cif), data.EqualTo("CurrentStatus", api.AccountStatus_PENDING), data.EqualTo("ProductVariantID", productVariantID)}...)
	if err != nil {
		msg := fmt.Sprintf("Error while fetching customer account with ID %s and status %s from DB: %s", cif, api.AccountStatus_PENDING, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccount, msg)
		return nil, err
	}
	return customerAccounts[0], nil
}

func (s *Store) getCustomerSubAccountsFromDB(ctx context.Context, id string) ([]*CustomerAccount, error) {
	customerAccounts, err := s.db.CustomerAccountDao.Find(ctx, []data.Condition{data.EqualTo("ParentAccountID", id), data.NotEqualTo("CurrentStatus", api.AccountStatus_CLOSED)}...)

	if err != nil {
		msg := fmt.Sprintf("Error while fetching customer sub accounts of ID %s from DB: %s", id, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerSubAccounts, msg)
		return nil, err
	}
	return customerAccounts, err
}

func (s *Store) getSavingsPocketsFromDB(ctx context.Context, ids []string) ([]*Pocket, error) {
	savingsPockets, err := s.db.PocketDao.Find(ctx, []data.Condition{data.ContainedIn("PocketID", convertToInterface(ids)...)}...)
	if err != nil {
		return nil, err
	}
	return savingsPockets, err
}

// CreatePocket ...
func (s *Store) CreatePocket(ctx context.Context, pocket *Pocket) error {
	if pocket == nil {
		return errors.New("nil input")
	}
	return s.createPocket(ctx, pocket)
}

func (s *Store) createPocket(ctx context.Context, pocket *Pocket) error {
	if err := s.db.PocketDao.Save(ctx, pocket); err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagCreatePocket, fmt.Sprintf("error saving pocket entry to DB: %s", err.Error()))
		return err
	}
	return nil
}

// CreatePocketTemplateAnswers ...
func (s *Store) CreatePocketTemplateAnswers(ctx context.Context, pocketTemplateAnswers []*PocketTemplateAnswer) error {
	if pocketTemplateAnswers == nil {
		return errors.New("nil input")
	}
	return s.createPocketTemplateAnswers(ctx, pocketTemplateAnswers)
}

func (s *Store) createPocketTemplateAnswers(ctx context.Context, pocketTemplateAnswers []*PocketTemplateAnswer) error {
	if err := s.db.PocketTemplateAnswerDao.SaveBatch(ctx, pocketTemplateAnswers); err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagCreatePocketTemplateAnswers, fmt.Sprintf("error saving pocket template answers to DB: %s", err.Error()))
		return err
	}
	return nil
}

func (s *Store) getSubAccountsCountFromDB(ctx context.Context, accountID string) (int64, error) {
	count, err := s.db.CustomerAccountDao.Count(ctx, []data.Condition{data.EqualTo("ParentAccountID", accountID)}...)
	if err != nil {
		msg := fmt.Sprintf("Error while fetching sub accounts count with accountID %s from DB: %s", accountID, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetSubAccountsCount, msg)
		return 0, err
	}
	return count, nil
}

func (s *Store) getCustomerAccountByCifFromDB(ctx context.Context, cif string) ([]*CustomerAccount, error) {
	anyRes, err := FindReplicaConditionally(
		ctx, s.db.CustomerAccountDao, []data.Condition{data.EqualTo("CifNumber", cif)}, ReplicaByCallerIdentity(commonctx.GetClientIdentity(ctx)),
	)
	if err != nil && !errors.Is(err, data.ErrNoData) {
		msg := fmt.Sprintf("Error while fetching customer account with cif %s: %s", cif, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountByCif, msg)
		return nil, apiErr.BuildLoadDataFromDBErrResponse(ctx, constants.LogTagGetCustomerAccountByCif, msg, err)
	}
	customerAccounts, _ := anyRes.([]*CustomerAccount)
	return customerAccounts, nil
}

func (s *Store) getCustomerAccountsByVariantIDFromDB(ctx context.Context, id string, variantID string) ([]*CustomerAccount, error) {
	customerAccounts, err := s.db.CustomerAccountDao.Find(ctx, []data.Condition{data.EqualTo("ParentAccountID", id), data.EqualTo("ProductVariantID", variantID)}...)

	if err != nil {
		msg := fmt.Sprintf("Error while fetching customer accounts by variantID %s from DB: %s", id, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetCustomerAccountsByVariantID, msg)
		return nil, err
	}
	return customerAccounts, err
}

func (s *Store) getRequestLog(ctx context.Context, requestID string) (*RequestLog, error) {
	return s.getRequestLogFromDB(ctx, requestID)
}

func (s *Store) createRequestLog(ctx context.Context, data *RequestLog) error {
	return s.createRequestLogInDB(ctx, data)
}

func (s *Store) getRequestLogFromDB(ctx context.Context, requestID string) (*RequestLog, error) {
	requestLogs, err := s.db.RequestLogDao.Find(ctx, data.EqualTo("RequestID", requestID))
	if err != nil {
		slog.FromContext(ctx).Warn(constants.LogTagGetRequestLog, fmt.Sprintf("Failed to get idempotency key: %s", err.Error()))
		return nil, err
	}
	return requestLogs[0], nil
}

func (s *Store) createRequestLogInDB(ctx context.Context, requestLog *RequestLog) error {
	return s.db.RequestLogDao.Save(ctx, requestLog)
}

func (s *Store) getCustomerSubAccountByName(ctx context.Context, parentAccountID string, name string) (int64, error) {
	count, err := s.db.CustomerAccountDao.Count(ctx, []data.Condition{data.EqualTo("ParentAccountID", parentAccountID), data.EqualTo("Name", name)}...)
	if err != nil {
		msg := fmt.Sprintf("Error while fetching sub accounts with parent accountID %s and name %s from DB: %s", parentAccountID, name, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetSubAccountsByName, msg)
		return 0, err
	}
	return count, nil
}

func (s *Store) getCustomerSubAccountByNameAndStatus(ctx context.Context, parentAccountID string, name string, status string) (int64, error) {
	count, err := s.db.CustomerAccountDao.Count(ctx, []data.Condition{data.EqualTo("ParentAccountID", parentAccountID), data.EqualTo("Name", name), data.EqualTo("CurrentStatus", status)}...)
	if err != nil {
		msg := fmt.Sprintf("Error while fetching sub accounts with parent accountID %s and name %s from DB: %s", parentAccountID, name, err.Error())
		slog.FromContext(ctx).Warn(constants.LogTagGetSubAccountsByName, msg)
		return 0, err
	}
	return count, nil
}

// convertToInterface converts a slice of string to slice of interface{}
func convertToInterface(arr []string) []interface{} {
	interfaceArr := make([]interface{}, len(arr))
	for i, elem := range arr {
		interfaceArr[i] = elem
	}
	return interfaceArr
}
